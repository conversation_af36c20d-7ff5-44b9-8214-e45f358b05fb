#!/bin/bash
# Скрипт для исправления прав доступа к файлам локализации

echo "🔧 Fixing file permissions for locales directory..."

# Проверяем, существует ли директория locales
if [ ! -d "locales" ]; then
    echo "❌ Directory 'locales' not found!"
    exit 1
fi

# Устанавливаем права доступа для директории locales
# UID/GID 1000 соответствует пользователю appuser в контейнере
sudo chown -R 1000:1000 locales/

# Устанавливаем права на чтение и запись
chmod -R 755 locales/

# Создаем .mo файлы если их нет
find locales -name "*.po" -exec dirname {} \; | sort -u | while read dir; do
    mo_file="$dir/bot.mo"
    if [ ! -f "$mo_file" ]; then
        echo "📝 Creating empty .mo file: $mo_file"
        touch "$mo_file"
        chown 1000:1000 "$mo_file"
        chmod 644 "$mo_file"
    fi
done

echo "✅ Permissions fixed successfully!"
echo "📋 Current permissions:"
ls -la locales/*/LC_MESSAGES/
