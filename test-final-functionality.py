#!/usr/bin/env python3
"""
Финальный тест всех функций VPN бота.
"""

import asyncio
import sys
import os

# Добавляем путь к корню проекта
sys.path.insert(0, '/app')

from db.base import get_session
from repositories.user_repository import SQLUserRepository
from services.base import get_service_container
from services.user_service import UserService
from services.subscription_service import SubscriptionService
import glv

async def test_user_creation():
    """Тестирует создание пользователя."""
    print("🔄 Testing user creation...")
    
    test_tg_id = 999888777
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Создаем пользователя
            user = await user_repo.create_user(test_tg_id)
            await session.commit()
            
            print(f"✅ User created: ID={user.id}, VPN_ID={user.vpn_id}")
            return True
            
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return False

async def test_test_subscription():
    """Тестирует создание тестовой подписки."""
    print("🔄 Testing test subscription creation...")
    
    test_tg_id = 999888777
    
    try:
        container = get_service_container()
        user_service = container.get_service(UserService)
        
        # Проверяем, что тестовая подписка не использована
        has_test = await user_service.check_test_subscription_used(test_tg_id)
        print(f"Test subscription used: {has_test}")
        
        # Создаем тестовую подписку
        result = await user_service.create_test_subscription(test_tg_id, days=5)
        
        if result:
            print("✅ Test subscription created successfully")
            
            # Проверяем, что теперь тестовая подписка использована
            has_test_after = await user_service.check_test_subscription_used(test_tg_id)
            print(f"Test subscription used after creation: {has_test_after}")
            
            return True
        else:
            print("❌ Failed to create test subscription")
            return False
            
    except Exception as e:
        print(f"❌ Error testing subscription: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_admin_config():
    """Тестирует конфигурацию администратора."""
    print("🔄 Testing admin configuration...")
    
    try:
        admin_id = glv.config.get('ADMIN_USER_ID')
        test_period = glv.config.get('TEST_PERIOD')
        period_limit = glv.config.get('PERIOD_LIMIT')
        
        print(f"Admin ID: {admin_id}")
        print(f"Test period enabled: {test_period}")
        print(f"Period limit: {period_limit} hours")
        
        if admin_id:
            print("✅ Admin configuration is set")
        else:
            print("⚠️ Admin ID not configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking admin config: {e}")
        return False

async def test_services():
    """Тестирует инициализацию сервисов."""
    print("🔄 Testing services initialization...")
    
    try:
        container = get_service_container()
        
        # Тестируем UserService
        user_service = container.get_service(UserService)
        print("✅ UserService initialized")
        
        # Тестируем SubscriptionService
        subscription_service = container.get_service(SubscriptionService)
        print("✅ SubscriptionService initialized")
        
        # Получаем статистику
        stats = await user_service.get_user_stats()
        print(f"📊 User stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing services: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_operations():
    """Тестирует операции с базой данных."""
    print("🔄 Testing database operations...")
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Получаем всех пользователей
            users = await user_repo.get_all_users(limit=5)
            print(f"📊 Total users in database: {len(users)}")
            
            # Получаем статистику
            stats = await user_repo.get_user_stats()
            print(f"📊 Database stats: {stats}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing database: {e}")
        return False

async def test_marzban_api():
    """Тестирует доступность Marzban API."""
    print("🔄 Testing Marzban API...")
    
    try:
        from utils import marzban_api
        
        # Проверяем токен
        await marzban_api.ensure_token()
        print("✅ Marzban API token obtained")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Marzban API: {e}")
        return False

async def main():
    """Главная функция."""
    print("🚀 Starting final functionality tests...")
    print("=" * 50)
    
    tests = [
        ("Database Operations", test_database_operations),
        ("Services Initialization", test_services),
        ("User Creation", test_user_creation),
        ("Test Subscription", test_test_subscription),
        ("Admin Configuration", test_admin_config),
        ("Marzban API", test_marzban_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 FINAL RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Bot is ready for use.")
        print("\n📱 Ready to test:")
        print("• /start command")
        print("• 5 days free button")
        print("• Admin commands (/admin)")
        print("• Menu navigation")
        return 0
    else:
        print(f"⚠️ {total - passed} tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
