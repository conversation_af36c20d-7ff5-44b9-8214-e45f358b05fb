#!/usr/bin/env python3
"""
Простой скрипт для тестирования подключения к базе данных.
Используется для отладки проблем с подключением.
"""

import os
import sys

def test_connection():
    """Тестирует подключение к базе данных"""
    
    # Получаем параметры подключения
    db_host = os.getenv('DB_ADDRESS', 'localhost')
    db_port = int(os.getenv('DB_PORT', '3306'))
    db_user = os.getenv('DB_USER', 'root')
    db_pass = os.getenv('DB_PASS', '')
    db_name = os.getenv('DB_NAME', 'test')
    
    print(f"Testing connection to:")
    print(f"  Host: {db_host}")
    print(f"  Port: {db_port}")
    print(f"  User: {db_user}")
    print(f"  Database: {db_name}")
    print()
    
    try:
        import pymysql  # type: ignore
        
        connection = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_pass,
            database=db_name,
            connect_timeout=5
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result and result[0] == 1:
                print("✅ Connection successful!")
                return True
        
        connection.close()
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False
    
    return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
