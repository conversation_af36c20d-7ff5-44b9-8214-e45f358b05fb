#!/usr/bin/env python3
"""
Простой тест создания пользователя с коммитом в базу данных.
"""

import asyncio
import sys
import os

# Добавляем путь к корню проекта
sys.path.insert(0, '/app')

from db.base import get_session
from repositories.user_repository import SQLUserRepository

async def test_user_creation_with_commit():
    """Тестирует создание пользователя с коммитом в базу данных."""
    print("🔄 Testing user creation with database commit...")
    
    test_tg_id = 555666777  # Новый тестовый ID
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Проверяем, что пользователя нет
            existing_user = await user_repo.get_by_telegram_id(test_tg_id)
            if existing_user:
                print(f"ℹ️ User {test_tg_id} already exists")
                return True
            
            # Создаем пользователя
            print(f"🔄 Creating user {test_tg_id}...")
            new_user = await user_repo.create_user(test_tg_id)
            
            # Коммитим изменения
            await session.commit()
            
            print(f"✅ User created and committed:")
            print(f"  - ID: {new_user.id}")
            print(f"  - Telegram ID: {new_user.tg_id}")
            print(f"  - VPN ID: {new_user.vpn_id}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        import traceback
        traceback.print_exc()
        return False

async def verify_user_in_database():
    """Проверяет, что пользователь действительно сохранился в базе данных."""
    print("\n🔄 Verifying user exists in database...")
    
    test_tg_id = 555666777
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Получаем пользователя из базы
            user = await user_repo.get_by_telegram_id(test_tg_id)
            
            if user:
                print(f"✅ User {test_tg_id} found in database:")
                print(f"  - Database ID: {user.id}")
                print(f"  - VPN ID: {user.vpn_id}")
                print(f"  - Created at: {user.created_at}")
                return True
            else:
                print(f"❌ User {test_tg_id} not found in database")
                return False
                
    except Exception as e:
        print(f"❌ Error verifying user: {e}")
        return False

async def test_user_stats():
    """Тестирует получение статистики пользователей."""
    print("\n🔄 Testing user statistics...")
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Получаем статистику
            stats = await user_repo.get_user_stats()
            print(f"📊 User statistics: {stats}")
            
            # Получаем всех пользователей
            users = await user_repo.get_all_users()
            print(f"👥 Total users in repository: {len(users)}")
            
            for user in users:
                print(f"  - User {user.tg_id}: VPN ID {user.vpn_id}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error getting user stats: {e}")
        return False

async def main():
    """Главная функция."""
    print("🚀 Starting user creation tests...")
    
    # Тест создания пользователя
    if not await test_user_creation_with_commit():
        return 1
    
    # Проверка, что пользователь сохранился
    if not await verify_user_in_database():
        return 1
    
    # Тест статистики
    if not await test_user_stats():
        return 1
    
    print("\n🎉 All user creation tests passed!")
    print("✅ User creation functionality works correctly")
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
