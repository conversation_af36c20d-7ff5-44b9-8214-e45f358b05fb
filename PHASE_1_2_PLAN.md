# 🔐 Phase 1.2: Завершение критических исправлений

## 📊 Текущий статус
**✅ Phase 1.1 ЗАВЕРШЕНА:**
- Централизованная обработка ошибок ✅
- Структурированное JSON логирование ✅  
- Health checks и мониторинг ✅
- Контейнер запускается стабильно ✅

**⚠️ Что требует доработки:**
- Метрики Prometheus (небольшая проблема с коллектором)
- Отсутствие шифрования чувствительных данных
- Отсутствие валидации входящих данных
- Нет алертов для критических событий

## 🎯 Цели Phase 1.2

### 1. **Исправление метрик Prometheus** ⚡ (30 минут)
**Проблема**: `'NoneType' object has no attribute 'collect'` в metrics endpoint
**Решение**: Исправить инициализацию registry в utils/metrics.py

### 2. **Безопасность данных** 🔐 (2 дня)
**Проблема**: Пароли и токены хранятся в открытом виде
**Решение**: Внедрить Fernet шифрование для чувствительных данных

### 3. **Валидация входящих данных** 🛡️ (2 дня)
**Проблема**: Отсутствие валидации webhook данных и пользовательского ввода
**Решение**: Внедрить Pydantic модели для всех входящих данных

### 4. **Настройка алертов** 📊 (1 день)
**Проблема**: Нет уведомлений о критических событиях
**Решение**: Настроить алерты для Prometheus метрик

## 📋 Детальный план выполнения

### Задача 1: Исправление метрик Prometheus
**Время**: 30 минут | **Приоритет**: Критический

#### Шаги:
1. Исправить инициализацию registry в `utils/metrics.py`
2. Добавить проверку доступности Prometheus
3. Протестировать endpoint `/metrics`

#### Ожидаемый результат:
- Метрики Prometheus работают корректно
- Endpoint возвращает валидные метрики

### Задача 2: Шифрование чувствительных данных
**Время**: 2 дня | **Приоритет**: Критический

#### Шаги:
1. **День 1**: Создание системы шифрования
   - Добавить Fernet в requirements.txt
   - Создать модуль `utils/encryption.py`
   - Реализовать функции шифрования/дешифрования
   - Добавить управление ключами шифрования

2. **День 2**: Интеграция с существующим кодом
   - Обновить модели БД для зашифрованных полей
   - Создать миграции для шифрования существующих данных
   - Обновить код работы с паролями и токенами
   - Протестировать шифрование/дешифрование

#### Файлы для изменения:
- `requirements.txt` - добавить cryptography
- `bot/utils/encryption.py` - новый модуль
- `bot/db/models.py` - обновить модели
- `bot/db/methods.py` - обновить методы работы с БД
- `alembic/versions/` - новая миграция

#### Ожидаемый результат:
- Все пароли и токены зашифрованы в БД
- Система автоматически шифрует/дешифрует данные
- Ключи шифрования управляются безопасно

### Задача 3: Валидация входящих данных
**Время**: 2 дня | **Приоритет**: Высокий

#### Шаги:
1. **День 1**: Создание Pydantic моделей
   - Добавить pydantic в requirements.txt
   - Создать модуль `bot/schemas/` с моделями валидации
   - Реализовать схемы для webhook данных
   - Создать схемы для пользовательского ввода

2. **День 2**: Интеграция валидации
   - Обновить webhook handlers для использования схем
   - Добавить валидацию в обработчики команд
   - Создать middleware для валидации
   - Добавить обработку ошибок валидации

#### Файлы для создания/изменения:
- `requirements.txt` - добавить pydantic
- `bot/schemas/__init__.py` - новый модуль
- `bot/schemas/webhooks.py` - схемы webhook
- `bot/schemas/user_input.py` - схемы пользовательского ввода
- `bot/middlewares/validation.py` - middleware валидации
- `bot/handlers/` - обновить handlers

#### Ожидаемый результат:
- Все входящие данные валидируются
- Некорректные данные отклоняются с понятными ошибками
- Система защищена от инъекций и некорректного ввода

### Задача 4: Настройка алертов
**Время**: 1 день | **Приоритет**: Средний

#### Шаги:
1. Создать конфигурацию алертов для Prometheus
2. Добавить правила алертов в `monitoring/alerts.yml`
3. Настроить уведомления (email/Telegram)
4. Протестировать срабатывание алертов

#### Файлы для создания:
- `monitoring/alerts.yml` - правила алертов
- `monitoring/prometheus.yml` - конфигурация Prometheus
- `monitoring/docker-compose.monitoring.yml` - мониторинг стек

#### Ожидаемый результат:
- Алерты срабатывают при критических событиях
- Уведомления доставляются своевременно

## 🔧 Технические детали

### Шифрование данных
```python
# utils/encryption.py
from cryptography.fernet import Fernet
import os
import base64

class DataEncryption:
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def encrypt(self, data: str) -> str:
        """Шифрует строку и возвращает base64 строку"""
        encrypted = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """Дешифрует base64 строку"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = self.cipher.decrypt(encrypted_bytes)
        return decrypted.decode()
```

### Pydantic схемы
```python
# schemas/webhooks.py
from pydantic import BaseModel, validator
from typing import Optional

class YooKassaWebhook(BaseModel):
    type: str
    event: str
    object: dict
    
    @validator('type')
    def validate_type(cls, v):
        if v != 'notification':
            raise ValueError('Invalid notification type')
        return v
```

### Алерты Prometheus
```yaml
# monitoring/alerts.yml
groups:
  - name: vpn-bot-alerts
    rules:
      - alert: BotDown
        expr: up{job="vpn-bot"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "VPN Bot is down"
          
      - alert: HighErrorRate
        expr: rate(vpn_bot_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
```

## 📊 Критерии успеха

### Метрики Prometheus
- [ ] Endpoint `/metrics` возвращает валидные метрики
- [ ] Нет ошибок в логах при запросе метрик
- [ ] Prometheus может собирать метрики

### Шифрование данных
- [ ] Все пароли в БД зашифрованы
- [ ] Токены API зашифрованы
- [ ] Система работает с зашифрованными данными
- [ ] Миграция существующих данных выполнена

### Валидация данных
- [ ] Webhook данные валидируются
- [ ] Пользовательский ввод валидируется
- [ ] Некорректные данные отклоняются
- [ ] Ошибки валидации логируются

### Алерты
- [ ] Алерты настроены в Prometheus
- [ ] Уведомления доставляются
- [ ] Алерты срабатывают при проблемах

## 🚀 Следующие шаги после Phase 1.2

После завершения Phase 1.2 переходим к:
1. **Phase 1.3**: Рефакторинг архитектуры (Repository Pattern, Service Layer)
2. **Phase 2**: Система выбора стран и управления нодами
3. **Phase 3**: Расширенные уведомления и управление платежами

**Общий прогресс**: Phase 1.1 ✅ → Phase 1.2 🔄 → Phase 1.3 → Phase 2 → Phase 3
