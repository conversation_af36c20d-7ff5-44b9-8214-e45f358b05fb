"""Add notification system tables

Revision ID: 0a5510ae2872
Revises: 9ae075a30549
Create Date: 2025-06-04 14:27:56.681405

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0a5510ae2872'
down_revision = '9ae075a30549'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Создание таблицы notification_templates
    op.create_table(
        'notification_templates',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('name', sa.String(100), nullable=False, comment='Название шаблона'),
        sa.Column('description', sa.Text, comment='Описание шаблона'),
        sa.Column('notification_type', sa.Enum(
            'subscription_expiring_7_days', 'subscription_expiring_3_days', 'subscription_expiring_1_day',
            'subscription_expired', 'node_unavailable', 'node_restored', 'node_maintenance',
            'payment_failed', 'payment_success', 'subscription_renewed', 'refund_processed',
            'welcome_message', 'account_suspended', 'account_restored', 'security_alert',
            'special_offer', 'discount_available', 'new_feature_announcement',
            name='notificationtype'
        ), nullable=False),
        sa.Column('priority', sa.Enum(
            'low', 'normal', 'high', 'urgent',
            name='notificationpriority'
        ), nullable=False, default='normal'),
        sa.Column('channel', sa.Enum(
            'telegram', 'email', 'push', 'sms',
            name='notificationchannel'
        ), nullable=False, default='telegram'),
        sa.Column('subject_template', sa.Text, comment='Шаблон заголовка (для email)'),
        sa.Column('body_template', sa.Text, nullable=False, comment='Шаблон тела сообщения'),
        sa.Column('variables', sa.JSON, comment='Список доступных переменных с описанием'),
        sa.Column('default_values', sa.JSON, comment='Значения переменных по умолчанию'),
        sa.Column('ab_test_variant', sa.Enum(
            'control', 'variant_b', 'variant_c',
            name='abtestvariant'
        ), default='control'),
        sa.Column('ab_test_weight', sa.Integer, default=100, comment='Вес варианта в A/B тесте (0-100)'),
        sa.Column('delay_seconds', sa.Integer, default=0, comment='Задержка перед отправкой в секундах'),
        sa.Column('retry_attempts', sa.Integer, default=3, comment='Количество попыток отправки'),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by', sa.String(100), comment='Кто создал шаблон'),
    )
    
    # Создание индексов для notification_templates
    op.create_index('ix_notification_templates_notification_type', 'notification_templates', ['notification_type'])
    op.create_index('ix_notification_templates_is_active', 'notification_templates', ['is_active'])
    op.create_index('ix_notification_templates_type_active', 'notification_templates', ['notification_type', 'is_active'])
    op.create_index('ix_notification_templates_ab_test', 'notification_templates', ['notification_type', 'ab_test_variant', 'is_active'])

    # Создание таблицы notification_schedules
    op.create_table(
        'notification_schedules',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, sa.ForeignKey('vpnusers.id'), nullable=False),
        sa.Column('template_id', sa.String(32), sa.ForeignKey('notification_templates.id'), nullable=False),
        sa.Column('scheduled_at', sa.DateTime, nullable=False),
        sa.Column('notification_type', sa.Enum(
            'subscription_expiring_7_days', 'subscription_expiring_3_days', 'subscription_expiring_1_day',
            'subscription_expired', 'node_unavailable', 'node_restored', 'node_maintenance',
            'payment_failed', 'payment_success', 'subscription_renewed', 'refund_processed',
            'welcome_message', 'account_suspended', 'account_restored', 'security_alert',
            'special_offer', 'discount_available', 'new_feature_announcement',
            name='notificationtype'
        ), nullable=False),
        sa.Column('priority', sa.Enum(
            'low', 'normal', 'high', 'urgent',
            name='notificationpriority'
        ), nullable=False, default='normal'),
        sa.Column('context_data', sa.JSON, comment='Данные для подстановки в шаблон'),
        sa.Column('status', sa.Enum(
            'pending', 'processing', 'sent', 'failed', 'cancelled', 'expired',
            name='notificationstatus'
        ), default='pending'),
        sa.Column('attempts', sa.Integer, default=0, comment='Количество попыток отправки'),
        sa.Column('max_attempts', sa.Integer, default=3, comment='Максимальное количество попыток'),
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('sent_at', sa.DateTime, comment='Время отправки'),
        sa.Column('failed_at', sa.DateTime, comment='Время последней неудачной попытки'),
        sa.Column('error_message', sa.Text, comment='Сообщение об ошибке'),
        sa.Column('error_code', sa.String(50), comment='Код ошибки'),
        sa.Column('expires_at', sa.DateTime, comment='Время истечения актуальности уведомления'),
    )
    
    # Создание индексов для notification_schedules
    op.create_index('ix_notification_schedules_user_id', 'notification_schedules', ['user_id'])
    op.create_index('ix_notification_schedules_scheduled_at', 'notification_schedules', ['scheduled_at'])
    op.create_index('ix_notification_schedules_notification_type', 'notification_schedules', ['notification_type'])
    op.create_index('ix_notification_schedules_status', 'notification_schedules', ['status'])
    op.create_index('ix_notification_schedules_user_type', 'notification_schedules', ['user_id', 'notification_type'])
    op.create_index('ix_notification_schedules_scheduled_status', 'notification_schedules', ['scheduled_at', 'status'])
    op.create_index('ix_notification_schedules_pending', 'notification_schedules', ['status', 'scheduled_at'])

    # Создание таблицы notification_logs
    op.create_table(
        'notification_logs',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, sa.ForeignKey('vpnusers.id'), nullable=False),
        sa.Column('template_id', sa.String(32), sa.ForeignKey('notification_templates.id'), nullable=False),
        sa.Column('schedule_id', sa.String(32), sa.ForeignKey('notification_schedules.id'), nullable=True),
        sa.Column('notification_type', sa.Enum(
            'subscription_expiring_7_days', 'subscription_expiring_3_days', 'subscription_expiring_1_day',
            'subscription_expired', 'node_unavailable', 'node_restored', 'node_maintenance',
            'payment_failed', 'payment_success', 'subscription_renewed', 'refund_processed',
            'welcome_message', 'account_suspended', 'account_restored', 'security_alert',
            'special_offer', 'discount_available', 'new_feature_announcement',
            name='notificationtype'
        ), nullable=False),
        sa.Column('channel', sa.Enum(
            'telegram', 'email', 'push', 'sms',
            name='notificationchannel'
        ), nullable=False),
        sa.Column('priority', sa.Enum(
            'low', 'normal', 'high', 'urgent',
            name='notificationpriority'
        ), nullable=False),
        sa.Column('delivery_status', sa.Enum(
            'delivered', 'failed', 'blocked_by_user', 'rate_limited', 'invalid_recipient', 'timeout',
            name='deliverystatus'
        ), nullable=False),
        sa.Column('sent_at', sa.DateTime, default=sa.func.now()),
        sa.Column('delivered_at', sa.DateTime, comment='Время подтверждения доставки'),
        sa.Column('telegram_message_id', sa.Integer, comment='ID сообщения в Telegram'),
        sa.Column('telegram_chat_id', sa.String(50), comment='ID чата в Telegram'),
        sa.Column('rendered_subject', sa.Text, comment='Отрендеренный заголовок'),
        sa.Column('rendered_body', sa.Text, comment='Отрендеренное тело сообщения'),
        sa.Column('context_data', sa.JSON, comment='Контекстные данные на момент отправки'),
        sa.Column('ab_test_variant', sa.Enum(
            'control', 'variant_b', 'variant_c',
            name='abtestvariant'
        ), comment='Вариант A/B теста'),
        sa.Column('processing_time_ms', sa.Integer, comment='Время обработки в миллисекундах'),
        sa.Column('message_size_bytes', sa.Integer, comment='Размер сообщения в байтах'),
        sa.Column('error_message', sa.Text, comment='Сообщение об ошибке доставки'),
        sa.Column('error_code', sa.String(50), comment='Код ошибки доставки'),
    )
    
    # Создание индексов для notification_logs
    op.create_index('ix_notification_logs_user_id', 'notification_logs', ['user_id'])
    op.create_index('ix_notification_logs_notification_type', 'notification_logs', ['notification_type'])
    op.create_index('ix_notification_logs_delivery_status', 'notification_logs', ['delivery_status'])
    op.create_index('ix_notification_logs_sent_at', 'notification_logs', ['sent_at'])
    op.create_index('ix_notification_logs_user_sent', 'notification_logs', ['user_id', 'sent_at'])
    op.create_index('ix_notification_logs_type_status', 'notification_logs', ['notification_type', 'delivery_status'])
    op.create_index('ix_notification_logs_channel_sent', 'notification_logs', ['channel', 'sent_at'])
    op.create_index('ix_notification_logs_ab_test', 'notification_logs', ['notification_type', 'ab_test_variant', 'sent_at'])

    # Создание таблицы notification_preferences
    op.create_table(
        'notification_preferences',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, sa.ForeignKey('vpnusers.id'), nullable=False),
        sa.Column('notification_type', sa.Enum(
            'subscription_expiring_7_days', 'subscription_expiring_3_days', 'subscription_expiring_1_day',
            'subscription_expired', 'node_unavailable', 'node_restored', 'node_maintenance',
            'payment_failed', 'payment_success', 'subscription_renewed', 'refund_processed',
            'welcome_message', 'account_suspended', 'account_restored', 'security_alert',
            'special_offer', 'discount_available', 'new_feature_announcement',
            name='notificationtype'
        ), nullable=False),
        sa.Column('is_enabled', sa.Boolean, default=True, comment='Включены ли уведомления этого типа'),
        sa.Column('preferred_channel', sa.Enum(
            'telegram', 'email', 'push', 'sms',
            name='notificationchannel'
        ), default='telegram'),
        sa.Column('quiet_hours_start', sa.Integer, comment='Начало тихих часов (час в UTC)'),
        sa.Column('quiet_hours_end', sa.Integer, comment='Конец тихих часов (час в UTC)'),
        sa.Column('timezone', sa.String(50), comment='Часовой пояс пользователя'),
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, default=sa.func.now(), onupdate=sa.func.now()),
    )
    
    # Создание индексов для notification_preferences
    op.create_index('ix_notification_preferences_user_id', 'notification_preferences', ['user_id'])
    op.create_index('ix_notification_preferences_user_type', 'notification_preferences', ['user_id', 'notification_type'])
    op.create_index('uq_notification_preferences_user_type', 'notification_preferences', ['user_id', 'notification_type'], unique=True)


def downgrade() -> None:
    # Удаление таблиц в обратном порядке
    op.drop_table('notification_preferences')
    op.drop_table('notification_logs')
    op.drop_table('notification_schedules')
    op.drop_table('notification_templates')
    
    # Удаление enum типов
    op.execute("DROP TYPE IF EXISTS notificationtype")
    op.execute("DROP TYPE IF EXISTS notificationpriority")
    op.execute("DROP TYPE IF EXISTS notificationchannel")
    op.execute("DROP TYPE IF EXISTS notificationstatus")
    op.execute("DROP TYPE IF EXISTS deliverystatus")
    op.execute("DROP TYPE IF EXISTS abtestvariant")
