#!/usr/bin/env python3
"""
Тест команды /start для Telegram бота.
"""

import asyncio
import os
import sys

# Добавляем путь к корню проекта
sys.path.insert(0, '/app')

from db.base import get_session
from repositories.user_repository import SQLUserRepository
from sqlalchemy import text

async def test_user_operations():
    """Тестирует операции с пользователями."""
    print("🔄 Testing user operations...")
    
    test_tg_id = 123456789  # Тестовый Telegram ID
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Тест 1: Проверка существования пользователя
            print(f"🔍 Test 1: Checking if user {test_tg_id} exists...")
            existing_user = await user_repo.get_by_telegram_id(test_tg_id)
            
            if existing_user:
                print(f"✅ User {test_tg_id} already exists: {existing_user.vpn_id}")
            else:
                print(f"ℹ️ User {test_tg_id} does not exist")
                
                # Тест 2: Создание пользователя
                print(f"🔄 Test 2: Creating user {test_tg_id}...")
                new_user = await user_repo.create_user(test_tg_id)
                print(f"✅ User created successfully: ID={new_user.id}, VPN_ID={new_user.vpn_id}")
            
            # Тест 3: Получение пользователя после создания
            print(f"🔄 Test 3: Getting user {test_tg_id} after creation...")
            user = await user_repo.get_by_telegram_id(test_tg_id)
            
            if user:
                print(f"✅ User retrieved successfully:")
                print(f"  - ID: {user.id}")
                print(f"  - Telegram ID: {user.tg_id}")
                print(f"  - VPN ID: {user.vpn_id}")
                print(f"  - Username: {user.username}")
                print(f"  - Subscription URL: {user.subscription_url}")
                print(f"  - Expire Date: {user.expire_date}")
            else:
                print(f"❌ Failed to retrieve user {test_tg_id}")
                return False
            
            # Тест 4: Проверка структуры таблицы
            print(f"🔄 Test 4: Checking table structure...")
            result = await session.execute(text("DESCRIBE vpnusers"))
            columns = result.fetchall()
            
            print(f"✅ Table structure:")
            for column in columns:
                print(f"  - {column[0]}: {column[1]} ({column[2]})")
            
            print(f"\n🎉 All user operation tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Error in user operations test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_connection():
    """Тестирует подключение к базе данных."""
    print("🔄 Testing database connection...")
    
    try:
        async with get_session() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
            print("✅ Database connection successful")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def main():
    """Главная функция."""
    print("🚀 Starting /start command tests...")
    
    # Тест подключения к базе данных
    if not await test_database_connection():
        return 1
    
    # Тест операций с пользователями
    if not await test_user_operations():
        return 1
    
    print("\n🎉 All tests passed! The /start command should work now.")
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
