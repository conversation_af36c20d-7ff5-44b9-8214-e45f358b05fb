#!/usr/bin/env python3
"""
Тесты для Repository Pattern.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from repositories.user_repository import SQLUserRepository
from repositories.payment_repository import SQLPaymentRepository
from repositories.base import UnitOfWork
from db.models import VPNUsers, YPayments, CPayments


class TestUserRepository:
    """Тесты для UserRepository."""
    
    @pytest.fixture
    def mock_session(self):
        """Мок сессии SQLAlchemy."""
        session = AsyncMock()
        return session
    
    @pytest.fixture
    def user_repository(self, mock_session):
        """Экземпляр UserRepository с мок сессией."""
        return SQLUserRepository(mock_session)
    
    @pytest.mark.asyncio
    async def test_get_by_telegram_id_found(self, user_repository, mock_session):
        """Тест получения пользователя по Telegram ID - пользователь найден."""
        # Arrange
        tg_id = 123456789
        expected_user = VPNUsers(id=1, tg_id=tg_id, vpn_id="test_vpn_id", test=False)
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = expected_user
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await user_repository.get_by_telegram_id(tg_id)
        
        # Assert
        assert result == expected_user
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_by_telegram_id_not_found(self, user_repository, mock_session):
        """Тест получения пользователя по Telegram ID - пользователь не найден."""
        # Arrange
        tg_id = 123456789
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await user_repository.get_by_telegram_id(tg_id)
        
        # Assert
        assert result is None
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_user_new(self, user_repository, mock_session):
        """Тест создания нового пользователя."""
        # Arrange
        tg_id = 123456789
        
        # Мокаем что пользователь не существует
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Мокаем создание пользователя
        new_user = VPNUsers(id=1, tg_id=tg_id, vpn_id="generated_vpn_id", test=False)
        mock_session.add = MagicMock()
        mock_session.flush = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Мокаем создание через базовый репозиторий
        user_repository.create = AsyncMock(return_value=new_user)
        
        # Act
        result = await user_repository.create_user(tg_id)
        
        # Assert
        assert result == new_user
        user_repository.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_user_existing(self, user_repository, mock_session):
        """Тест создания пользователя - пользователь уже существует."""
        # Arrange
        tg_id = 123456789
        existing_user = VPNUsers(id=1, tg_id=tg_id, vpn_id="existing_vpn_id", test=False)
        
        # Мокаем что пользователь уже существует
        user_repository.get_by_telegram_id = AsyncMock(return_value=existing_user)
        
        # Act
        result = await user_repository.create_user(tg_id)
        
        # Assert
        assert result == existing_user
        user_repository.get_by_telegram_id.assert_called_once_with(tg_id)
    
    @pytest.mark.asyncio
    async def test_has_test_subscription_true(self, user_repository, mock_session):
        """Тест проверки тестовой подписки - подписка использована."""
        # Arrange
        tg_id = 123456789
        user_with_test = VPNUsers(id=1, tg_id=tg_id, vpn_id="test_vpn_id", test=True)
        
        user_repository.get_by_telegram_id = AsyncMock(return_value=user_with_test)
        
        # Act
        result = await user_repository.has_test_subscription(tg_id)
        
        # Assert
        assert result is True
        user_repository.get_by_telegram_id.assert_called_once_with(tg_id)
    
    @pytest.mark.asyncio
    async def test_has_test_subscription_false(self, user_repository, mock_session):
        """Тест проверки тестовой подписки - подписка не использована."""
        # Arrange
        tg_id = 123456789
        user_without_test = VPNUsers(id=1, tg_id=tg_id, vpn_id="test_vpn_id", test=False)
        
        user_repository.get_by_telegram_id = AsyncMock(return_value=user_without_test)
        
        # Act
        result = await user_repository.has_test_subscription(tg_id)
        
        # Assert
        assert result is False
        user_repository.get_by_telegram_id.assert_called_once_with(tg_id)
    
    @pytest.mark.asyncio
    async def test_has_test_subscription_user_not_found(self, user_repository, mock_session):
        """Тест проверки тестовой подписки - пользователь не найден."""
        # Arrange
        tg_id = 123456789
        
        user_repository.get_by_telegram_id = AsyncMock(return_value=None)
        
        # Act
        result = await user_repository.has_test_subscription(tg_id)
        
        # Assert
        assert result is False
        user_repository.get_by_telegram_id.assert_called_once_with(tg_id)


class TestPaymentRepository:
    """Тесты для PaymentRepository."""
    
    @pytest.fixture
    def mock_session(self):
        """Мок сессии SQLAlchemy."""
        session = AsyncMock()
        return session
    
    @pytest.fixture
    def payment_repository(self, mock_session):
        """Экземпляр PaymentRepository с мок сессией."""
        return SQLPaymentRepository(mock_session)
    
    @pytest.mark.asyncio
    async def test_create_yookassa_payment(self, payment_repository, mock_session):
        """Тест создания платежа YooKassa."""
        # Arrange
        tg_id = 123456789
        payment_id = "test_payment_id"
        callback = "test_callback"
        chat_id = 987654321
        lang_code = "ru"
        
        expected_payment = YPayments(
            id=1,
            tg_id=tg_id,
            payment_id=payment_id,
            callback=callback,
            chat_id=chat_id,
            lang=lang_code
        )
        
        payment_repository.yookassa_repo.create = AsyncMock(return_value=expected_payment)
        
        # Act
        result = await payment_repository.create_yookassa_payment(
            tg_id, payment_id, callback, chat_id, lang_code
        )
        
        # Assert
        assert result == expected_payment
        payment_repository.yookassa_repo.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_yookassa_payment_found(self, payment_repository, mock_session):
        """Тест получения платежа YooKassa - платеж найден."""
        # Arrange
        payment_id = "test_payment_id"
        expected_payment = YPayments(id=1, payment_id=payment_id)
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = expected_payment
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await payment_repository.get_yookassa_payment(payment_id)
        
        # Assert
        assert result == expected_payment
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_yookassa_payment_not_found(self, payment_repository, mock_session):
        """Тест получения платежа YooKassa - платеж не найден."""
        # Arrange
        payment_id = "nonexistent_payment_id"
        
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await payment_repository.get_yookassa_payment(payment_id)
        
        # Assert
        assert result is None
        mock_session.execute.assert_called_once()


class TestUnitOfWork:
    """Тесты для Unit of Work."""
    
    @pytest.mark.asyncio
    async def test_unit_of_work_success(self):
        """Тест успешного выполнения Unit of Work."""
        # Arrange
        mock_session = AsyncMock()
        
        # Мокаем get_session
        import repositories.base
        original_get_session = repositories.base.get_session
        
        async def mock_get_session():
            yield mock_session
        
        repositories.base.get_session = mock_get_session
        
        try:
            # Act
            async with UnitOfWork() as uow:
                # Симулируем успешную операцию
                pass
            
            # Assert
            mock_session.commit.assert_called_once()
            mock_session.close.assert_called_once()
            mock_session.rollback.assert_not_called()
        
        finally:
            # Восстанавливаем оригинальную функцию
            repositories.base.get_session = original_get_session
    
    @pytest.mark.asyncio
    async def test_unit_of_work_exception(self):
        """Тест Unit of Work при возникновении исключения."""
        # Arrange
        mock_session = AsyncMock()
        
        # Мокаем get_session
        import repositories.base
        original_get_session = repositories.base.get_session
        
        async def mock_get_session():
            yield mock_session
        
        repositories.base.get_session = mock_get_session
        
        try:
            # Act & Assert
            with pytest.raises(ValueError):
                async with UnitOfWork() as uow:
                    # Симулируем ошибку
                    raise ValueError("Test error")
            
            # Assert
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()
            mock_session.commit.assert_not_called()
        
        finally:
            # Восстанавливаем оригинальную функцию
            repositories.base.get_session = original_get_session


if __name__ == "__main__":
    # Запуск тестов
    pytest.main([__file__, "-v"])
