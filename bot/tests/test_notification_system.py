"""
Тесты для системы уведомлений.

Этот модуль содержит unit-тесты для проверки работы
системы уведомлений, включая шаблоны, планировщик и отправку.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

# Импорты для тестирования
from services.template_service import TemplateService, TemplateRenderError
from services.advanced_notification_service import AdvancedNotificationService, NotificationResult
from services.notification_scheduler import NotificationScheduler
from repositories.notification_repository import NotificationRepositoryInterface
from db.models_notifications import NotificationTemplate, NotificationSchedule
from schemas.notification_enums import (
    NotificationType, NotificationPriority, NotificationChannel,
    NotificationStatus, DeliveryStatus, ABTestVariant
)


class MockNotificationRepository(NotificationRepositoryInterface):
    """Mock-реализация репозитория для тестов."""
    
    def __init__(self):
        self.templates = {}
        self.schedules = {}
        self.logs = []
        self.preferences = {}
    
    async def create_template(self, template):
        self.templates[template.id] = template
        return template
    
    async def get_template_by_id(self, template_id):
        return self.templates.get(template_id)
    
    async def get_template_by_type(self, notification_type):
        for template in self.templates.values():
            if template.notification_type == notification_type and template.is_active:
                return template
        return None
    
    async def get_templates_by_type(self, notification_type):
        return [t for t in self.templates.values() 
                if t.notification_type == notification_type and t.is_active]
    
    async def create_schedule(self, schedule):
        self.schedules[schedule.id] = schedule
        return schedule
    
    async def get_pending_notifications(self, limit=100):
        pending = [s for s in self.schedules.values() 
                  if s.status == NotificationStatus.PENDING]
        return pending[:limit]
    
    async def update_schedule_status(self, schedule_id, status, error_message=None):
        if schedule_id in self.schedules:
            self.schedules[schedule_id].status = status
            if error_message:
                self.schedules[schedule_id].error_message = error_message
            return True
        return False
    
    async def cancel_scheduled_notifications(self, user_id, notification_type):
        cancelled = 0
        for schedule in self.schedules.values():
            if (schedule.user_id == user_id and 
                schedule.notification_type == notification_type and
                schedule.status == NotificationStatus.PENDING):
                schedule.status = NotificationStatus.CANCELLED
                cancelled += 1
        return cancelled
    
    async def log_notification(self, log):
        self.logs.append(log)
        return log
    
    async def get_user_preferences(self, user_id):
        return self.preferences.get(user_id, [])
    
    async def update_user_preference(self, user_id, notification_type, is_enabled):
        # Упрощенная реализация
        return Mock(user_id=user_id, notification_type=notification_type, is_enabled=is_enabled)


@pytest.fixture
def mock_repo():
    """Фикстура для mock-репозитория."""
    return MockNotificationRepository()


@pytest.fixture
def template_service():
    """Фикстура для сервиса шаблонов."""
    return TemplateService()


@pytest.fixture
def notification_service(mock_repo, template_service):
    """Фикстура для сервиса уведомлений."""
    return AdvancedNotificationService(mock_repo, template_service)


@pytest.fixture
def sample_template():
    """Фикстура для примера шаблона."""
    return NotificationTemplate(
        id="test_template_1",
        name="Test Template",
        notification_type=NotificationType.SUBSCRIPTION_EXPIRING_1_DAY,
        priority=NotificationPriority.HIGH,
        channel=NotificationChannel.TELEGRAM,
        subject_template="Subscription Expiring",
        body_template="Your subscription expires in {{ days_left }} days!",
        variables={"days_left": {"type": "integer", "required": True}},
        is_active=True,
        ab_test_variant=ABTestVariant.CONTROL,
        ab_test_weight=100
    )


class TestTemplateService:
    """Тесты для сервиса шаблонов."""
    
    @pytest.mark.asyncio
    async def test_render_simple_template(self, template_service, sample_template):
        """Тест рендеринга простого шаблона."""
        context = {"days_left": 3}
        
        result = await template_service.render_template(sample_template, context)
        
        assert result.body == "Your subscription expires in 3 days!"
        assert result.subject == "Subscription Expiring"
        assert result.template_id == "test_template_1"
        assert "days_left" in result.variables_used
    
    @pytest.mark.asyncio
    async def test_render_template_with_filters(self, template_service):
        """Тест рендеринга шаблона с фильтрами."""
        template = NotificationTemplate(
            id="test_filter",
            name="Filter Test",
            notification_type=NotificationType.PAYMENT_SUCCESS,
            priority=NotificationPriority.NORMAL,
            channel=NotificationChannel.TELEGRAM,
            body_template="Amount: {{ amount|currency }} on {{ date|datetime('%d.%m.%Y') }}",
            is_active=True
        )
        
        context = {
            "amount": 100.50,
            "date": datetime(2024, 1, 15, 10, 30)
        }
        
        result = await template_service.render_template(template, context)
        
        assert "100.50 ₽" in result.body
        assert "15.01.2024" in result.body
    
    @pytest.mark.asyncio
    async def test_template_validation(self, template_service):
        """Тест валидации шаблонов."""
        # Валидный шаблон
        result = await template_service.validate_template(
            "Test Subject",
            "Hello {{ name }}!"
        )
        
        assert result['is_valid'] is True
        assert "name" in result['variables']
        
        # Невалидный шаблон
        result = await template_service.validate_template(
            None,
            "Hello {{ name"  # Незакрытая скобка
        )
        
        assert result['is_valid'] is False
        assert len(result['errors']) > 0
    
    def test_custom_filters(self, template_service):
        """Тест кастомных фильтров."""
        filters = template_service.get_available_filters()
        
        assert 'datetime' in filters
        assert 'currency' in filters
        assert 'pluralize' in filters
        assert 'truncate' in filters


class TestAdvancedNotificationService:
    """Тесты для расширенного сервиса уведомлений."""
    
    @pytest.mark.asyncio
    async def test_schedule_expiry_notifications(self, notification_service, mock_repo, sample_template):
        """Тест планирования уведомлений об истечении подписки."""
        # Добавляем шаблон в репозиторий
        await mock_repo.create_template(sample_template)
        
        user_id = 123
        expires_at = datetime.utcnow() + timedelta(days=10)
        subscription_data = {"plan": "premium", "price": "100"}
        
        await notification_service.schedule_subscription_expiry_notifications(
            user_id, expires_at, subscription_data
        )
        
        # Проверяем, что уведомления запланированы
        pending = await mock_repo.get_pending_notifications()
        assert len(pending) > 0
        
        # Проверяем, что уведомление на 1 день запланировано
        day_1_notification = next(
            (n for n in pending if n.notification_type == NotificationType.SUBSCRIPTION_EXPIRING_1_DAY),
            None
        )
        assert day_1_notification is not None
        assert day_1_notification.user_id == user_id
    
    @pytest.mark.asyncio
    async def test_send_immediate_notification(self, notification_service, mock_repo, sample_template):
        """Тест отправки немедленного уведомления."""
        # Добавляем шаблон в репозиторий
        await mock_repo.create_template(sample_template)
        
        user_id = 123
        context_data = {"days_left": 1}
        
        result = await notification_service.send_immediate_notification(
            user_id,
            NotificationType.SUBSCRIPTION_EXPIRING_1_DAY,
            context_data
        )
        
        assert result.success is True
        assert result.message_id is not None
        assert result.delivery_status == DeliveryStatus.DELIVERED
    
    @pytest.mark.asyncio
    async def test_cancel_user_notifications(self, notification_service, mock_repo, sample_template):
        """Тест отмены уведомлений пользователя."""
        # Добавляем шаблон и планируем уведомления
        await mock_repo.create_template(sample_template)
        
        user_id = 123
        expires_at = datetime.utcnow() + timedelta(days=10)
        
        await notification_service.schedule_subscription_expiry_notifications(
            user_id, expires_at, {}
        )
        
        # Отменяем уведомления
        cancelled_count = await notification_service.cancel_user_notifications(
            user_id, NotificationType.SUBSCRIPTION_EXPIRING_1_DAY
        )
        
        assert cancelled_count > 0
        
        # Проверяем, что уведомления отменены
        pending = await mock_repo.get_pending_notifications()
        day_1_pending = [n for n in pending 
                        if n.notification_type == NotificationType.SUBSCRIPTION_EXPIRING_1_DAY]
        assert len(day_1_pending) == 0


class TestNotificationScheduler:
    """Тесты для планировщика уведомлений."""
    
    @pytest.mark.asyncio
    async def test_scheduler_lifecycle(self, notification_service):
        """Тест жизненного цикла планировщика."""
        scheduler = NotificationScheduler(
            notification_service=notification_service,
            check_interval_seconds=1,
            batch_size=10
        )
        
        # Проверяем начальное состояние
        assert scheduler.is_running is False
        
        # Запускаем планировщик
        task = await scheduler.start()
        assert scheduler.is_running is True
        assert task is not None
        
        # Ждем немного
        await asyncio.sleep(0.1)
        
        # Останавливаем планировщик
        await scheduler.stop()
        assert scheduler.is_running is False
    
    def test_scheduler_stats(self, notification_service):
        """Тест статистики планировщика."""
        scheduler = NotificationScheduler(
            notification_service=notification_service,
            check_interval_seconds=60,
            batch_size=50
        )
        
        stats = scheduler.get_stats()
        
        assert 'total_processed' in stats
        assert 'total_sent' in stats
        assert 'total_failed' in stats
        assert 'is_running' in stats
        assert stats['check_interval'] == 60
        assert stats['batch_size'] == 50
    
    @pytest.mark.asyncio
    async def test_scheduler_context_manager(self, notification_service):
        """Тест контекстного менеджера планировщика."""
        scheduler = NotificationScheduler(
            notification_service=notification_service,
            check_interval_seconds=1
        )
        
        async with scheduler.running_context():
            assert scheduler.is_running is True
        
        assert scheduler.is_running is False


# Интеграционные тесты
class TestNotificationSystemIntegration:
    """Интеграционные тесты системы уведомлений."""
    
    @pytest.mark.asyncio
    async def test_full_notification_flow(self, mock_repo, template_service):
        """Тест полного потока уведомлений."""
        # Создаем сервисы
        notification_service = AdvancedNotificationService(mock_repo, template_service)
        
        # Создаем шаблон
        template = NotificationTemplate(
            id="integration_test",
            name="Integration Test",
            notification_type=NotificationType.WELCOME_MESSAGE,
            priority=NotificationPriority.NORMAL,
            channel=NotificationChannel.TELEGRAM,
            body_template="Welcome {{ user_name }}! Your account is ready.",
            variables={"user_name": {"type": "string", "required": True}},
            is_active=True
        )
        
        await mock_repo.create_template(template)
        
        # Отправляем уведомление
        result = await notification_service.send_immediate_notification(
            user_id=456,
            notification_type=NotificationType.WELCOME_MESSAGE,
            context_data={"user_name": "John Doe"}
        )
        
        # Проверяем результат
        assert result.success is True
        assert len(mock_repo.logs) > 0
        
        # Проверяем лог
        log = mock_repo.logs[0]
        assert log.user_id == 456
        assert log.notification_type == NotificationType.WELCOME_MESSAGE


if __name__ == "__main__":
    # Запуск тестов
    pytest.main([__file__, "-v"])
