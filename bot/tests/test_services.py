#!/usr/bin/env python3
"""
Тесты для Service Layer.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch
from decimal import Decimal

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.base import ServiceContainer, setup_service_container
from services.user_service import UserService
from services.payment_service import PaymentService
from services.subscription_service import SubscriptionService
from services.notification_service import NotificationService
from db.models import VPNUsers, YPayments


class TestServiceContainer:
    """Тесты для ServiceContainer."""
    
    @pytest.fixture
    def container(self):
        """Экземпляр ServiceContainer."""
        return ServiceContainer()
    
    def test_register_singleton(self, container):
        """Тест регистрации singleton сервиса."""
        # Arrange
        mock_service = MagicMock()
        
        # Act
        container.register_singleton(UserService, mock_service)
        
        # Assert
        assert UserService in container._singletons
        assert container._singletons[UserService] == mock_service
    
    def test_register_factory(self, container):
        """Тест регистрации фабрики сервиса."""
        # Arrange
        mock_factory = MagicMock()
        
        # Act
        container.register_factory(UserService, mock_factory)
        
        # Assert
        assert UserService in container._factories
        assert container._factories[UserService] == mock_factory
    
    def test_register_transient(self, container):
        """Тест регистрации transient сервиса."""
        # Act
        container.register_transient(UserService, UserService)
        
        # Assert
        assert UserService in container._services
        assert container._services[UserService] == UserService
    
    def test_get_singleton_service(self, container):
        """Тест получения singleton сервиса."""
        # Arrange
        mock_service = MagicMock()
        container.register_singleton(UserService, mock_service)
        
        # Act
        service = container.get_service(UserService)
        
        # Assert
        assert service == mock_service
    
    def test_get_factory_service(self, container):
        """Тест получения сервиса через фабрику."""
        # Arrange
        mock_service = MagicMock()
        mock_factory = MagicMock(return_value=mock_service)
        container.register_factory(UserService, mock_factory)
        
        # Act
        service = container.get_service(UserService)
        
        # Assert
        assert service == mock_service
        mock_factory.assert_called_once_with(container)
    
    def test_get_service_not_registered(self, container):
        """Тест получения незарегистрированного сервиса."""
        # Act & Assert
        with pytest.raises(ValueError, match="Service UserService not registered"):
            container.get_service(UserService)
    
    def test_get_stats(self, container):
        """Тест получения статистики контейнера."""
        # Arrange
        container.register_singleton(UserService, MagicMock())
        container.register_factory(PaymentService, MagicMock())
        
        # Act
        stats = container.get_stats()
        
        # Assert
        assert stats['initialized'] is False
        assert stats['singletons_count'] == 1
        assert stats['factories_count'] == 1
        assert stats['transients_count'] == 0
        assert 'UserService' in stats['registered_services']
        assert 'PaymentService' in stats['registered_services']


class TestUserService:
    """Тесты для UserService."""
    
    @pytest.fixture
    def mock_container(self):
        """Мок контейнера."""
        return MagicMock()
    
    @pytest.fixture
    def user_service(self, mock_container):
        """Экземпляр UserService с мок контейнером."""
        return UserService(mock_container)
    
    @pytest.mark.asyncio
    async def test_initialize(self, user_service):
        """Тест инициализации UserService."""
        # Act
        await user_service.initialize()
        
        # Assert - проверяем что метод выполнился без ошибок
        assert True
    
    @pytest.mark.asyncio
    async def test_cleanup(self, user_service):
        """Тест очистки UserService."""
        # Act
        await user_service.cleanup()
        
        # Assert - проверяем что метод выполнился без ошибок
        assert True
    
    @pytest.mark.asyncio
    @patch('services.user_service.UserService.get_user_repository')
    async def test_create_or_get_user_existing(self, mock_repo_context, user_service):
        """Тест получения существующего пользователя."""
        # Arrange
        telegram_id = 123456789
        existing_user = VPNUsers(id=1, tg_id=telegram_id, vpn_id="test_vpn_id", test=False)
        
        mock_repo = AsyncMock()
        mock_repo.get_by_telegram_id.return_value = existing_user
        mock_repo_context.return_value.__aenter__.return_value = mock_repo
        
        # Act
        result = await user_service.create_or_get_user(telegram_id)
        
        # Assert
        assert result == existing_user
        mock_repo.get_by_telegram_id.assert_called_once_with(telegram_id)
        mock_repo.create_user.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('services.user_service.UserService.get_user_repository')
    async def test_create_or_get_user_new(self, mock_repo_context, user_service):
        """Тест создания нового пользователя."""
        # Arrange
        telegram_id = 123456789
        new_user = VPNUsers(id=1, tg_id=telegram_id, vpn_id="new_vpn_id", test=False)
        
        mock_repo = AsyncMock()
        mock_repo.get_by_telegram_id.return_value = None
        mock_repo.create_user.return_value = new_user
        mock_repo_context.return_value.__aenter__.return_value = mock_repo
        
        # Act
        result = await user_service.create_or_get_user(telegram_id)
        
        # Assert
        assert result == new_user
        mock_repo.get_by_telegram_id.assert_called_once_with(telegram_id)
        mock_repo.create_user.assert_called_once_with(telegram_id)
    
    @pytest.mark.asyncio
    @patch('services.user_service.UserService.get_user_repository')
    async def test_check_test_subscription_used(self, mock_repo_context, user_service):
        """Тест проверки использования тестовой подписки."""
        # Arrange
        telegram_id = 123456789
        
        mock_repo = AsyncMock()
        mock_repo.has_test_subscription.return_value = True
        mock_repo_context.return_value.__aenter__.return_value = mock_repo
        
        # Act
        result = await user_service.check_test_subscription_used(telegram_id)
        
        # Assert
        assert result is True
        mock_repo.has_test_subscription.assert_called_once_with(telegram_id)


class TestPaymentService:
    """Тесты для PaymentService."""
    
    @pytest.fixture
    def mock_container(self):
        """Мок контейнера."""
        return MagicMock()
    
    @pytest.fixture
    def payment_service(self, mock_container):
        """Экземпляр PaymentService с мок контейнером."""
        return PaymentService(mock_container)
    
    @pytest.mark.asyncio
    async def test_initialize(self, payment_service):
        """Тест инициализации PaymentService."""
        # Act
        await payment_service.initialize()
        
        # Assert
        assert True
    
    @pytest.mark.asyncio
    @patch('services.payment_service.goods')
    async def test_validate_product_valid(self, mock_goods, payment_service):
        """Тест валидации существующего товара."""
        # Arrange
        product_callback = "test_product"
        mock_goods.get_callbacks.return_value = ["test_product", "other_product"]
        
        # Act
        result = await payment_service.validate_product(product_callback)
        
        # Assert
        assert result is True
        mock_goods.get_callbacks.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('services.payment_service.goods')
    async def test_validate_product_invalid(self, mock_goods, payment_service):
        """Тест валидации несуществующего товара."""
        # Arrange
        product_callback = "invalid_product"
        mock_goods.get_callbacks.return_value = ["test_product", "other_product"]
        
        # Act
        result = await payment_service.validate_product(product_callback)
        
        # Assert
        assert result is False
        mock_goods.get_callbacks.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('services.payment_service.goods')
    async def test_get_product_info(self, mock_goods, payment_service):
        """Тест получения информации о товаре."""
        # Arrange
        product_callback = "test_product"
        product_data = {"name": "Test Product", "price": {"rub": 100}}
        
        mock_goods.get_callbacks.return_value = ["test_product"]
        mock_goods.get.return_value = product_data
        
        # Act
        result = await payment_service.get_product_info(product_callback)
        
        # Assert
        assert result == product_data
        mock_goods.get.assert_called_once_with(product_callback)
    
    @pytest.mark.asyncio
    @patch('services.payment_service.yookassa')
    @patch('services.payment_service.goods')
    async def test_create_yookassa_payment(self, mock_goods, mock_yookassa, payment_service):
        """Тест создания платежа YooKassa."""
        # Arrange
        telegram_id = 123456789
        product_callback = "test_product"
        chat_id = 987654321
        language_code = "ru"
        
        mock_goods.get_callbacks.return_value = ["test_product"]
        mock_yookassa.create_payment.return_value = {"id": "payment_123", "amount": "100"}
        
        # Act
        result = await payment_service.create_yookassa_payment(
            telegram_id, product_callback, chat_id, language_code
        )
        
        # Assert
        assert result is not None
        assert result["id"] == "payment_123"
        mock_yookassa.create_payment.assert_called_once_with(
            telegram_id, product_callback, chat_id, language_code
        )


class TestSubscriptionService:
    """Тесты для SubscriptionService."""
    
    @pytest.fixture
    def mock_container(self):
        """Мок контейнера."""
        container = MagicMock()
        
        # Мокаем UserService
        mock_user_service = AsyncMock()
        container.get_service.return_value = mock_user_service
        
        return container
    
    @pytest.fixture
    def subscription_service(self, mock_container):
        """Экземпляр SubscriptionService с мок контейнером."""
        return SubscriptionService(mock_container)
    
    @pytest.mark.asyncio
    async def test_initialize(self, subscription_service):
        """Тест инициализации SubscriptionService."""
        # Act
        await subscription_service.initialize()
        
        # Assert
        assert True
    
    @pytest.mark.asyncio
    @patch('services.subscription_service.marzban_api')
    async def test_get_user_subscription_info(self, mock_marzban_api, subscription_service):
        """Тест получения информации о подписке."""
        # Arrange
        telegram_id = 123456789
        subscription_data = {
            "status": "active",
            "expire": 1640995200,  # timestamp
            "subscription_url": "/sub/test"
        }
        
        mock_marzban_api.get_marzban_profile.return_value = subscription_data
        
        # Act
        result = await subscription_service.get_user_subscription_info(telegram_id)
        
        # Assert
        assert result is not None
        assert result["status"] == "active"
        assert "expire_date" in result
        assert "days_remaining" in result
        mock_marzban_api.get_marzban_profile.assert_called_once_with(telegram_id)
    
    @pytest.mark.asyncio
    @patch('services.subscription_service.marzban_api')
    async def test_create_test_subscription(self, mock_marzban_api, subscription_service):
        """Тест создания тестовой подписки."""
        # Arrange
        telegram_id = 123456789
        test_user = VPNUsers(id=1, tg_id=telegram_id, vpn_id="test_vpn_id", test=False)
        
        # Мокаем UserService
        mock_user_service = AsyncMock()
        mock_user_service.check_test_subscription_used.return_value = False
        mock_user_service.get_user_profile.return_value = test_user
        mock_user_service.mark_test_subscription_used.return_value = True
        
        subscription_service.container.get_service.return_value = mock_user_service
        mock_marzban_api.generate_test_subscription.return_value = True
        
        # Act
        result = await subscription_service.create_test_subscription(telegram_id)
        
        # Assert
        assert result is True
        mock_user_service.check_test_subscription_used.assert_called_once_with(telegram_id)
        mock_user_service.mark_test_subscription_used.assert_called_once_with(telegram_id)
        mock_marzban_api.generate_test_subscription.assert_called_once_with(test_user.vpn_id)


class TestNotificationService:
    """Тесты для NotificationService."""
    
    @pytest.fixture
    def mock_bot(self):
        """Мок бота."""
        return AsyncMock()
    
    @pytest.fixture
    def notification_service(self, mock_bot):
        """Экземпляр NotificationService с мок ботом."""
        service = NotificationService()
        service.set_bot(mock_bot)
        return service
    
    @pytest.mark.asyncio
    async def test_initialize(self, notification_service):
        """Тест инициализации NotificationService."""
        # Act
        await notification_service.initialize()
        
        # Assert
        assert True
    
    @pytest.mark.asyncio
    async def test_send_welcome_message(self, notification_service):
        """Тест отправки приветственного сообщения."""
        # Arrange
        telegram_id = 123456789
        user_name = "Test User"
        has_test_subscription = False
        
        # Act
        result = await notification_service.send_welcome_message(
            telegram_id, user_name, has_test_subscription
        )
        
        # Assert
        assert result is True
        notification_service.bot.send_message.assert_called_once()
        
        # Проверяем аргументы вызова
        call_args = notification_service.bot.send_message.call_args
        assert call_args[1]['chat_id'] == telegram_id
        assert user_name in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_send_custom_notification(self, notification_service):
        """Тест отправки кастомного уведомления."""
        # Arrange
        telegram_id = 123456789
        text = "Custom notification text"
        
        # Act
        result = await notification_service.send_custom_notification(telegram_id, text)
        
        # Assert
        assert result is True
        notification_service.bot.send_message.assert_called_once_with(
            chat_id=telegram_id,
            text=text,
            reply_markup=None
        )
    
    @pytest.mark.asyncio
    async def test_broadcast_notification(self, notification_service):
        """Тест рассылки уведомлений."""
        # Arrange
        user_ids = [123456789, 987654321, 555666777]
        text = "Broadcast message"
        
        # Act
        result = await notification_service.broadcast_notification(user_ids, text)
        
        # Assert
        assert result['sent'] == 3
        assert result['failed'] == 0
        assert notification_service.bot.send_message.call_count == 3


class TestServiceIntegration:
    """Интеграционные тесты для сервисов."""
    
    @pytest.mark.asyncio
    async def test_setup_service_container(self):
        """Тест настройки контейнера сервисов."""
        # Act
        container = setup_service_container()
        
        # Assert
        assert isinstance(container, ServiceContainer)
        
        # Проверяем что все сервисы зарегистрированы
        stats = container.get_stats()
        assert 'UserService' in stats['registered_services']
        assert 'PaymentService' in stats['registered_services']
        assert 'SubscriptionService' in stats['registered_services']
        assert 'NotificationService' in stats['registered_services']
    
    @pytest.mark.asyncio
    async def test_service_dependencies(self):
        """Тест зависимостей между сервисами."""
        # Arrange
        container = setup_service_container()
        
        # Act
        user_service = container.get_service(UserService)
        payment_service = container.get_service(PaymentService)
        subscription_service = container.get_service(SubscriptionService)
        notification_service = container.get_service(NotificationService)
        
        # Assert
        assert isinstance(user_service, UserService)
        assert isinstance(payment_service, PaymentService)
        assert isinstance(subscription_service, SubscriptionService)
        assert isinstance(notification_service, NotificationService)
        
        # Проверяем что у сервисов есть контейнер
        assert user_service.container == container
        assert payment_service.container == container
        assert subscription_service.container == container
        assert notification_service.container == container


if __name__ == "__main__":
    # Запуск тестов
    pytest.main([__file__, "-v"])
