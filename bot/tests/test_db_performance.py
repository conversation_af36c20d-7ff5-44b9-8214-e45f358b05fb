#!/usr/bin/env python3
"""
Тесты производительности БД и оптимизаций.
"""

import pytest
import asyncio
import sys
import os
import time
from unittest.mock import AsyncMock, MagicMock, patch

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.performance import DatabasePerformanceAnalyzer, QueryPerformanceMetrics
from db.cache import MemoryCache, DatabaseCache
from db.migrations import MigrationManager
from db.base import get_pool_status, check_db_connection, get_engine_info
from repositories.user_repository import SQLUserRepository
from db.models import VPNUsers
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="db-performance-test", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class TestDatabasePerformanceAnalyzer:
    """Тесты анализатора производительности БД."""
    
    @pytest.fixture
    def analyzer(self):
        """Экземпляр анализатора производительности."""
        return DatabasePerformanceAnalyzer()
    
    @pytest.mark.asyncio
    async def test_measure_query_success(self, analyzer):
        """Тест измерения времени выполнения успешного запроса."""
        # Arrange
        query_description = "test_query"
        
        # Act
        async with analyzer.measure_query(query_description):
            await asyncio.sleep(0.1)  # Симулируем выполнение запроса
        
        # Assert
        assert len(analyzer.query_metrics) == 1
        metric = analyzer.query_metrics[0]
        assert metric.query == query_description
        assert metric.execution_time >= 0.1
        assert metric.error is None
    
    @pytest.mark.asyncio
    async def test_measure_query_with_error(self, analyzer):
        """Тест измерения времени выполнения запроса с ошибкой."""
        # Arrange
        query_description = "failing_query"
        
        # Act & Assert
        with pytest.raises(ValueError):
            async with analyzer.measure_query(query_description):
                raise ValueError("Test error")
        
        # Assert
        assert len(analyzer.query_metrics) == 1
        metric = analyzer.query_metrics[0]
        assert metric.query == query_description
        assert metric.error == "Test error"
    
    @pytest.mark.asyncio
    async def test_analyze_query_patterns(self, analyzer):
        """Тест анализа паттернов запросов."""
        # Arrange
        # Добавляем несколько метрик
        analyzer.query_metrics = [
            QueryPerformanceMetrics("get_user_by_telegram_id:123", 0.1, analyzer.query_metrics[0].timestamp if analyzer.query_metrics else None),
            QueryPerformanceMetrics("get_user_by_telegram_id:456", 0.2, analyzer.query_metrics[0].timestamp if analyzer.query_metrics else None),
            QueryPerformanceMetrics("create_user:789", 0.3, analyzer.query_metrics[0].timestamp if analyzer.query_metrics else None),
            QueryPerformanceMetrics("select something", 2.0, analyzer.query_metrics[0].timestamp if analyzer.query_metrics else None),  # Медленный запрос
        ]
        
        # Act
        analysis = await analyzer.analyze_query_patterns()
        
        # Assert
        assert analysis['total_queries'] == 4
        assert analysis['slow_queries_count'] == 1
        assert 'user_lookup_by_tg_id' in analysis['query_groups']
        assert 'user_creation' in analysis['query_groups']
        assert analysis['query_groups']['user_lookup_by_tg_id']['count'] == 2
    
    def test_get_query_type(self, analyzer):
        """Тест определения типа запроса."""
        # Test cases
        test_cases = [
            ("get_by_telegram_id:123", "user_lookup_by_tg_id"),
            ("get_by_vpn_id:abc", "user_lookup_by_vpn_id"),
            ("create_user:456", "user_creation"),
            ("yookassa_payment:789", "yookassa_payment"),
            ("SELECT * FROM users", "select"),
            ("INSERT INTO users", "insert"),
            ("UPDATE users SET", "update"),
            ("DELETE FROM users", "delete"),
            ("unknown query", "other")
        ]
        
        for query, expected_type in test_cases:
            result = analyzer._get_query_type(query)
            assert result == expected_type, f"Query '{query}' should be type '{expected_type}', got '{result}'"


class TestMemoryCache:
    """Тесты кэша в памяти."""
    
    @pytest.fixture
    def cache(self):
        """Экземпляр кэша."""
        return MemoryCache(max_size=5, default_ttl=1)
    
    @pytest.mark.asyncio
    async def test_set_and_get(self, cache):
        """Тест сохранения и получения значения."""
        # Arrange
        key = "test_key"
        value = {"data": "test_value"}
        
        # Act
        await cache.set(key, value)
        result = await cache.get(key)
        
        # Assert
        assert result == value
        
        # Проверяем статистику
        stats = cache.get_stats()
        assert stats['hits'] == 1
        assert stats['misses'] == 0
    
    @pytest.mark.asyncio
    async def test_get_nonexistent(self, cache):
        """Тест получения несуществующего значения."""
        # Act
        result = await cache.get("nonexistent_key")
        
        # Assert
        assert result is None
        
        # Проверяем статистику
        stats = cache.get_stats()
        assert stats['hits'] == 0
        assert stats['misses'] == 1
    
    @pytest.mark.asyncio
    async def test_ttl_expiration(self, cache):
        """Тест истечения TTL."""
        # Arrange
        key = "expiring_key"
        value = "expiring_value"
        
        # Act
        await cache.set(key, value, ttl=0.1)  # 0.1 секунды
        
        # Сразу после установки значение должно быть доступно
        result1 = await cache.get(key)
        assert result1 == value
        
        # Ждем истечения TTL
        await asyncio.sleep(0.2)
        
        # После истечения TTL значение должно быть недоступно
        result2 = await cache.get(key)
        assert result2 is None
        
        # Проверяем статистику
        stats = cache.get_stats()
        assert stats['expired'] == 1
    
    @pytest.mark.asyncio
    async def test_lru_eviction(self, cache):
        """Тест LRU выселения."""
        # Arrange - заполняем кэш до максимума
        for i in range(5):
            await cache.set(f"key_{i}", f"value_{i}")
        
        # Обращаемся к первому ключу, чтобы он стал недавно использованным
        await cache.get("key_0")
        
        # Act - добавляем новый ключ, что должно вызвать выселение
        await cache.set("new_key", "new_value")
        
        # Assert - key_0 должен остаться, а key_1 должен быть выселен
        assert await cache.get("key_0") == "value_0"
        assert await cache.get("key_1") is None
        assert await cache.get("new_key") == "new_value"
        
        # Проверяем статистику
        stats = cache.get_stats()
        assert stats['evictions'] == 1
    
    @pytest.mark.asyncio
    async def test_cleanup_expired(self, cache):
        """Тест очистки истекших записей."""
        # Arrange
        await cache.set("key1", "value1", ttl=0.1)
        await cache.set("key2", "value2", ttl=1.0)
        
        # Ждем истечения первого ключа
        await asyncio.sleep(0.2)
        
        # Act
        expired_count = await cache.cleanup_expired()
        
        # Assert
        assert expired_count == 1
        assert await cache.get("key1") is None
        assert await cache.get("key2") == "value2"


class TestDatabaseCache:
    """Тесты кэша БД."""
    
    @pytest.fixture
    def db_cache(self):
        """Экземпляр кэша БД."""
        cache = DatabaseCache()
        # Останавливаем задачу очистки для тестов
        if cache._cleanup_task:
            cache._cleanup_task.cancel()
        return cache
    
    @pytest.mark.asyncio
    async def test_user_cache_operations(self, db_cache):
        """Тест операций с кэшем пользователей."""
        # Arrange
        tg_id = 123456789
        user_data = {"id": 1, "tg_id": tg_id, "vpn_id": "test_vpn_id"}
        
        # Act & Assert
        # Сначала пользователя нет в кэше
        result = await db_cache.get_user(tg_id)
        assert result is None
        
        # Сохраняем пользователя в кэш
        await db_cache.set_user(tg_id, user_data)
        
        # Теперь пользователь должен быть в кэше
        result = await db_cache.get_user(tg_id)
        assert result == user_data
        
        # Инвалидируем кэш
        await db_cache.invalidate_user(tg_id)
        
        # Пользователя снова нет в кэше
        result = await db_cache.get_user(tg_id)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_payment_cache_operations(self, db_cache):
        """Тест операций с кэшем платежей."""
        # Arrange
        payment_id = "test_payment_123"
        payment_data = {"id": 1, "payment_id": payment_id, "amount": 100}
        
        # Act & Assert
        # Сначала платежа нет в кэше
        result = await db_cache.get_payment(payment_id)
        assert result is None
        
        # Сохраняем платеж в кэш
        await db_cache.set_payment(payment_id, payment_data)
        
        # Теперь платеж должен быть в кэше
        result = await db_cache.get_payment(payment_id)
        assert result == payment_data
    
    @pytest.mark.asyncio
    async def test_stats_cache_operations(self, db_cache):
        """Тест операций с кэшем статистики."""
        # Arrange
        stats_data = {"total_users": 100, "active_users": 80}
        
        # Act & Assert
        # Сначала статистики нет в кэше
        result = await db_cache.get_user_stats()
        assert result is None
        
        # Сохраняем статистику в кэш
        await db_cache.set_user_stats(stats_data)
        
        # Теперь статистика должна быть в кэше
        result = await db_cache.get_user_stats()
        assert result == stats_data
    
    def test_get_cache_stats(self, db_cache):
        """Тест получения статистики кэшей."""
        # Act
        stats = db_cache.get_cache_stats()
        
        # Assert
        assert 'user_cache' in stats
        assert 'payment_cache' in stats
        assert 'stats_cache' in stats
        
        for cache_stats in stats.values():
            assert 'size' in cache_stats
            assert 'max_size' in cache_stats
            assert 'hit_rate' in cache_stats


class TestMigrationManager:
    """Тесты менеджера миграций."""
    
    @pytest.fixture
    def migration_manager(self):
        """Экземпляр менеджера миграций."""
        return MigrationManager()
    
    def test_load_migrations(self, migration_manager):
        """Тест загрузки миграций."""
        # Assert
        assert len(migration_manager.migrations) > 0
        
        # Проверяем первую миграцию
        first_migration = migration_manager.migrations[0]
        assert first_migration.version == "001"
        assert first_migration.name == "add_performance_indexes"
        assert "CREATE INDEX" in first_migration.up_sql
        assert "DROP INDEX" in first_migration.down_sql
    
    def test_migration_status_structure(self, migration_manager):
        """Тест структуры статуса миграций."""
        # Act
        # Мокаем get_applied_migrations чтобы избежать обращения к БД
        migration_manager.get_applied_migrations = AsyncMock(return_value=[])
        
        # Создаем корутину и запускаем её
        async def run_test():
            status = await migration_manager.get_migration_status()
            
            # Assert
            assert 'total_migrations' in status
            assert 'applied_migrations' in status
            assert 'pending_migrations' in status
            assert 'migrations' in status
            
            assert status['total_migrations'] == len(migration_manager.migrations)
            assert status['applied_migrations'] == 0
            assert status['pending_migrations'] == len(migration_manager.migrations)
            
            # Проверяем структуру информации о миграциях
            for migration_info in status['migrations']:
                assert 'version' in migration_info
                assert 'name' in migration_info
                assert 'description' in migration_info
                assert 'applied' in migration_info
                assert 'created_at' in migration_info
        
        # Запускаем тест
        asyncio.run(run_test())


class TestConnectionPooling:
    """Тесты пула соединений."""
    
    def test_get_pool_status_structure(self):
        """Тест структуры статуса пула соединений."""
        # Act
        status = get_pool_status()
        
        # Assert
        assert isinstance(status, dict)
        assert 'status' in status
        
        # Если пул инициализирован, проверяем дополнительные поля
        if status['status'] == 'active':
            assert 'pool_size' in status
            assert 'checked_in' in status
            assert 'checked_out' in status
            assert 'overflow' in status
            assert 'invalid' in status
            assert 'total_connections' in status
    
    def test_get_engine_info_structure(self):
        """Тест структуры информации о движке."""
        # Act
        info = get_engine_info()
        
        # Assert
        assert isinstance(info, dict)
        assert 'status' in info
        
        # Если движок инициализирован, проверяем дополнительные поля
        if info['status'] == 'initialized':
            assert 'url' in info
            assert 'dialect' in info
            assert 'driver' in info
            assert 'pool_class' in info
            assert 'echo' in info
            assert 'echo_pool' in info
    
    @pytest.mark.asyncio
    async def test_check_db_connection(self):
        """Тест проверки соединения с БД."""
        # Act
        result = await check_db_connection()
        
        # Assert
        assert isinstance(result, bool)
        # В тестовой среде соединение может быть недоступно, это нормально


if __name__ == "__main__":
    # Запуск тестов
    pytest.main([__file__, "-v"])
