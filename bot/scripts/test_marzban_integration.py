#!/usr/bin/env python3
"""
Комплексный тест интеграции VPN бота с Marzban инфраструктурой.
Проверяет все компоненты системы: БД, API, создание пользователей, выбор нод.
"""

import asyncio
import sys
import os
import time
from typing import Dict, Any, List

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.marzban_api import panel, generate_test_subscription, check_if_user_exists
from db.base import get_session
from repositories.country_node_repository import *
from services.marzban_country_node_service import MarzbanCountryNodeService
from services.user_preference_service import UserPreferenceService
from services.node_selection_service import NodeSelectionService, SelectionStrategy
from utils.logging_config import get_logger

logger = get_logger(__name__)


class MarzbanIntegrationTester:
    """Класс для комплексного тестирования интеграции с Marzban."""
    
    def __init__(self):
        self.test_results = {}
        self.test_user_id = 999999  # Тестовый Telegram ID
        self.created_users = []  # Список созданных тестовых пользователей
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Запускает все тесты и возвращает результаты."""
        logger.info("🧪 Starting comprehensive Marzban integration tests...")
        
        tests = [
            ("Database Connection", self.test_database_connection),
            ("Marzban API Connection", self.test_marzban_api_connection),
            ("Countries and Nodes Setup", self.test_countries_nodes_setup),
            ("Node Selection Service", self.test_node_selection_service),
            ("User Creation (VLESS)", self.test_user_creation),
            ("User Configuration Retrieval", self.test_user_config_retrieval),
            ("Country Selection Integration", self.test_country_selection),
            ("Performance and Caching", self.test_performance_caching),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*60}")
            logger.info(f"🔍 Running test: {test_name}")
            logger.info(f"{'='*60}")
            
            try:
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'PASS' if result else 'FAIL',
                    'details': result if isinstance(result, dict) else {}
                }
                status_emoji = "✅" if result else "❌"
                logger.info(f"{status_emoji} Test '{test_name}': {'PASSED' if result else 'FAILED'}")
                
            except Exception as e:
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
                logger.error(f"💥 Test '{test_name}' ERROR: {e}")
        
        # Очистка тестовых данных
        await self.cleanup_test_data()
        
        # Вывод итогового отчета
        self.print_final_report()
        
        return self.test_results
    
    async def test_database_connection(self) -> bool:
        """Тест подключения к базе данных."""
        try:
            async with get_session() as session:
                # Проверяем подключение
                result = await session.execute("SELECT 1")
                assert result.scalar() == 1
                
                # Проверяем наличие основных таблиц
                tables_to_check = [
                    'vpnusers', 'countries', 'marzban_nodes', 
                    'marzban_inbounds', 'user_node_preferences'
                ]
                
                for table in tables_to_check:
                    result = await session.execute(f"SHOW TABLES LIKE '{table}'")
                    assert result.fetchone() is not None, f"Table {table} not found"
                
                logger.info("✅ Database connection and tables verified")
                return True
                
        except Exception as e:
            logger.error(f"❌ Database test failed: {e}")
            return False
    
    async def test_marzban_api_connection(self) -> bool:
        """Тест подключения к Marzban API."""
        try:
            # Тест получения токена
            token = await panel.get_token()
            assert token is not None and len(token) > 0
            logger.info(f"✅ Token obtained: {token[:20]}...")
            
            # Тест получения пользователей
            users = await panel.get_users(limit=5)
            assert 'users' in users
            logger.info(f"✅ Users retrieved: {len(users['users'])}")
            
            # Тест получения нод
            nodes = await panel.get_nodes()
            logger.info(f"✅ Nodes retrieved: {len(nodes)}")
            
            # Тест получения системной статистики
            stats = await panel.get_system_stats()
            logger.info(f"✅ System stats retrieved")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Marzban API test failed: {e}")
            return False
    
    async def test_countries_nodes_setup(self) -> bool:
        """Тест настройки стран и нод."""
        try:
            async with get_session() as session:
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                inbound_repo = MarzbanInboundRepository(session)
                
                # Проверяем страны
                countries = await country_repo.get_all_active()
                assert len(countries) > 0, "No active countries found"
                logger.info(f"✅ Active countries: {len(countries)}")
                
                # Проверяем ноды
                nodes = await node_repo.get_all_active()
                logger.info(f"✅ Active nodes: {len(nodes)}")
                
                # Проверяем inbounds
                inbounds = await inbound_repo.get_all_active()
                logger.info(f"✅ Active inbounds: {len(inbounds)}")
                
                # Проверяем связи
                for country in countries[:2]:  # Проверяем первые 2 страны
                    country_nodes = await node_repo.get_nodes_by_country(country.id)
                    logger.info(f"✅ Country {country.name_ru}: {len(country_nodes)} nodes")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Countries/nodes setup test failed: {e}")
            return False
    
    async def test_node_selection_service(self) -> Dict[str, Any]:
        """Тест сервиса выбора нод."""
        try:
            async with get_session() as session:
                # Создаем сервисы
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                preference_repo = UserNodePreferenceRepository(session)
                
                preference_service = UserPreferenceService(preference_repo, country_repo)
                selection_service = NodeSelectionService(node_repo, stats_repo, preference_service)
                main_service = MarzbanCountryNodeService(
                    preference_service, selection_service, country_repo, node_repo, stats_repo
                )
                
                # Тест получения доступных стран
                countries = await main_service.get_available_countries_with_nodes()
                assert len(countries) > 0, "No countries with nodes found"
                logger.info(f"✅ Countries with nodes: {len(countries)}")
                
                # Тест выбора оптимальной ноды
                result = await main_service.get_optimal_node_for_user(
                    self.test_user_id, strategy=SelectionStrategy.HYBRID
                )
                
                if result.success:
                    logger.info(f"✅ Optimal node selected: {result.selected_node.name}")
                    logger.info(f"   Strategy: {result.selection_strategy}")
                    logger.info(f"   Reasons: {result.selection_reasons}")
                else:
                    logger.warning(f"⚠️ No optimal node found: {result.selection_reasons}")
                
                # Тест рекомендаций стран
                recommendations = await main_service.get_country_recommendations(self.test_user_id)
                logger.info(f"✅ Country recommendations: {len(recommendations)}")
                
                return {
                    'countries_count': len(countries),
                    'node_selection_success': result.success,
                    'selected_node': result.selected_node.name if result.selected_node else None,
                    'recommendations_count': len(recommendations)
                }
                
        except Exception as e:
            logger.error(f"❌ Node selection service test failed: {e}")
            return False
    
    async def test_user_creation(self) -> Dict[str, Any]:
        """Тест создания пользователя VLESS."""
        try:
            # Создаем тестового пользователя
            test_username = f'test_user_{int(time.time())}'
            self.created_users.append(test_username)
            
            # Проверяем, что пользователь не существует
            exists_before = await check_if_user_exists(test_username)
            assert not exists_before, f"User {test_username} already exists"
            
            # Создаем пользователя
            result = await generate_test_subscription(test_username)
            assert result is not None, "User creation returned None"
            assert 'subscription_url' in result, "No subscription URL in result"
            
            logger.info(f"✅ User created: {test_username}")
            logger.info(f"   Subscription URL: {result['subscription_url'][:50]}...")
            
            # Проверяем, что пользователь создался
            exists_after = await check_if_user_exists(test_username)
            assert exists_after, f"User {test_username} not found after creation"
            
            # Получаем информацию о пользователе
            user_info = await panel.get_user(test_username)
            assert user_info['status'] == 'active', f"User status is not active: {user_info['status']}"
            
            logger.info(f"✅ User verification successful")
            logger.info(f"   Status: {user_info['status']}")
            logger.info(f"   Protocols: {list(user_info.get('proxies', {}).keys())}")
            logger.info(f"   Expire: {user_info.get('expire', 'N/A')}")
            
            return {
                'username': test_username,
                'subscription_url': result['subscription_url'],
                'status': user_info['status'],
                'protocols': list(user_info.get('proxies', {}).keys()),
                'expire': user_info.get('expire')
            }
            
        except Exception as e:
            logger.error(f"❌ User creation test failed: {e}")
            return False
    
    async def test_user_config_retrieval(self) -> bool:
        """Тест получения конфигурации пользователя."""
        try:
            if not self.created_users:
                logger.warning("⚠️ No test users available for config retrieval test")
                return True
            
            username = self.created_users[0]
            user_info = await panel.get_user(username)
            
            # Проверяем наличие VLESS конфигурации
            proxies = user_info.get('proxies', {})
            assert 'vless' in proxies, "VLESS protocol not found in user proxies"
            
            vless_config = proxies['vless']
            logger.info(f"✅ VLESS config retrieved for {username}")
            logger.info(f"   Flow: {vless_config.get('flow', 'N/A')}")
            
            # Проверяем subscription URL
            subscription_url = user_info.get('subscription_url')
            assert subscription_url is not None, "Subscription URL is None"
            logger.info(f"✅ Subscription URL available")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ User config retrieval test failed: {e}")
            return False
    
    async def test_country_selection(self) -> Dict[str, Any]:
        """Тест интеграции выбора стран."""
        try:
            async with get_session() as session:
                preference_repo = UserNodePreferenceRepository(session)
                country_repo = CountryRepository(session)
                
                preference_service = UserPreferenceService(preference_repo, country_repo)
                
                # Получаем доступные страны
                countries = await preference_service.get_available_countries()
                assert len(countries) > 0, "No available countries"
                
                # Добавляем предпочтение
                first_country = countries[0]
                success = await preference_service.add_preferred_country(
                    self.test_user_id, first_country.id
                )
                assert success, "Failed to add country preference"
                
                # Получаем предпочтения
                preferences = await preference_service.get_user_preferences(self.test_user_id)
                assert first_country.id in preferences.preferred_countries
                
                logger.info(f"✅ Country selection integration working")
                logger.info(f"   Available countries: {len(countries)}")
                logger.info(f"   Added preference: {first_country.name_ru}")
                
                return {
                    'available_countries': len(countries),
                    'preference_added': first_country.name_ru,
                    'user_preferences': len(preferences.preferred_countries)
                }
                
        except Exception as e:
            logger.error(f"❌ Country selection test failed: {e}")
            return False
    
    async def test_performance_caching(self) -> Dict[str, Any]:
        """Тест производительности и кэширования."""
        try:
            async with get_session() as session:
                # Создаем сервисы
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                preference_repo = UserNodePreferenceRepository(session)
                
                preference_service = UserPreferenceService(preference_repo, country_repo)
                selection_service = NodeSelectionService(node_repo, stats_repo, preference_service)
                main_service = MarzbanCountryNodeService(
                    preference_service, selection_service, country_repo, node_repo, stats_repo
                )
                
                # Тест без кэша
                start_time = time.time()
                countries1 = await main_service.get_available_countries_with_nodes(use_cache=False)
                time_no_cache = time.time() - start_time
                
                # Тест с кэшем (первый запрос)
                start_time = time.time()
                countries2 = await main_service.get_available_countries_with_nodes(use_cache=True)
                time_first_cache = time.time() - start_time
                
                # Тест с кэшем (второй запрос - должен быть быстрее)
                start_time = time.time()
                countries3 = await main_service.get_available_countries_with_nodes(use_cache=True)
                time_cached = time.time() - start_time
                
                # Проверяем статистику кэша
                cache_stats = main_service.get_cache_stats()
                
                logger.info(f"✅ Performance test completed")
                logger.info(f"   No cache: {time_no_cache:.4f}s")
                logger.info(f"   First cache: {time_first_cache:.4f}s")
                logger.info(f"   Cached: {time_cached:.4f}s")
                logger.info(f"   Cache improvement: {(time_no_cache/time_cached):.1f}x faster")
                
                return {
                    'time_no_cache': time_no_cache,
                    'time_first_cache': time_first_cache,
                    'time_cached': time_cached,
                    'cache_improvement': time_no_cache / time_cached if time_cached > 0 else 0,
                    'cache_stats': cache_stats
                }
                
        except Exception as e:
            logger.error(f"❌ Performance/caching test failed: {e}")
            return False
    
    async def cleanup_test_data(self):
        """Очистка тестовых данных."""
        logger.info("🧹 Cleaning up test data...")
        
        # Удаляем тестовых пользователей из Marzban
        for username in self.created_users:
            try:
                await panel.delete_user(username)
                logger.info(f"✅ Deleted test user: {username}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to delete test user {username}: {e}")
        
        # Очищаем тестовые предпочтения из БД
        try:
            async with get_session() as session:
                preference_repo = UserNodePreferenceRepository(session)
                await preference_repo.delete_user_preferences(self.test_user_id)
                logger.info(f"✅ Deleted test preferences for user {self.test_user_id}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to delete test preferences: {e}")
    
    def print_final_report(self):
        """Выводит итоговый отчет тестирования."""
        logger.info("\n" + "="*80)
        logger.info("📊 FINAL TEST REPORT")
        logger.info("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'PASS')
        failed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'FAIL')
        error_tests = sum(1 for r in self.test_results.values() if r['status'] == 'ERROR')
        
        logger.info(f"📈 Total tests: {total_tests}")
        logger.info(f"✅ Passed: {passed_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"💥 Errors: {error_tests}")
        logger.info(f"📊 Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\n📋 Detailed results:")
        for test_name, result in self.test_results.items():
            status_emoji = {"PASS": "✅", "FAIL": "❌", "ERROR": "💥"}[result['status']]
            logger.info(f"  {status_emoji} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR':
                logger.info(f"      Error: {result.get('error', 'Unknown error')}")
        
        if passed_tests == total_tests:
            logger.info("\n🎉 ALL TESTS PASSED! Your Marzban integration is working perfectly!")
        else:
            logger.info(f"\n⚠️ {failed_tests + error_tests} tests failed. Please check the logs above.")
        
        logger.info("="*80)


async def main():
    """Главная функция для запуска тестов."""
    tester = MarzbanIntegrationTester()
    results = await tester.run_all_tests()
    
    # Возвращаем код выхода на основе результатов
    failed_count = sum(1 for r in results.values() if r['status'] != 'PASS')
    return 0 if failed_count == 0 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
