#!/usr/bin/env python3
"""
Скрипт для тестирования MarzbanCountryNodeService и NodeMonitoringService.
Проверяет интеграцию с Marzban API и работу мониторинга нод.
"""

import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.base import get_session
from repositories.country_node_repository import (
    CountryRepository, MarzbanNodeRepository, MarzbanInboundRepository,
    UserNodePreferenceRepository, NodeStatisticsRepository
)
from services.user_preference_service import UserPreferenceService
from services.node_selection_service import NodeSelectionService, SelectionStrategy
from services.marzban_country_node_service import MarzbanCountryNodeService
from services.node_monitoring_service import NodeMonitoringService, NodeHealthCheck
from db.models_countries_nodes import MarzbanNode, NodeStatus
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="test-marzban-services", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class MockMarzbanAPI:
    """Мок Marzban API для тестирования."""
    
    def __init__(self):
        self.nodes_data = {
            "de-fra-01": {
                "id": "de-fra-01",
                "name": "Germany Frankfurt #1",
                "status": "connected",
                "cpu_usage": 45.5,
                "memory_usage": 60.2,
                "users_count": 150
            },
            "nl-ams-01": {
                "id": "nl-ams-01", 
                "name": "Netherlands Amsterdam #1",
                "status": "connected",
                "cpu_usage": 55.8,
                "memory_usage": 70.1,
                "users_count": 200
            },
            "us-ny-01": {
                "id": "us-ny-01",
                "name": "United States New York #1", 
                "status": "disconnected",
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "users_count": 0
            }
        }
    
    async def get_nodes(self):
        """Мок получения списка нод."""
        return list(self.nodes_data.values())
    
    async def get_node_status(self, node_id: str):
        """Мок получения статуса ноды."""
        if node_id in self.nodes_data:
            return self.nodes_data[node_id]
        return None
    
    async def get_node_stats(self, node_id: str):
        """Мок получения статистики ноды."""
        if node_id in self.nodes_data:
            node = self.nodes_data[node_id]
            return {
                "cpu_usage": node["cpu_usage"],
                "memory_usage": node["memory_usage"],
                "users_count": node["users_count"]
            }
        return None
    
    async def get_inbounds(self):
        """Мок получения inbounds."""
        return [
            {"id": "vless-reality-de-01", "protocol": "vless", "node_id": "de-fra-01"},
            {"id": "shadowsocks-tcp-de-01", "protocol": "shadowsocks", "node_id": "de-fra-01"},
            {"id": "vless-reality-nl-01", "protocol": "vless", "node_id": "nl-ams-01"}
        ]


class MarzbanServicesTester:
    """Тестер для сервисов Marzban."""
    
    def __init__(self):
        self.test_results = []
        self.test_user_id = 123456789
        self.mock_api = MockMarzbanAPI()
    
    async def test_marzban_country_node_service(self):
        """Тест MarzbanCountryNodeService."""
        try:
            async with get_session() as session:
                # Создаем repositories
                preference_repo = UserNodePreferenceRepository(session)
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                inbound_repo = MarzbanInboundRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                
                # Создаем сервисы
                preference_service = UserPreferenceService(preference_repo, country_repo, node_repo)
                selection_service = NodeSelectionService(node_repo, inbound_repo, stats_repo, preference_service)
                
                # Создаем основной сервис
                marzban_service = MarzbanCountryNodeService(
                    preference_service, selection_service, country_repo, node_repo, stats_repo
                )
                
                # Тест 1: Получение стран с нодами
                countries_with_nodes = await marzban_service.get_available_countries_with_nodes(
                    include_stats=True, use_cache=False
                )
                
                if len(countries_with_nodes) > 0:
                    logger.info(f"✅ Found {len(countries_with_nodes)} countries with nodes")
                    
                    # Проверяем структуру данных
                    first_country = countries_with_nodes[0]
                    if (hasattr(first_country, 'country') and 
                        hasattr(first_country, 'nodes') and
                        hasattr(first_country, 'total_nodes')):
                        logger.info(f"✅ Country data structure is correct: {first_country.country.name_ru}")
                        self.test_results.append(("Get Countries with Nodes", True, f"{len(countries_with_nodes)} countries"))
                    else:
                        self.test_results.append(("Get Countries with Nodes", False, "Invalid data structure"))
                else:
                    self.test_results.append(("Get Countries with Nodes", False, "No countries found"))
                
                # Тест 2: Выбор оптимальной ноды для пользователя
                optimal_result = await marzban_service.get_optimal_node_for_user(
                    tg_id=self.test_user_id,
                    strategy=SelectionStrategy.HYBRID,
                    use_cache=False
                )
                
                if optimal_result.success and optimal_result.selected_node:
                    logger.info(f"✅ Selected optimal node: {optimal_result.selected_node.name}")
                    logger.info(f"   Strategy: {optimal_result.selection_strategy}")
                    logger.info(f"   Reasons: {', '.join(optimal_result.selection_reasons)}")
                    self.test_results.append(("Get Optimal Node", True, f"Selected {optimal_result.selected_node.name}"))
                else:
                    logger.warning(f"❌ Failed to select optimal node: {optimal_result.selection_reasons}")
                    self.test_results.append(("Get Optimal Node", False, "No optimal node found"))
                
                # Тест 3: Получение рекомендаций стран
                recommendations = await marzban_service.get_country_recommendations(
                    tg_id=self.test_user_id,
                    limit=3,
                    use_cache=False
                )
                
                if len(recommendations) > 0:
                    logger.info(f"✅ Got {len(recommendations)} country recommendations")
                    for rec in recommendations:
                        logger.info(f"   {rec.country.flag} {rec.country.name_ru}: {rec.available_nodes} nodes")
                    self.test_results.append(("Country Recommendations", True, f"{len(recommendations)} recommendations"))
                else:
                    self.test_results.append(("Country Recommendations", False, "No recommendations"))
                
                # Тест 4: Обновление предпочтений пользователя
                success = await marzban_service.update_user_country_preference(
                    tg_id=self.test_user_id,
                    country_id="DE",
                    action="add"
                )
                
                if success:
                    logger.info("✅ Successfully updated user country preference")
                    self.test_results.append(("Update User Preference", True, "Added DE to preferences"))
                else:
                    self.test_results.append(("Update User Preference", False, "Failed to update"))
                
                # Тест 5: Проверка кэширования
                cache_stats = marzban_service.get_cache_stats()
                
                if cache_stats and 'countries_cache' in cache_stats:
                    logger.info(f"✅ Cache stats: {cache_stats}")
                    self.test_results.append(("Cache Functionality", True, "Cache working"))
                else:
                    self.test_results.append(("Cache Functionality", False, "Cache not working"))
                
        except Exception as e:
            logger.error(f"❌ Error testing MarzbanCountryNodeService: {e}")
            self.test_results.append(("MarzbanCountryNodeService", False, str(e)))
    
    async def test_node_monitoring_service(self):
        """Тест NodeMonitoringService."""
        try:
            async with get_session() as session:
                # Создаем repositories
                node_repo = MarzbanNodeRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                
                # Создаем сервис мониторинга с мок API
                monitoring_service = NodeMonitoringService(node_repo, stats_repo, self.mock_api)
                
                # Тест 1: Проверка всех нод
                health_checks = await monitoring_service.check_all_nodes()
                
                if len(health_checks) > 0:
                    online_count = sum(1 for check in health_checks if check.is_online)
                    offline_count = len(health_checks) - online_count
                    
                    logger.info(f"✅ Health check completed: {online_count} online, {offline_count} offline")
                    
                    for check in health_checks:
                        status = "🟢 ONLINE" if check.is_online else "🔴 OFFLINE"
                        logger.info(f"   {check.node_id}: {status} ({check.response_time_ms:.1f}ms)")
                    
                    self.test_results.append(("Node Health Check", True, f"{len(health_checks)} nodes checked"))
                else:
                    self.test_results.append(("Node Health Check", False, "No nodes to check"))
                
                # Тест 2: Проверка конкретной ноды
                single_check = await monitoring_service.check_single_node_by_id("de-fra-01")
                
                if single_check:
                    logger.info(f"✅ Single node check: {single_check.node_id} - {'ONLINE' if single_check.is_online else 'OFFLINE'}")
                    self.test_results.append(("Single Node Check", True, f"Checked {single_check.node_id}"))
                else:
                    self.test_results.append(("Single Node Check", False, "Failed to check node"))
                
                # Тест 3: Получение статистики мониторинга
                monitoring_stats = await monitoring_service.get_monitoring_stats()
                
                if monitoring_stats:
                    logger.info(f"✅ Monitoring stats: {monitoring_stats.online_nodes}/{monitoring_stats.total_nodes} online")
                    logger.info(f"   Average response time: {monitoring_stats.average_response_time:.1f}ms")
                    self.test_results.append(("Monitoring Stats", True, f"{monitoring_stats.total_nodes} nodes monitored"))
                else:
                    self.test_results.append(("Monitoring Stats", False, "No monitoring stats"))
                
                # Тест 4: Получение uptime статистики
                uptime_stats = await monitoring_service.get_node_uptime_stats("de-fra-01", hours=24)
                
                if uptime_stats:
                    logger.info(f"✅ Uptime stats for de-fra-01: {uptime_stats}")
                    self.test_results.append(("Uptime Stats", True, f"Uptime: {uptime_stats.get('uptime_percentage', 0):.1f}%"))
                else:
                    self.test_results.append(("Uptime Stats", False, "No uptime stats"))
                
                # Тест 5: Проверка состояния мониторинга
                is_running_before = monitoring_service.is_running
                
                # Запускаем мониторинг на короткое время
                await monitoring_service.start_monitoring()
                await asyncio.sleep(1)  # Ждем 1 секунду
                
                is_running_during = monitoring_service.is_running
                
                await monitoring_service.stop_monitoring()
                is_running_after = monitoring_service.is_running
                
                if not is_running_before and is_running_during and not is_running_after:
                    logger.info("✅ Monitoring start/stop functionality works correctly")
                    self.test_results.append(("Monitoring Control", True, "Start/stop works"))
                else:
                    logger.warning(f"❌ Monitoring control issue: before={is_running_before}, during={is_running_during}, after={is_running_after}")
                    self.test_results.append(("Monitoring Control", False, "Start/stop failed"))
                
        except Exception as e:
            logger.error(f"❌ Error testing NodeMonitoringService: {e}")
            self.test_results.append(("NodeMonitoringService", False, str(e)))
    
    async def test_integration_scenario(self):
        """Тест интеграционного сценария."""
        try:
            async with get_session() as session:
                # Создаем все repositories
                preference_repo = UserNodePreferenceRepository(session)
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                inbound_repo = MarzbanInboundRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                
                # Создаем все сервисы
                preference_service = UserPreferenceService(preference_repo, country_repo, node_repo)
                selection_service = NodeSelectionService(node_repo, inbound_repo, stats_repo, preference_service)
                marzban_service = MarzbanCountryNodeService(
                    preference_service, selection_service, country_repo, node_repo, stats_repo
                )
                monitoring_service = NodeMonitoringService(node_repo, stats_repo, self.mock_api)
                
                # Сценарий: Пользователь выбирает страну и получает оптимальную ноду
                
                # 1. Устанавливаем предпочтения пользователя
                await marzban_service.update_user_country_preference(self.test_user_id, "DE", "add")
                await marzban_service.update_user_country_preference(self.test_user_id, "NL", "add")
                
                # 2. Запускаем мониторинг для обновления статуса нод
                health_checks = await monitoring_service.check_all_nodes()
                
                # 3. Получаем рекомендации стран
                recommendations = await marzban_service.get_country_recommendations(self.test_user_id)
                
                # 4. Выбираем оптимальную ноду
                optimal_result = await marzban_service.get_optimal_node_for_user(
                    self.test_user_id, strategy=SelectionStrategy.HYBRID
                )
                
                # 5. Проверяем результат
                if (len(health_checks) > 0 and 
                    len(recommendations) > 0 and 
                    optimal_result.success):
                    
                    logger.info("✅ Integration scenario completed successfully:")
                    logger.info(f"   Health checks: {len(health_checks)} nodes")
                    logger.info(f"   Recommendations: {len(recommendations)} countries")
                    logger.info(f"   Selected node: {optimal_result.selected_node.name}")
                    
                    # Проверяем, что выбранная нода в предпочитаемых странах
                    selected_country = optimal_result.selected_node.country_id
                    if selected_country in ["DE", "NL"]:
                        logger.info(f"   ✅ Selected node is in preferred country: {selected_country}")
                        self.test_results.append(("Integration Scenario", True, f"Complete workflow successful"))
                    else:
                        logger.info(f"   ⚠️ Selected node in fallback country: {selected_country}")
                        self.test_results.append(("Integration Scenario", True, f"Fallback selection worked"))
                else:
                    self.test_results.append(("Integration Scenario", False, "Workflow incomplete"))
                
        except Exception as e:
            logger.error(f"❌ Error in integration scenario: {e}")
            self.test_results.append(("Integration Scenario", False, str(e)))
    
    def print_results(self):
        """Выводит результаты тестов."""
        logger.info("\n" + "="*60)
        logger.info("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ MARZBAN СЕРВИСОВ")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {details}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📈 ИТОГО: {passed}/{total} тестов пройдено")
        
        if passed == total:
            logger.info("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
            return True
        else:
            logger.error(f"💥 {total - passed} тестов провалено")
            return False


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Marzban Services Testing")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = MarzbanServicesTester()
    
    try:
        # Запускаем все тесты
        await tester.test_marzban_country_node_service()
        await tester.test_node_monitoring_service()
        await tester.test_integration_scenario()
        
        # Выводим результаты
        success = tester.print_results()
        
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success
        
    except Exception as e:
        logger.error(f"💥 Testing failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
