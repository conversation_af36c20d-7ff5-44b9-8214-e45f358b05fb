#!/usr/bin/env python3
"""
Простые миграции для добавления столбцов timestamps.
"""

import asyncio
import sys
import os
from datetime import datetime

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.base import get_session
from sqlalchemy import text
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="simple-migrations", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


async def add_timestamps_to_table(table_name: str):
    """Добавляет timestamps к таблице."""
    try:
        async with get_session() as session:
            # Проверяем существование столбца created_at
            check_created_at = text(f"""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '{table_name}' 
                AND COLUMN_NAME = 'created_at'
            """)
            
            result = await session.execute(check_created_at)
            created_at_exists = result.scalar() > 0
            
            if not created_at_exists:
                logger.info(f"Adding created_at to {table_name}...")
                add_created_at = text(f"""
                    ALTER TABLE {table_name} 
                    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                """)
                await session.execute(add_created_at)
                logger.info(f"✅ Added created_at to {table_name}")
            else:
                logger.info(f"✅ created_at already exists in {table_name}")
            
            # Проверяем существование столбца updated_at
            check_updated_at = text(f"""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '{table_name}' 
                AND COLUMN_NAME = 'updated_at'
            """)
            
            result = await session.execute(check_updated_at)
            updated_at_exists = result.scalar() > 0
            
            if not updated_at_exists:
                logger.info(f"Adding updated_at to {table_name}...")
                add_updated_at = text(f"""
                    ALTER TABLE {table_name} 
                    ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                """)
                await session.execute(add_updated_at)
                logger.info(f"✅ Added updated_at to {table_name}")
            else:
                logger.info(f"✅ updated_at already exists in {table_name}")
            
            await session.commit()
            
    except Exception as e:
        logger.error(f"❌ Error adding timestamps to {table_name}: {e}")
        raise


async def create_index_if_not_exists(table_name: str, index_name: str, columns: str):
    """Создает индекс если он не существует."""
    try:
        async with get_session() as session:
            # Проверяем существование индекса
            check_index = text(f"""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '{table_name}' 
                AND INDEX_NAME = '{index_name}'
            """)
            
            result = await session.execute(check_index)
            index_exists = result.scalar() > 0
            
            if not index_exists:
                logger.info(f"Creating index {index_name} on {table_name}({columns})...")
                create_index = text(f"""
                    CREATE INDEX {index_name} ON {table_name}({columns})
                """)
                await session.execute(create_index)
                await session.commit()
                logger.info(f"✅ Created index {index_name}")
            else:
                logger.info(f"✅ Index {index_name} already exists")
                
    except Exception as e:
        logger.error(f"❌ Error creating index {index_name}: {e}")
        # Не прерываем выполнение, если индекс уже существует
        pass


async def main():
    """Основная функция миграций."""
    logger.info("🚀 Starting Simple Database Migrations")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Добавляем timestamps к таблицам
        tables = ['vpnusers', 'yookassa_payments', 'crypto_payments']
        
        for table in tables:
            await add_timestamps_to_table(table)
        
        # Создаем индексы
        indexes = [
            ('vpnusers', 'idx_vpnusers_tg_id', 'tg_id'),
            ('vpnusers', 'idx_vpnusers_vpn_id', 'vpn_id'),
            ('vpnusers', 'idx_vpnusers_created_at', 'created_at'),
            ('yookassa_payments', 'idx_yookassa_payments_payment_id', 'payment_id'),
            ('yookassa_payments', 'idx_yookassa_payments_tg_id', 'tg_id'),
            ('yookassa_payments', 'idx_yookassa_payments_created_at', 'created_at'),
            ('crypto_payments', 'idx_crypto_payments_order_id', 'order_id'),
            ('crypto_payments', 'idx_crypto_payments_tg_id', 'tg_id'),
            ('crypto_payments', 'idx_crypto_payments_created_at', 'created_at'),
        ]
        
        for table, index_name, columns in indexes:
            await create_index_if_not_exists(table, index_name, columns)
        
        logger.info("🎉 All migrations completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"💥 Migration failed: {e}", exc_info=True)
        return False
    
    finally:
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
