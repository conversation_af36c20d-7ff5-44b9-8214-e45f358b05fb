#!/usr/bin/env python3
"""
Тестовый скрипт для проверки системы шифрования.
"""

import asyncio
import sys
from pathlib import Path

# Добавляем путь к проекту
sys.path.append(str(Path(__file__).parent.parent))

from db.encryption_methods import (
    store_encrypted_config, 
    get_encrypted_config, 
    list_encrypted_configs,
    save_user_payment_data,
    get_user_payment_data,
    delete_user_payment_data
)
from utils.logging_config import get_logger

logger = get_logger(__name__)


async def test_config_encryption():
    """
    Тестирует шифрование конфигураций.
    """
    print("🔐 Testing config encryption...")
    
    # Тестовые данные
    test_configs = {
        'TEST_API_KEY': 'sk-test-1234567890abcdef',
        'TEST_PASSWORD': 'super_secret_password_123',
        'TEST_TOKEN': 'bot123456:AAHdqTcvCH1vGWJxfSeofSAs0K5PALDsaw'
    }
    
    # Сохраняем тестовые конфигурации
    for key, value in test_configs.items():
        success = await store_encrypted_config(key, value, f"Test config: {key}")
        if success:
            print(f"✅ Stored {key}")
        else:
            print(f"❌ Failed to store {key}")
            return False
    
    # Получаем и проверяем конфигурации
    for key, original_value in test_configs.items():
        decrypted_value = await get_encrypted_config(key)
        if decrypted_value == original_value:
            print(f"✅ Retrieved and verified {key}")
        else:
            print(f"❌ Verification failed for {key}")
            print(f"   Original: {original_value}")
            print(f"   Decrypted: {decrypted_value}")
            return False
    
    # Проверяем список конфигураций
    config_keys = await list_encrypted_configs()
    print(f"📋 Found {len(config_keys)} encrypted configs: {config_keys}")
    
    return True


async def test_user_payment_encryption():
    """
    Тестирует шифрование данных пользователей.
    """
    print("\n💳 Testing user payment data encryption...")
    
    # Тестовые данные пользователя
    test_user_id = 123456789
    test_payment_data = {
        'card_number': '****************',
        'expiry': '12/25',
        'cvv': '123',
        'cardholder': 'John Doe'
    }
    
    # Сохраняем данные
    success = await save_user_payment_data(
        test_user_id, 
        'yookassa', 
        test_payment_data
    )
    
    if success:
        print("✅ Stored user payment data")
    else:
        print("❌ Failed to store user payment data")
        return False
    
    # Получаем данные
    user_data = await get_user_payment_data(test_user_id, 'yookassa')
    
    if user_data:
        print(f"✅ Retrieved {len(user_data)} payment records")
        for record in user_data:
            print(f"   Record ID: {record['id']}")
            print(f"   Method: {record['payment_method']}")
            print(f"   Created: {record['created_at']}")
            # Не выводим сами данные в логи для безопасности
    else:
        print("❌ Failed to retrieve user payment data")
        return False
    
    # Удаляем тестовые данные
    if user_data:
        for record in user_data:
            delete_success = await delete_user_payment_data(test_user_id, record['id'])
            if delete_success:
                print(f"✅ Deleted payment record {record['id']}")
            else:
                print(f"❌ Failed to delete payment record {record['id']}")
    
    return True


async def test_encryption_edge_cases():
    """
    Тестирует граничные случаи шифрования.
    """
    print("\n🧪 Testing encryption edge cases...")
    
    # Тест с пустой строкой
    try:
        await store_encrypted_config('EMPTY_TEST', '', 'Empty value test')
        empty_value = await get_encrypted_config('EMPTY_TEST')
        if empty_value == '':
            print("✅ Empty string encryption works")
        else:
            print(f"❌ Empty string test failed: got '{empty_value}'")
            return False
    except Exception as e:
        print(f"❌ Empty string test failed with exception: {e}")
        return False
    
    # Тест с несуществующим ключом
    non_existent = await get_encrypted_config('NON_EXISTENT_KEY')
    if non_existent is None:
        print("✅ Non-existent key returns None")
    else:
        print(f"❌ Non-existent key test failed: got '{non_existent}'")
        return False
    
    # Тест с Unicode символами
    unicode_value = "Тест с русскими символами и эмодзи 🔐🚀"
    await store_encrypted_config('UNICODE_TEST', unicode_value, 'Unicode test')
    decrypted_unicode = await get_encrypted_config('UNICODE_TEST')
    if decrypted_unicode == unicode_value:
        print("✅ Unicode encryption works")
    else:
        print(f"❌ Unicode test failed")
        return False
    
    return True


async def main():
    """
    Основная функция тестирования.
    """
    print("🧪 Starting encryption system tests...\n")
    
    try:
        # Тестируем шифрование конфигураций
        config_test = await test_config_encryption()
        
        # Тестируем шифрование данных пользователей
        payment_test = await test_user_payment_encryption()
        
        # Тестируем граничные случаи
        edge_cases_test = await test_encryption_edge_cases()
        
        # Общий результат
        if config_test and payment_test and edge_cases_test:
            print("\n🎉 All encryption tests passed!")
            print("✅ System is ready for production use")
            return 0
        else:
            print("\n❌ Some tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"Test suite failed: {e}", exc_info=True)
        print(f"\n💥 Test suite failed with exception: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
