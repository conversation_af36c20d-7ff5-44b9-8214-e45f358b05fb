"""
Скрипт для создания базовых шаблонов уведомлений.

Этот скрипт создает стандартные шаблоны уведомлений в базе данных
для различных типов событий в системе.
"""

import asyncio
import sys
import os

# Добавляем путь к корневой директории проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from db.models_notifications import NotificationTemplate
from schemas.notification_enums import (
    NotificationType, NotificationPriority, NotificationChannel, ABTestVariant
)
from utils.id_generator import generate_id
from utils.logging_config import get_logger

logger = get_logger(__name__)


# Базовые шаблоны уведомлений
NOTIFICATION_TEMPLATES = [
    {
        'name': 'Подписка истекает через 7 дней',
        'notification_type': NotificationType.SUBSCRIPTION_EXPIRING_7_DAYS,
        'priority': NotificationPriority.NORMAL,
        'subject_template': None,
        'body_template': '''⏰ Ваша подписка истекает через {{ days_left }} {{ days_left|pluralize("день,дня,дней") }}!

📅 Дата истечения: {{ expires_at|datetime("%d.%m.%Y в %H:%M") }}

💡 Продлите подписку заранее, чтобы не потерять доступ к VPN.

🔗 [Продлить подписку]({{ renewal_url }})''',
        'variables': {
            'days_left': {'type': 'integer', 'required': True, 'description': 'Количество дней до истечения'},
            'expires_at': {'type': 'datetime', 'required': True, 'description': 'Дата и время истечения'},
            'renewal_url': {'type': 'url', 'required': True, 'description': 'Ссылка для продления'}
        },
        'default_values': {
            'renewal_url': 'https://t.me/your_bot?start=renew'
        }
    },
    {
        'name': 'Подписка истекает через 3 дня',
        'notification_type': NotificationType.SUBSCRIPTION_EXPIRING_3_DAYS,
        'priority': NotificationPriority.NORMAL,
        'subject_template': None,
        'body_template': '''⚠️ Внимание! Ваша подписка истекает через {{ days_left }} {{ days_left|pluralize("день,дня,дней") }}!

📅 Дата истечения: {{ expires_at|datetime("%d.%m.%Y в %H:%M") }}

🚨 Не забудьте продлить подписку, чтобы избежать прерывания сервиса.

🔗 [Продлить подписку]({{ renewal_url }})''',
        'variables': {
            'days_left': {'type': 'integer', 'required': True, 'description': 'Количество дней до истечения'},
            'expires_at': {'type': 'datetime', 'required': True, 'description': 'Дата и время истечения'},
            'renewal_url': {'type': 'url', 'required': True, 'description': 'Ссылка для продления'}
        },
        'default_values': {
            'renewal_url': 'https://t.me/your_bot?start=renew'
        }
    },
    {
        'name': 'Подписка истекает завтра',
        'notification_type': NotificationType.SUBSCRIPTION_EXPIRING_1_DAY,
        'priority': NotificationPriority.HIGH,
        'subject_template': None,
        'body_template': '''🔴 СРОЧНО! Ваша подписка истекает завтра!

📅 Дата истечения: {{ expires_at|datetime("%d.%m.%Y в %H:%M") }}

⚡ Продлите подписку прямо сейчас, чтобы не потерять доступ к VPN!

🔗 [Продлить подписку]({{ renewal_url }})

💬 Если у вас есть вопросы, обратитесь в поддержку.''',
        'variables': {
            'expires_at': {'type': 'datetime', 'required': True, 'description': 'Дата и время истечения'},
            'renewal_url': {'type': 'url', 'required': True, 'description': 'Ссылка для продления'}
        },
        'default_values': {
            'renewal_url': 'https://t.me/your_bot?start=renew'
        }
    },
    {
        'name': 'Подписка истекла',
        'notification_type': NotificationType.SUBSCRIPTION_EXPIRED,
        'priority': NotificationPriority.HIGH,
        'subject_template': None,
        'body_template': '''❌ Ваша подписка истекла

📅 Дата истечения: {{ expires_at|datetime("%d.%m.%Y в %H:%M") }}

🔒 Доступ к VPN приостановлен. Продлите подписку для восстановления сервиса.

🔗 [Продлить подписку]({{ renewal_url }})

💬 Нужна помощь? Обратитесь в поддержку.''',
        'variables': {
            'expires_at': {'type': 'datetime', 'required': True, 'description': 'Дата и время истечения'},
            'renewal_url': {'type': 'url', 'required': True, 'description': 'Ссылка для продления'}
        },
        'default_values': {
            'renewal_url': 'https://t.me/your_bot?start=renew'
        }
    },
    {
        'name': 'Нода недоступна',
        'notification_type': NotificationType.NODE_UNAVAILABLE,
        'priority': NotificationPriority.HIGH,
        'subject_template': None,
        'body_template': '''⚠️ Проблемы с подключением

🌍 Сервер в стране {{ country }} временно недоступен.

🔧 Наши специалисты уже работают над устранением проблемы.

💡 Вы можете переключиться на другой сервер:
🔗 [Выбрать другой сервер]({{ alternative_nodes_url }})

⏱️ Ожидаемое время восстановления: до 30 минут.''',
        'variables': {
            'country': {'type': 'string', 'required': True, 'description': 'Название страны'},
            'alternative_nodes_url': {'type': 'url', 'required': True, 'description': 'Ссылка для выбора альтернативного сервера'}
        },
        'default_values': {
            'alternative_nodes_url': 'https://t.me/your_bot?start=change_node'
        }
    },
    {
        'name': 'Нода восстановлена',
        'notification_type': NotificationType.NODE_RESTORED,
        'priority': NotificationPriority.NORMAL,
        'subject_template': None,
        'body_template': '''✅ Подключение восстановлено

🌍 Сервер в стране {{ country }} снова работает нормально.

🚀 Вы можете продолжить использование VPN без ограничений.

💚 Спасибо за терпение!''',
        'variables': {
            'country': {'type': 'string', 'required': True, 'description': 'Название страны'}
        }
    },
    {
        'name': 'Платеж не прошел',
        'notification_type': NotificationType.PAYMENT_FAILED,
        'priority': NotificationPriority.HIGH,
        'subject_template': None,
        'body_template': '''❌ Ошибка платежа

💳 К сожалению, ваш платеж на сумму {{ amount }} {{ currency }} не был обработан.

🔍 Возможные причины:
• Недостаточно средств на карте
• Карта заблокирована банком
• Технические проблемы платежной системы

🔄 Попробуйте еще раз или используйте другой способ оплаты.

🔗 [Повторить платеж]({{ retry_payment_url }})

💬 Нужна помощь? Обратитесь в поддержку.''',
        'variables': {
            'amount': {'type': 'string', 'required': True, 'description': 'Сумма платежа'},
            'currency': {'type': 'string', 'required': True, 'description': 'Валюта'},
            'retry_payment_url': {'type': 'url', 'required': True, 'description': 'Ссылка для повторного платежа'}
        },
        'default_values': {
            'retry_payment_url': 'https://t.me/your_bot?start=payment'
        }
    },
    {
        'name': 'Платеж успешен',
        'notification_type': NotificationType.PAYMENT_SUCCESS,
        'priority': NotificationPriority.NORMAL,
        'subject_template': None,
        'body_template': '''✅ Платеж успешно обработан

💳 Сумма: {{ amount }} {{ currency }}
📅 Дата: {{ payment_date|datetime("%d.%m.%Y в %H:%M") }}

🎉 Ваша подписка активирована!

📱 Вы можете начать использовать VPN прямо сейчас.

🔗 [Получить конфигурацию]({{ config_url }})

❤️ Спасибо за выбор нашего сервиса!''',
        'variables': {
            'amount': {'type': 'string', 'required': True, 'description': 'Сумма платежа'},
            'currency': {'type': 'string', 'required': True, 'description': 'Валюта'},
            'payment_date': {'type': 'datetime', 'required': True, 'description': 'Дата платежа'},
            'config_url': {'type': 'url', 'required': True, 'description': 'Ссылка для получения конфигурации'}
        },
        'default_values': {
            'config_url': 'https://t.me/your_bot?start=config'
        }
    },
    {
        'name': 'Добро пожаловать',
        'notification_type': NotificationType.WELCOME_MESSAGE,
        'priority': NotificationPriority.NORMAL,
        'subject_template': None,
        'body_template': '''👋 Добро пожаловать, {{ user_name }}!

🎉 Спасибо за регистрацию в нашем VPN сервисе!

🚀 Что вы можете сделать:
• 🆓 Получить тестовый доступ
• 💎 Выбрать премиум подписку
• 🌍 Подключиться к серверам по всему миру

💡 Начните с тестового периода, чтобы оценить качество нашего сервиса.

🔗 [Получить тестовый доступ]({{ test_access_url }})

❓ Есть вопросы? Обратитесь в поддержку.''',
        'variables': {
            'user_name': {'type': 'string', 'required': True, 'description': 'Имя пользователя'},
            'test_access_url': {'type': 'url', 'required': True, 'description': 'Ссылка для получения тестового доступа'}
        },
        'default_values': {
            'test_access_url': 'https://t.me/your_bot?start=test'
        }
    }
]


async def create_notification_templates():
    """Создает базовые шаблоны уведомлений в базе данных."""

    # Получаем URL базы данных из glv.config
    try:
        import glv
        database_url = glv.config['DB_URL'] + "?async_fallback=True"
    except (ImportError, KeyError):
        # Fallback на переменную окружения
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            logger.error("DATABASE_URL not found in glv.config or environment variables")
            return False
    
    # Создаем подключение к базе данных
    engine = create_async_engine(database_url)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as session:
            created_count = 0
            
            for template_data in NOTIFICATION_TEMPLATES:
                # Проверяем, существует ли уже шаблон для этого типа
                from sqlalchemy import select
                existing = await session.execute(
                    select(NotificationTemplate.id).where(
                        (NotificationTemplate.notification_type == template_data['notification_type']) &
                        (NotificationTemplate.name == template_data['name'])
                    )
                )

                if existing.scalar_one_or_none():
                    logger.info(f"Template '{template_data['name']}' already exists, skipping")
                    continue
                
                # Создаем новый шаблон
                template = NotificationTemplate(
                    id=generate_id(),
                    name=template_data['name'],
                    description=f"Базовый шаблон для {template_data['notification_type'].value}",
                    notification_type=template_data['notification_type'],
                    priority=template_data['priority'],
                    channel=NotificationChannel.TELEGRAM,
                    subject_template=template_data.get('subject_template'),
                    body_template=template_data['body_template'],
                    variables=template_data.get('variables', {}),
                    default_values=template_data.get('default_values', {}),
                    ab_test_variant=ABTestVariant.CONTROL,
                    ab_test_weight=100,
                    is_active=True,
                    created_by='system_script'
                )
                
                session.add(template)
                created_count += 1
                logger.info(f"Created template: {template_data['name']}")
            
            await session.commit()
            logger.info(f"Successfully created {created_count} notification templates")
            return True
            
    except Exception as e:
        logger.error(f"Error creating notification templates: {e}")
        return False
    finally:
        await engine.dispose()


async def main():
    """Главная функция скрипта."""
    logger.info("Starting notification templates creation...")
    
    success = await create_notification_templates()
    
    if success:
        logger.info("✅ Notification templates created successfully!")
        return 0
    else:
        logger.error("❌ Failed to create notification templates")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
