#!/usr/bin/env python3
"""
Скрипт для настройки нод Mar<PERSON>ban в базе данных бота.
Создает страны, ноды и inbounds согласно вашей инфраструктуре.
"""

import asyncio
import sys
import os
from typing import List, Dict, Any

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.database import get_session
from repositories.country_node_repository import (
    CountryRepository, MarzbanNodeRepository, MarzbanInboundRepository
)
from db.models_countries_nodes import (
    Country, MarzbanNode, MarzbanInbound, NodeStatus, 
    InboundProtocol, SecurityType, NetworkType
)
from utils.logging_config import get_logger

logger = get_logger(__name__)


async def create_countries() -> Dict[str, Country]:
    """Создает страны в базе данных."""
    countries_data = [
        {
            'id': 'DE',
            'name_en': 'Germany',
            'name_ru': 'Германия',
            'iso_code': 'DE',
            'flag': '🇩🇪',
            'priority': 1,
            'is_active': True
        },
        {
            'id': 'NL',
            'name_en': 'Netherlands',
            'name_ru': 'Нидерланды',
            'iso_code': 'NL',
            'flag': '🇳🇱',
            'priority': 2,
            'is_active': True
        },
        {
            'id': 'US',
            'name_en': 'United States',
            'name_ru': 'США',
            'iso_code': 'US',
            'flag': '🇺🇸',
            'priority': 3,
            'is_active': True
        }
    ]
    
    countries = {}
    
    async with get_session() as session:
        country_repo = CountryRepository(session)
        
        for country_data in countries_data:
            # Проверяем, существует ли страна
            existing = await country_repo.get_by_id(country_data['id'])
            if existing:
                logger.info(f"Country {country_data['name_ru']} already exists")
                countries[country_data['id']] = existing
                continue
            
            country = Country(**country_data)
            created_country = await country_repo.create(country)
            countries[country_data['id']] = created_country
            logger.info(f"Created country: {created_country.name_ru}")
    
    return countries


async def create_your_marzban_nodes(countries: Dict[str, Country]) -> List[MarzbanNode]:
    """Создает ваши ноды Marzban согласно инфраструктуре."""
    
    # Конфигурация ваших нод
    nodes_data = [
        {
            'id': 'germany-node-1',
            'name': 'Germany Frankfurt #1',
            'country_id': 'DE',
            'city': 'Frankfurt',
            'host': '************',
            'port': 12000,  # Порт REALITY inbound для клиентов
            'api_port': 62050,  # SERVICE_PORT для управления нодой
            'max_users': 1000,
            'current_users': 0,
            'status': NodeStatus.CONNECTED,
            'is_active': True,
            'cpu_usage': 15.0,
            'memory_usage': 45.0,
            'config': {
                'reality_destination': 'deb.debian.org:443',
                'reality_server_names': ['deb.debian.org'],
                'protocols': ['vless'],
                'haproxy_enabled': False,  # Прямое подключение к порту 12000
                'description': 'VLESS TCP REALITY node in Germany'
            }
        },
        # Добавьте дополнительные ноды по мере необходимости
        {
            'id': 'netherlands-node-1',
            'name': 'Netherlands Amsterdam #1',
            'country_id': 'NL',
            'city': 'Amsterdam',
            'host': 'your-netherlands-server-ip',  # Замените на реальный IP
            'port': 12000,
            'api_port': 62050,
            'max_users': 1000,
            'current_users': 0,
            'status': NodeStatus.DISCONNECTED,  # Пока не настроена
            'is_active': False,
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'config': {
                'reality_destination': 'deb.debian.org:443',
                'reality_server_names': ['deb.debian.org'],
                'protocols': ['vless'],
                'haproxy_enabled': False,
                'description': 'VLESS TCP REALITY node in Netherlands (not configured yet)'
            }
        }
    ]
    
    nodes = []
    
    async with get_session() as session:
        node_repo = MarzbanNodeRepository(session)
        
        for node_data in nodes_data:
            # Проверяем, существует ли нода
            existing = await node_repo.get_by_id(node_data['id'])
            if existing:
                logger.info(f"Node {node_data['name']} already exists")
                nodes.append(existing)
                continue
            
            # Проверяем, что страна существует
            if node_data['country_id'] not in countries:
                logger.warning(f"Country {node_data['country_id']} not found, skipping node {node_data['name']}")
                continue
            
            node = MarzbanNode(**node_data)
            created_node = await node_repo.create(node)
            nodes.append(created_node)
            logger.info(f"Created node: {created_node.name}")
    
    return nodes


async def create_inbounds_for_nodes(nodes: List[MarzbanNode]) -> List[MarzbanInbound]:
    """Создает inbounds для нод согласно вашей конфигурации VLESS TCP REALITY."""
    
    inbounds = []
    
    async with get_session() as session:
        inbound_repo = MarzbanInboundRepository(session)
        
        for node in nodes:
            # Создаем VLESS TCP REALITY inbound для каждой ноды
            inbound_data = {
                'id': f'{node.id}-vless-reality',
                'node_id': node.id,
                'tag': f'VLESS_TCP_REALITY_{node.id.upper()}',
                'protocol': InboundProtocol.VLESS,
                'port': node.port,  # 12000
                'listen': '127.0.0.1',  # Локальный адрес для HAProxy или прямого подключения
                'security': SecurityType.REALITY,
                'network': NetworkType.TCP,
                'is_active': node.is_active,
                'config': {
                    'reality_settings': {
                        'dest': 'deb.debian.org:443',
                        'server_names': ['deb.debian.org'],
                        'private_key': 'YOUR_REALITY_PRIVATE_KEY_HERE',  # Замените на реальный ключ
                        'short_ids': ['YOUR_SHORT_ID_HERE']  # Замените на реальный short ID
                    },
                    'tcp_settings': {
                        'accept_proxy_protocol': True  # Для работы с HAProxy
                    },
                    'sniffing': {
                        'enabled': True,
                        'dest_override': ['http', 'tls']
                    }
                }
            }
            
            # Проверяем, существует ли inbound
            existing = await inbound_repo.get_by_id(inbound_data['id'])
            if existing:
                logger.info(f"Inbound {inbound_data['tag']} already exists")
                inbounds.append(existing)
                continue
            
            inbound = MarzbanInbound(**inbound_data)
            created_inbound = await inbound_repo.create(inbound)
            inbounds.append(created_inbound)
            logger.info(f"Created inbound: {created_inbound.tag} for node {node.name}")
    
    return inbounds


async def setup_marzban_infrastructure():
    """Основная функция настройки инфраструктуры Marzban."""
    logger.info("🚀 Starting Marzban infrastructure setup...")
    
    try:
        # 1. Создаем страны
        logger.info("📍 Creating countries...")
        countries = await create_countries()
        logger.info(f"✅ Countries setup complete: {len(countries)} countries")
        
        # 2. Создаем ноды
        logger.info("🖥️ Creating Marzban nodes...")
        nodes = await create_your_marzban_nodes(countries)
        logger.info(f"✅ Nodes setup complete: {len(nodes)} nodes")
        
        # 3. Создаем inbounds
        logger.info("🔌 Creating inbounds for nodes...")
        inbounds = await create_inbounds_for_nodes(nodes)
        logger.info(f"✅ Inbounds setup complete: {len(inbounds)} inbounds")
        
        # 4. Выводим сводку
        logger.info("\n" + "="*60)
        logger.info("🎉 MARZBAN INFRASTRUCTURE SETUP COMPLETE!")
        logger.info("="*60)
        
        logger.info(f"📊 Summary:")
        logger.info(f"  - Countries: {len(countries)}")
        logger.info(f"  - Nodes: {len(nodes)}")
        logger.info(f"  - Inbounds: {len(inbounds)}")
        
        logger.info(f"\n🌍 Countries created:")
        for country in countries.values():
            logger.info(f"  - {country.flag} {country.name_ru} ({country.id})")
        
        logger.info(f"\n🖥️ Nodes created:")
        for node in nodes:
            status_emoji = "🟢" if node.status == NodeStatus.CONNECTED else "🔴"
            logger.info(f"  - {status_emoji} {node.name} ({node.host}:{node.port})")
        
        logger.info(f"\n🔌 Inbounds created:")
        for inbound in inbounds:
            active_emoji = "✅" if inbound.is_active else "❌"
            logger.info(f"  - {active_emoji} {inbound.tag} ({inbound.protocol.value})")
        
        logger.info("\n" + "="*60)
        logger.info("⚠️  IMPORTANT NEXT STEPS:")
        logger.info("="*60)
        logger.info("1. 🔑 Update REALITY private keys and short IDs in the database")
        logger.info("2. 🔧 Configure actual node IPs and verify connectivity")
        logger.info("3. 🧪 Test node selection and user creation")
        logger.info("4. 📊 Set up monitoring for node health checks")
        logger.info("5. 🚀 Start the bot and test country selection feature")
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        raise


async def update_reality_keys():
    """Обновляет REALITY ключи для нод (запустите отдельно после получения ключей)."""
    logger.info("🔑 Updating REALITY keys...")
    
    # Пример обновления ключей - замените на ваши реальные ключи
    keys_config = {
        'germany-node-1': {
            'private_key': 'YOUR_GERMANY_NODE_PRIVATE_KEY',
            'short_ids': ['a1b2c3d4', 'e5f6g7h8']
        },
        'netherlands-node-1': {
            'private_key': 'YOUR_NETHERLANDS_NODE_PRIVATE_KEY', 
            'short_ids': ['i9j0k1l2', 'm3n4o5p6']
        }
    }
    
    async with get_session() as session:
        inbound_repo = MarzbanInboundRepository(session)
        
        for node_id, keys in keys_config.items():
            inbound_id = f'{node_id}-vless-reality'
            inbound = await inbound_repo.get_by_id(inbound_id)
            
            if inbound:
                # Обновляем конфигурацию
                config = inbound.config or {}
                if 'reality_settings' not in config:
                    config['reality_settings'] = {}
                
                config['reality_settings']['private_key'] = keys['private_key']
                config['reality_settings']['short_ids'] = keys['short_ids']
                
                inbound.config = config
                await inbound_repo.update(inbound)
                logger.info(f"✅ Updated REALITY keys for {inbound.tag}")
            else:
                logger.warning(f"⚠️ Inbound {inbound_id} not found")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Setup Marzban infrastructure")
    parser.add_argument('--update-keys', action='store_true', 
                       help='Update REALITY keys only')
    
    args = parser.parse_args()
    
    if args.update_keys:
        asyncio.run(update_reality_keys())
    else:
        asyncio.run(setup_marzban_infrastructure())
