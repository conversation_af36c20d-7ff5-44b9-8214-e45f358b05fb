#!/usr/bin/env python3
"""
Скрипт для миграции чувствительных данных в зашифрованное хранилище.
Переносит API ключи и другие секретные данные из переменных окружения в зашифрованную БД.
"""

import asyncio
import os
import sys
from pathlib import Path

# Добавляем путь к проекту
sys.path.append(str(Path(__file__).parent.parent))

from db.encryption_methods import store_encrypted_config, get_encrypted_config
from utils.logging_config import get_logger
import glv

logger = get_logger(__name__)


class SensitiveDataMigrator:
    """
    Класс для миграции чувствительных данных в зашифрованное хранилище.
    """
    
    # Список чувствительных конфигураций для миграции
    SENSITIVE_CONFIGS = {
        'TELEGRAM_BOT_TOKEN': {
            'env_var': 'TELEGRAM_BOT_TOKEN',
            'description': 'Токен Telegram бота'
        },
        'YOOKASSA_SHOP_ID': {
            'env_var': 'YOOKASSA_SHOP_ID',
            'description': 'ID магазина YooKassa'
        },
        'YOOKASSA_SECRET_KEY': {
            'env_var': 'YOOKASSA_SECRET_KEY',
            'description': 'Секретный ключ YooKassa'
        },
        'CRYPTOMUS_MERCHANT_ID': {
            'env_var': 'CRYPTOMUS_MERCHANT_ID',
            'description': 'ID мерчанта Cryptomus'
        },
        'CRYPTOMUS_API_KEY': {
            'env_var': 'CRYPTOMUS_API_KEY',
            'description': 'API ключ Cryptomus'
        },
        'MARZBAN_USERNAME': {
            'env_var': 'MARZBAN_USERNAME',
            'description': 'Имя пользователя Marzban'
        },
        'MARZBAN_PASSWORD': {
            'env_var': 'MARZBAN_PASSWORD',
            'description': 'Пароль Marzban'
        },
        'MARZBAN_URL': {
            'env_var': 'MARZBAN_URL',
            'description': 'URL Marzban панели'
        }
    }
    
    def __init__(self):
        self.migrated_count = 0
        self.skipped_count = 0
        self.error_count = 0
    
    async def migrate_config(self, key_name: str, config_info: dict) -> bool:
        """
        Мигрирует одну конфигурацию в зашифрованное хранилище.
        
        Args:
            key_name: Имя ключа конфигурации
            config_info: Информация о конфигурации
            
        Returns:
            True если успешно мигрировано
        """
        env_var = config_info['env_var']
        description = config_info['description']
        
        try:
            # Проверяем, есть ли уже такая конфигурация в зашифрованном хранилище
            existing_value = await get_encrypted_config(key_name)
            if existing_value:
                logger.info(f"Config {key_name} already exists in encrypted storage, skipping")
                self.skipped_count += 1
                return True
            
            # Получаем значение из переменной окружения
            env_value = os.getenv(env_var)
            if not env_value:
                logger.warning(f"Environment variable {env_var} not found, skipping {key_name}")
                self.skipped_count += 1
                return True
            
            # Сохраняем в зашифрованном виде
            success = await store_encrypted_config(key_name, env_value, description)
            if success:
                logger.info(f"Successfully migrated {key_name} to encrypted storage")
                self.migrated_count += 1
                return True
            else:
                logger.error(f"Failed to migrate {key_name}")
                self.error_count += 1
                return False
                
        except Exception as e:
            logger.error(f"Error migrating {key_name}: {e}", exc_info=True)
            self.error_count += 1
            return False
    
    async def migrate_all(self) -> bool:
        """
        Мигрирует все чувствительные конфигурации.
        
        Returns:
            True если все миграции прошли успешно
        """
        logger.info("Starting migration of sensitive data to encrypted storage")
        
        success = True
        for key_name, config_info in self.SENSITIVE_CONFIGS.items():
            result = await self.migrate_config(key_name, config_info)
            if not result:
                success = False
        
        # Выводим статистику
        logger.info(f"Migration completed:")
        logger.info(f"  Migrated: {self.migrated_count}")
        logger.info(f"  Skipped: {self.skipped_count}")
        logger.info(f"  Errors: {self.error_count}")
        
        return success and self.error_count == 0
    
    async def verify_migration(self) -> bool:
        """
        Проверяет, что все данные корректно мигрированы.
        
        Returns:
            True если все данные доступны
        """
        logger.info("Verifying migration...")
        
        verification_success = True
        for key_name, config_info in self.SENSITIVE_CONFIGS.items():
            try:
                # Проверяем, что данные можно получить из зашифрованного хранилища
                decrypted_value = await get_encrypted_config(key_name)
                env_value = os.getenv(config_info['env_var'])
                
                if env_value and decrypted_value:
                    if decrypted_value == env_value:
                        logger.info(f"✅ {key_name}: Migration verified successfully")
                    else:
                        logger.error(f"❌ {key_name}: Decrypted value doesn't match original")
                        verification_success = False
                elif env_value and not decrypted_value:
                    logger.error(f"❌ {key_name}: Original exists but encrypted version not found")
                    verification_success = False
                elif not env_value and decrypted_value:
                    logger.info(f"✅ {key_name}: Only encrypted version exists (OK)")
                else:
                    logger.warning(f"⚠️ {key_name}: Neither original nor encrypted version found")
                    
            except Exception as e:
                logger.error(f"❌ {key_name}: Verification failed: {e}")
                verification_success = False
        
        return verification_success


async def main():
    """
    Основная функция для запуска миграции.
    """
    print("🔐 Starting sensitive data migration to encrypted storage...")
    
    migrator = SensitiveDataMigrator()
    
    try:
        # Выполняем миграцию
        migration_success = await migrator.migrate_all()
        
        if migration_success:
            print("✅ Migration completed successfully!")
            
            # Проверяем миграцию
            verification_success = await migrator.verify_migration()
            
            if verification_success:
                print("✅ Migration verification passed!")
                print("\n📋 Next steps:")
                print("1. Update your application code to use encrypted config functions")
                print("2. Consider removing sensitive data from environment variables")
                print("3. Test the application with encrypted data")
                return 0
            else:
                print("❌ Migration verification failed!")
                return 1
        else:
            print("❌ Migration failed!")
            return 1
            
    except Exception as e:
        logger.error(f"Migration script failed: {e}", exc_info=True)
        print(f"❌ Migration script failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
