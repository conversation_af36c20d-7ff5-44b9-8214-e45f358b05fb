#!/usr/bin/env python3
"""
Скрипт для тестирования сервисов предпочтений пользователей и выбора нод.
Проверяет работу UserPreferenceService и NodeSelectionService.
"""

import asyncio
import sys
import os
from datetime import datetime

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.base import get_session
from repositories.country_node_repository import (
    CountryRepository, MarzbanNodeRepository, MarzbanInboundRepository,
    UserNodePreferenceRepository, NodeStatisticsRepository
)
from services.user_preference_service import UserPreferenceService, UserPreferences
from services.node_selection_service import NodeSelectionService, SelectionStrategy
from db.models_countries_nodes import (
    Country, MarzbanNode, MarzbanInbound, UserNodePreference,
    NodeStatus, InboundProtocol, SecurityType, NetworkType
)
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="test-preference-services", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class PreferenceServiceTester:
    """Тестер для сервисов предпочтений и выбора нод."""
    
    def __init__(self):
        self.test_results = []
        self.test_user_id = 987654321
    
    async def test_user_preference_service(self):
        """Тест UserPreferenceService."""
        try:
            async with get_session() as session:
                # Создаем repositories
                preference_repo = UserNodePreferenceRepository(session)
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                
                # Создаем сервис
                service = UserPreferenceService(preference_repo, country_repo, node_repo)
                
                # Тест 1: Получение дефолтных предпочтений
                preferences = await service.get_user_preferences(self.test_user_id)
                
                if preferences.tg_id == self.test_user_id and preferences.auto_select_optimal:
                    logger.info("✅ Default preferences retrieved successfully")
                    self.test_results.append(("Get Default Preferences", True, "Default preferences work"))
                else:
                    self.test_results.append(("Get Default Preferences", False, "Default preferences failed"))
                
                # Тест 2: Обновление предпочтений
                preferences.preferred_countries = ["DE", "NL"]
                preferences.preferred_protocols = ["vless", "shadowsocks"]
                preferences.prefer_low_latency = True
                
                success = await service.update_user_preferences(preferences)
                
                if success:
                    logger.info("✅ User preferences updated successfully")
                    self.test_results.append(("Update Preferences", True, "Preferences updated"))
                else:
                    self.test_results.append(("Update Preferences", False, "Failed to update preferences"))
                
                # Тест 3: Получение обновленных предпочтений
                updated_preferences = await service.get_user_preferences(self.test_user_id)
                
                if (updated_preferences.preferred_countries == ["DE", "NL"] and 
                    updated_preferences.preferred_protocols == ["vless", "shadowsocks"]):
                    logger.info("✅ Updated preferences retrieved correctly")
                    self.test_results.append(("Get Updated Preferences", True, "Updated preferences correct"))
                else:
                    self.test_results.append(("Get Updated Preferences", False, "Updated preferences incorrect"))
                
                # Тест 4: Получение доступных стран
                countries = await service.get_available_countries()
                
                if len(countries) > 0:
                    logger.info(f"✅ Found {len(countries)} available countries")
                    self.test_results.append(("Get Available Countries", True, f"{len(countries)} countries found"))
                else:
                    self.test_results.append(("Get Available Countries", False, "No countries found"))
                
                # Тест 5: Получение рекомендуемых стран
                recommended = await service.get_recommended_countries(self.test_user_id, limit=3)
                
                if len(recommended) > 0:
                    logger.info(f"✅ Found {len(recommended)} recommended countries")
                    self.test_results.append(("Get Recommended Countries", True, f"{len(recommended)} recommended"))
                else:
                    self.test_results.append(("Get Recommended Countries", False, "No recommendations"))
                
        except Exception as e:
            logger.error(f"❌ Error testing UserPreferenceService: {e}")
            self.test_results.append(("UserPreferenceService", False, str(e)))
    
    async def test_node_selection_service(self):
        """Тест NodeSelectionService."""
        try:
            async with get_session() as session:
                # Создаем repositories
                node_repo = MarzbanNodeRepository(session)
                inbound_repo = MarzbanInboundRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                preference_repo = UserNodePreferenceRepository(session)
                country_repo = CountryRepository(session)
                
                # Создаем сервисы
                preference_service = UserPreferenceService(preference_repo, country_repo, node_repo)
                selection_service = NodeSelectionService(node_repo, inbound_repo, stats_repo, preference_service)
                
                # Тест 1: Выбор оптимальной ноды
                result = await selection_service.select_optimal_node(
                    tg_id=self.test_user_id,
                    strategy=SelectionStrategy.HYBRID
                )
                
                if result.success and result.selected_node:
                    logger.info(f"✅ Selected optimal node: {result.selected_node.name}")
                    logger.info(f"   Reasons: {', '.join(result.selection_reasons)}")
                    logger.info(f"   Available inbounds: {len(result.available_inbounds)}")
                    logger.info(f"   Fallback nodes: {len(result.fallback_nodes)}")
                    self.test_results.append(("Select Optimal Node", True, f"Selected {result.selected_node.name}"))
                else:
                    logger.warning(f"❌ Failed to select optimal node: {result.selection_reasons}")
                    self.test_results.append(("Select Optimal Node", False, "No optimal node found"))
                
                # Тест 2: Выбор ноды по стране
                result_de = await selection_service.select_node_by_country(
                    tg_id=self.test_user_id,
                    country_id="DE",
                    strategy=SelectionStrategy.LOAD_BALANCED
                )
                
                if result_de.success and result_de.selected_node:
                    logger.info(f"✅ Selected node in Germany: {result_de.selected_node.name}")
                    self.test_results.append(("Select Node by Country", True, f"Selected {result_de.selected_node.name} in DE"))
                else:
                    logger.warning(f"❌ Failed to select node in Germany: {result_de.selection_reasons}")
                    self.test_results.append(("Select Node by Country", False, "No node found in DE"))
                
                # Тест 3: Тестирование разных стратегий
                strategies = [
                    SelectionStrategy.LOAD_BALANCED,
                    SelectionStrategy.PERFORMANCE,
                    SelectionStrategy.GEOGRAPHIC,
                    SelectionStrategy.RANDOM
                ]
                
                strategy_results = []
                for strategy in strategies:
                    result = await selection_service.select_optimal_node(
                        tg_id=self.test_user_id,
                        strategy=strategy
                    )
                    
                    if result.success:
                        strategy_results.append(f"{strategy.value}: {result.selected_node.name}")
                    else:
                        strategy_results.append(f"{strategy.value}: failed")
                
                logger.info(f"✅ Strategy test results: {', '.join(strategy_results)}")
                self.test_results.append(("Test Strategies", True, f"Tested {len(strategies)} strategies"))
                
        except Exception as e:
            logger.error(f"❌ Error testing NodeSelectionService: {e}")
            self.test_results.append(("NodeSelectionService", False, str(e)))
    
    async def test_integration(self):
        """Тест интеграции сервисов."""
        try:
            async with get_session() as session:
                # Создаем все repositories
                preference_repo = UserNodePreferenceRepository(session)
                country_repo = CountryRepository(session)
                node_repo = MarzbanNodeRepository(session)
                inbound_repo = MarzbanInboundRepository(session)
                stats_repo = NodeStatisticsRepository(session)
                
                # Создаем сервисы
                preference_service = UserPreferenceService(preference_repo, country_repo, node_repo)
                selection_service = NodeSelectionService(node_repo, inbound_repo, stats_repo, preference_service)
                
                # Тест полного цикла: установка предпочтений -> выбор ноды
                
                # 1. Устанавливаем предпочтения
                preferences = UserPreferences(
                    tg_id=self.test_user_id,
                    preferred_countries=["NL", "DE"],
                    preferred_protocols=["vless"],
                    auto_select_optimal=True,
                    prefer_low_latency=True,
                    prefer_low_load=True
                )
                
                await preference_service.update_user_preferences(preferences)
                
                # 2. Выбираем ноду с учетом предпочтений
                result = await selection_service.select_optimal_node(self.test_user_id)
                
                if result.success and result.selected_node:
                    # 3. Проверяем, что выбранная нода соответствует предпочтениям
                    selected_country = result.selected_node.country_id
                    
                    if selected_country in preferences.preferred_countries:
                        logger.info(f"✅ Integration test passed: selected node {result.selected_node.name} in preferred country {selected_country}")
                        self.test_results.append(("Integration Test", True, f"Node in preferred country {selected_country}"))
                    else:
                        logger.info(f"✅ Integration test passed: selected fallback node {result.selected_node.name} in {selected_country}")
                        self.test_results.append(("Integration Test", True, f"Fallback node in {selected_country}"))
                else:
                    self.test_results.append(("Integration Test", False, "Failed to select node"))
                
        except Exception as e:
            logger.error(f"❌ Error in integration test: {e}")
            self.test_results.append(("Integration Test", False, str(e)))
    
    def print_results(self):
        """Выводит результаты тестов."""
        logger.info("\n" + "="*60)
        logger.info("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ СЕРВИСОВ ПРЕДПОЧТЕНИЙ")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {details}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📈 ИТОГО: {passed}/{total} тестов пройдено")
        
        if passed == total:
            logger.info("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
            return True
        else:
            logger.error(f"💥 {total - passed} тестов провалено")
            return False


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Preference Services Testing")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = PreferenceServiceTester()
    
    try:
        # Запускаем все тесты
        await tester.test_user_preference_service()
        await tester.test_node_selection_service()
        await tester.test_integration()
        
        # Выводим результаты
        success = tester.print_results()
        
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success
        
    except Exception as e:
        logger.error(f"💥 Testing failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
