#!/usr/bin/env python3
"""
Скрипт для тестирования моделей стран и нод.
Создает тестовые данные и проверяет связи между моделями.
"""

import asyncio
import sys
import os
from datetime import datetime

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.base import get_session
from db.models_countries_nodes import (
    Country, MarzbanNode, MarzbanInbound, 
    UserNodePreference, NodeStatistics,
    NodeStatus, InboundProtocol, SecurityType, NetworkType
)
from sqlalchemy import select, func
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="test-countries-nodes", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class CountriesNodesModelTester:
    """Тестер для моделей стран и нод."""
    
    def __init__(self):
        self.test_results = []
    
    async def test_create_countries(self):
        """Тест создания стран."""
        try:
            async with get_session() as session:
                # Создаем тестовые страны
                countries = [
                    Country(
                        id="DE",
                        name="Germany",
                        name_ru="Германия",
                        flag="🇩🇪",
                        continent="Europe",
                        region="Western Europe",
                        priority=10
                    ),
                    Country(
                        id="NL",
                        name="Netherlands",
                        name_ru="Нидерланды",
                        flag="🇳🇱",
                        continent="Europe",
                        region="Western Europe",
                        priority=20
                    ),
                    Country(
                        id="US",
                        name="United States",
                        name_ru="США",
                        flag="🇺🇸",
                        continent="North America",
                        region="North America",
                        priority=30
                    )
                ]
                
                for country in countries:
                    session.add(country)
                
                await session.commit()
                
                # Проверяем количество созданных стран
                result = await session.execute(select(func.count(Country.id)))
                count = result.scalar()
                
                logger.info(f"✅ Created {len(countries)} countries, total in DB: {count}")
                self.test_results.append(("Create Countries", True, f"{count} countries created"))
                
        except Exception as e:
            logger.error(f"❌ Error creating countries: {e}")
            self.test_results.append(("Create Countries", False, str(e)))
    
    async def test_create_nodes(self):
        """Тест создания нод."""
        try:
            async with get_session() as session:
                # Создаем тестовые ноды
                nodes = [
                    MarzbanNode(
                        id="de-fra-01",
                        name="Germany Frankfurt #1",
                        country_id="DE",
                        city="Frankfurt",
                        node_name="de-fra-01",
                        node_address="de-fra-01.example.com",
                        service_port=62050,
                        api_port=62051,
                        status=NodeStatus.CONNECTED,
                        max_users=1000,
                        current_users=150
                    ),
                    MarzbanNode(
                        id="nl-ams-01",
                        name="Netherlands Amsterdam #1",
                        country_id="NL",
                        city="Amsterdam",
                        node_name="nl-ams-01",
                        node_address="nl-ams-01.example.com",
                        service_port=62050,
                        api_port=62051,
                        status=NodeStatus.CONNECTED,
                        max_users=800,
                        current_users=200
                    ),
                    MarzbanNode(
                        id="us-ny-01",
                        name="United States New York #1",
                        country_id="US",
                        city="New York",
                        node_name="us-ny-01",
                        node_address="us-ny-01.example.com",
                        service_port=62050,
                        api_port=62051,
                        status=NodeStatus.DISCONNECTED,
                        max_users=1200,
                        current_users=0
                    )
                ]
                
                for node in nodes:
                    session.add(node)
                
                await session.commit()
                
                # Проверяем количество созданных нод
                result = await session.execute(select(func.count(MarzbanNode.id)))
                count = result.scalar()
                
                logger.info(f"✅ Created {len(nodes)} nodes, total in DB: {count}")
                self.test_results.append(("Create Nodes", True, f"{count} nodes created"))
                
        except Exception as e:
            logger.error(f"❌ Error creating nodes: {e}")
            self.test_results.append(("Create Nodes", False, str(e)))
    
    async def test_create_inbounds(self):
        """Тест создания inbounds."""
        try:
            async with get_session() as session:
                # Создаем тестовые inbounds
                inbounds = [
                    MarzbanInbound(
                        id="vless-reality-de-01",
                        tag="VLESS TCP REALITY DE",
                        protocol=InboundProtocol.VLESS,
                        port=2040,
                        network_type=NetworkType.TCP,
                        security_type=SecurityType.REALITY,
                        primary_node_id="de-fra-01",
                        protocol_settings={"flow": ""},
                        reality_settings={
                            "dest": "www.microsoft.com:443",
                            "serverNames": ["www.microsoft.com"],
                            "privateKey": "cOATe3zm0zoc5lwQwkoKk_gRWqCzdWNivaPRFlapS24",
                            "shortIds": ["91e743eac97cbf9f"]
                        }
                    ),
                    MarzbanInbound(
                        id="shadowsocks-tcp-de-01",
                        tag="Shadowsocks TCP DE",
                        protocol=InboundProtocol.SHADOWSOCKS,
                        port=1080,
                        network_type=NetworkType.TCP,
                        security_type=SecurityType.NONE,
                        primary_node_id="de-fra-01",
                        protocol_settings={"method": "chacha20-ietf-poly1305"}
                    ),
                    MarzbanInbound(
                        id="vless-reality-nl-01",
                        tag="VLESS TCP REALITY NL",
                        protocol=InboundProtocol.VLESS,
                        port=2040,
                        network_type=NetworkType.TCP,
                        security_type=SecurityType.REALITY,
                        primary_node_id="nl-ams-01",
                        protocol_settings={"flow": ""},
                        reality_settings={
                            "dest": "www.cloudflare.com:443",
                            "serverNames": ["www.cloudflare.com"],
                            "privateKey": "aOATe3zm0zoc5lwQwkoKk_gRWqCzdWNivaPRFlapS25",
                            "shortIds": ["92e743eac97cbf9g"]
                        }
                    )
                ]
                
                for inbound in inbounds:
                    session.add(inbound)
                
                await session.commit()
                
                # Проверяем количество созданных inbounds
                result = await session.execute(select(func.count(MarzbanInbound.id)))
                count = result.scalar()
                
                logger.info(f"✅ Created {len(inbounds)} inbounds, total in DB: {count}")
                self.test_results.append(("Create Inbounds", True, f"{count} inbounds created"))
                
        except Exception as e:
            logger.error(f"❌ Error creating inbounds: {e}")
            self.test_results.append(("Create Inbounds", False, str(e)))
    
    async def test_relationships(self):
        """Тест связей между моделями."""
        try:
            async with get_session() as session:
                # Тестируем связь Country -> Nodes
                result = await session.execute(
                    select(Country).where(Country.id == "DE")
                )
                germany = result.scalar_one_or_none()
                
                if germany:
                    # Загружаем связанные ноды
                    await session.refresh(germany, ["nodes"])
                    nodes_count = len(germany.nodes)
                    logger.info(f"✅ Germany has {nodes_count} nodes")
                    
                    # Тестируем связь Node -> Inbounds
                    if germany.nodes:
                        node = germany.nodes[0]
                        await session.refresh(node, ["inbounds"])
                        inbounds_count = len(node.inbounds)
                        logger.info(f"✅ Node {node.name} has {inbounds_count} inbounds")
                        
                        self.test_results.append(("Test Relationships", True, f"Country->Nodes: {nodes_count}, Node->Inbounds: {inbounds_count}"))
                    else:
                        self.test_results.append(("Test Relationships", False, "No nodes found for Germany"))
                else:
                    self.test_results.append(("Test Relationships", False, "Germany not found"))
                
        except Exception as e:
            logger.error(f"❌ Error testing relationships: {e}")
            self.test_results.append(("Test Relationships", False, str(e)))
    
    async def test_user_preferences(self):
        """Тест предпочтений пользователей."""
        try:
            async with get_session() as session:
                # Создаем тестовые предпочтения пользователя
                preference = UserNodePreference(
                    tg_id=123456789,
                    preferred_countries=["DE", "NL"],
                    preferred_protocols=["vless", "shadowsocks"],
                    auto_select_optimal=True,
                    prefer_low_latency=True,
                    last_selected_country="DE",
                    last_selected_node="de-fra-01"
                )
                
                session.add(preference)
                await session.commit()
                
                # Проверяем созданные предпочтения
                result = await session.execute(
                    select(UserNodePreference).where(UserNodePreference.tg_id == 123456789)
                )
                saved_preference = result.scalar_one_or_none()
                
                if saved_preference:
                    logger.info(f"✅ User preferences saved: countries={saved_preference.preferred_countries}, protocols={saved_preference.preferred_protocols}")
                    self.test_results.append(("User Preferences", True, f"Preferences saved for user {saved_preference.tg_id}"))
                else:
                    self.test_results.append(("User Preferences", False, "Preferences not saved"))
                
        except Exception as e:
            logger.error(f"❌ Error testing user preferences: {e}")
            self.test_results.append(("User Preferences", False, str(e)))
    
    async def test_node_statistics(self):
        """Тест статистики нод."""
        try:
            async with get_session() as session:
                # Создаем тестовую статистику
                stats = [
                    NodeStatistics(
                        node_id="de-fra-01",
                        users_count=150,
                        cpu_usage=45.5,
                        memory_usage=60.2,
                        network_upload_speed=1024000,
                        network_download_speed=2048000,
                        response_time_ms=25,
                        is_online=True
                    ),
                    NodeStatistics(
                        node_id="nl-ams-01",
                        users_count=200,
                        cpu_usage=55.8,
                        memory_usage=70.1,
                        network_upload_speed=1536000,
                        network_download_speed=3072000,
                        response_time_ms=30,
                        is_online=True
                    )
                ]
                
                for stat in stats:
                    session.add(stat)
                
                await session.commit()
                
                # Проверяем количество записей статистики
                result = await session.execute(select(func.count(NodeStatistics.id)))
                count = result.scalar()
                
                logger.info(f"✅ Created {len(stats)} statistics records, total in DB: {count}")
                self.test_results.append(("Node Statistics", True, f"{count} statistics records"))
                
        except Exception as e:
            logger.error(f"❌ Error testing node statistics: {e}")
            self.test_results.append(("Node Statistics", False, str(e)))
    
    def print_results(self):
        """Выводит результаты тестов."""
        logger.info("\n" + "="*60)
        logger.info("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ МОДЕЛЕЙ СТРАН И НОД")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {details}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📈 ИТОГО: {passed}/{total} тестов пройдено")
        
        if passed == total:
            logger.info("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
            return True
        else:
            logger.error(f"💥 {total - passed} тестов провалено")
            return False


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Countries and Nodes Models Testing")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = CountriesNodesModelTester()
    
    try:
        # Запускаем все тесты
        await tester.test_create_countries()
        await tester.test_create_nodes()
        await tester.test_create_inbounds()
        await tester.test_relationships()
        await tester.test_user_preferences()
        await tester.test_node_statistics()
        
        # Выводим результаты
        success = tester.print_results()
        
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success
        
    except Exception as e:
        logger.error(f"💥 Testing failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
