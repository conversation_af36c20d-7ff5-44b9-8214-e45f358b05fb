#!/usr/bin/env python3
"""
Скрипт для тестирования системы алертов VPN бота.
Симулирует различные критические события для проверки работы алертов.
"""

import asyncio
import sys
import os
import time
import logging
from datetime import datetime, timedelta

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from monitoring.alerts import AlertManager, TelegramAlertChannel, Alert, AlertSeverity, AlertRule
from monitoring.alert_rules import create_default_alert_rules, record_payment_error, record_critical_exception, record_general_error
from monitoring.config import load_alert_config, validate_alert_config
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="alert-test", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class AlertTester:
    """Класс для тестирования системы алертов."""
    
    def __init__(self):
        self.alert_manager = AlertManager()
        self.config = load_alert_config()
        self.test_results = []
    
    async def setup(self):
        """Настройка тестового окружения."""
        logger.info("🔧 Setting up alert testing environment...")
        
        # Валидируем конфигурацию
        errors = validate_alert_config(self.config)
        if errors:
            logger.error("❌ Configuration validation failed:")
            for error in errors:
                logger.error(f"  - {error}")
            return False
        
        # Добавляем правила алертов
        rules = create_default_alert_rules()
        for rule in rules.values():
            self.alert_manager.add_rule(rule)
        
        logger.info(f"✅ Added {len(rules)} alert rules")
        
        # Добавляем тестовый канал уведомлений (без реального Telegram бота)
        if self.config.telegram_enabled and self.config.admin_chat_ids:
            logger.info(f"📱 Telegram notifications configured for {len(self.config.admin_chat_ids)} chats")
        else:
            logger.warning("⚠️ Telegram notifications not configured")
        
        return True
    
    async def test_configuration(self):
        """Тестирует конфигурацию алертов."""
        logger.info("🧪 Testing alert configuration...")
        
        try:
            # Проверяем загрузку конфигурации
            config = load_alert_config()
            logger.info(f"✅ Configuration loaded: {config.enabled}")
            
            # Проверяем валидацию
            errors = validate_alert_config(config)
            if errors:
                logger.error("❌ Configuration validation failed")
                for error in errors:
                    logger.error(f"  - {error}")
                return False
            else:
                logger.info("✅ Configuration validation passed")
            
            # Проверяем правила
            rules = create_default_alert_rules()
            logger.info(f"✅ Created {len(rules)} default rules")
            
            self.test_results.append(("Configuration Test", True, "All checks passed"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration test failed: {e}")
            self.test_results.append(("Configuration Test", False, str(e)))
            return False
    
    async def test_alert_creation(self):
        """Тестирует создание алертов."""
        logger.info("🧪 Testing alert creation...")
        
        try:
            # Создаем тестовое правило
            test_rule = AlertRule(
                name="test_alert",
                description="Test alert for validation",
                severity=AlertSeverity.INFO,
                condition=lambda: True,  # Всегда срабатывает
                for_duration=timedelta(seconds=1)
            )
            
            self.alert_manager.add_rule(test_rule)
            
            # Запускаем менеджер алертов
            await self.alert_manager.start(check_interval=2)
            
            # Ждем срабатывания алерта
            await asyncio.sleep(3)
            
            # Проверяем, что алерт создался
            active_alerts = self.alert_manager.get_active_alerts()
            
            if any(alert.name == "test_alert" for alert in active_alerts):
                logger.info("✅ Test alert created successfully")
                self.test_results.append(("Alert Creation", True, "Test alert fired"))
                
                # Останавливаем правило
                self.alert_manager.disable_rule("test_alert")
                await asyncio.sleep(2)
                
                return True
            else:
                logger.error("❌ Test alert was not created")
                self.test_results.append(("Alert Creation", False, "Test alert did not fire"))
                return False
                
        except Exception as e:
            logger.error(f"❌ Alert creation test failed: {e}")
            self.test_results.append(("Alert Creation", False, str(e)))
            return False
        finally:
            await self.alert_manager.stop()
    
    async def test_error_recording(self):
        """Тестирует запись ошибок."""
        logger.info("🧪 Testing error recording...")
        
        try:
            # Записываем различные типы ошибок
            record_payment_error("yookassa")
            record_payment_error("cryptomus")
            record_critical_exception()
            record_general_error()
            
            logger.info("✅ Error recording completed")
            self.test_results.append(("Error Recording", True, "All error types recorded"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Error recording test failed: {e}")
            self.test_results.append(("Error Recording", False, str(e)))
            return False
    
    async def test_log_monitoring(self):
        """Тестирует мониторинг логов."""
        logger.info("🧪 Testing log monitoring...")
        
        try:
            # Генерируем тестовые логи с ошибками
            test_logger = logging.getLogger("test_alerts")
            
            # Различные типы ошибок
            test_logger.error("YooKassa payment failed with error 500")
            test_logger.warning("Cryptomus API timeout occurred")
            test_logger.error("Database connection failed")
            test_logger.critical("Critical exception in payment processing")
            
            logger.info("✅ Test logs generated")
            self.test_results.append(("Log Monitoring", True, "Test logs generated"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Log monitoring test failed: {e}")
            self.test_results.append(("Log Monitoring", False, str(e)))
            return False
    
    async def test_system_metrics(self):
        """Тестирует получение системных метрик."""
        logger.info("🧪 Testing system metrics...")
        
        try:
            from monitoring.alert_rules import SystemMetrics
            
            # Получаем системные метрики
            memory_usage = SystemMetrics.get_memory_usage_percent()
            cpu_usage = SystemMetrics.get_cpu_usage_percent()
            disk_usage = SystemMetrics.get_disk_usage_percent()
            
            logger.info(f"📊 System metrics:")
            logger.info(f"  Memory: {memory_usage:.1f}%")
            logger.info(f"  CPU: {cpu_usage:.1f}%")
            logger.info(f"  Disk: {disk_usage:.1f}%")
            
            if all(isinstance(metric, (int, float)) and metric >= 0 for metric in [memory_usage, cpu_usage, disk_usage]):
                logger.info("✅ System metrics collection working")
                self.test_results.append(("System Metrics", True, "All metrics collected"))
                return True
            else:
                logger.error("❌ Invalid system metrics")
                self.test_results.append(("System Metrics", False, "Invalid metrics"))
                return False
                
        except Exception as e:
            logger.error(f"❌ System metrics test failed: {e}")
            self.test_results.append(("System Metrics", False, str(e)))
            return False
    
    async def test_alert_manager_lifecycle(self):
        """Тестирует жизненный цикл менеджера алертов."""
        logger.info("🧪 Testing alert manager lifecycle...")
        
        try:
            # Создаем новый менеджер
            manager = AlertManager()
            
            # Добавляем правило
            test_rule = AlertRule(
                name="lifecycle_test",
                description="Lifecycle test alert",
                severity=AlertSeverity.LOW,
                condition=lambda: False  # Никогда не срабатывает
            )
            manager.add_rule(test_rule)
            
            # Запускаем
            await manager.start(check_interval=1)
            logger.info("✅ Alert manager started")
            
            # Проверяем статус
            status = manager.get_status()
            if status["running"]:
                logger.info("✅ Alert manager is running")
            else:
                logger.error("❌ Alert manager not running")
                return False
            
            # Останавливаем
            await manager.stop()
            logger.info("✅ Alert manager stopped")
            
            self.test_results.append(("Manager Lifecycle", True, "Start/stop cycle completed"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Alert manager lifecycle test failed: {e}")
            self.test_results.append(("Manager Lifecycle", False, str(e)))
            return False
    
    def print_test_results(self):
        """Выводит результаты тестирования."""
        logger.info("\n" + "="*60)
        logger.info("📋 ALERT SYSTEM TEST RESULTS")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {message}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📊 SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Alert system is ready for production.")
        else:
            logger.error(f"⚠️ {total - passed} tests failed. Please check the configuration.")
        
        return passed == total


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting VPN Bot Alert System Tests")
    logger.info(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = AlertTester()
    
    # Настройка
    if not await tester.setup():
        logger.error("❌ Setup failed, aborting tests")
        return False
    
    # Запуск тестов
    tests = [
        tester.test_configuration,
        tester.test_system_metrics,
        tester.test_error_recording,
        tester.test_log_monitoring,
        tester.test_alert_creation,
        tester.test_alert_manager_lifecycle
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
            tester.test_results.append((test.__name__, False, str(e)))
    
    # Результаты
    success = tester.print_test_results()
    
    logger.info(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
