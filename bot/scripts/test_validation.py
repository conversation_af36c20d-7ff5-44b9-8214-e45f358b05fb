#!/usr/bin/env python3
"""
Тестовый скрипт для проверки системы валидации.
"""

import asyncio
import sys
from pathlib import Path

# Добавляем путь к проекту
sys.path.append(str(Path(__file__).parent.parent))

from schemas.webhooks_simple import (
    validate_yookassa_webhook,
    validate_cryptomus_webhook,
    WebhookValidationError
)
from schemas.user_input_simple import (
    parse_callback_data,
    validate_user_input,
    ValidationError
)
from schemas.payments import validate_product_catalog
from utils.logging_config import get_logger

logger = get_logger(__name__)


def test_webhook_validation():
    """
    Тестирует валидацию webhook данных.
    """
    print("🔍 Testing webhook validation...")
    
    # Тест валидного YooKassa webhook
    valid_yookassa_data = {
        "type": "notification",
        "event": "payment.succeeded",
        "object": {
            "id": "test_payment_123",
            "status": "succeeded",
            "amount": {
                "value": "100.00",
                "currency": "RUB"
            },
            "created_at": "2023-01-01T12:00:00Z",
            "paid": True,
            "test": True
        }
    }
    
    try:
        validated = validate_yookassa_webhook(valid_yookassa_data)
        print(f"✅ Valid YooKassa webhook validated: {validated.object.id}")
    except WebhookValidationError as e:
        print(f"❌ Valid YooKassa webhook failed: {e}")
        return False
    
    # Тест невалидного YooKassa webhook
    invalid_yookassa_data = {
        "type": "invalid_type",
        "event": "payment.succeeded",
        "object": {
            "id": "",  # Пустой ID
            "status": "succeeded",
            "amount": {
                "value": "-100.00",  # Отрицательная сумма
                "currency": "INVALID"  # Неверная валюта
            },
            "created_at": "invalid_date",
            "paid": True
        }
    }
    
    try:
        validate_yookassa_webhook(invalid_yookassa_data)
        print("❌ Invalid YooKassa webhook should have failed")
        return False
    except WebhookValidationError:
        print("✅ Invalid YooKassa webhook correctly rejected")
    
    # Тест валидного Cryptomus webhook
    valid_cryptomus_data = {
        "uuid": "test_uuid_123",
        "order_id": "test_order_456",
        "amount": "1.00",
        "currency": "USD",
        "status": "paid",
        "sign": "abcdef1234567890abcdef1234567890"
    }
    
    try:
        validated = validate_cryptomus_webhook(valid_cryptomus_data)
        print(f"✅ Valid Cryptomus webhook validated: {validated.order_id}")
    except WebhookValidationError as e:
        print(f"❌ Valid Cryptomus webhook failed: {e}")
        return False
    
    # Тест невалидного Cryptomus webhook
    invalid_cryptomus_data = {
        "uuid": "",  # Пустой UUID
        "order_id": "test",
        "amount": "invalid_amount",  # Неверная сумма
        "currency": "USD",
        "status": "invalid_status",  # Неверный статус
        "sign": "short"  # Короткая подпись
    }
    
    try:
        validate_cryptomus_webhook(invalid_cryptomus_data)
        print("❌ Invalid Cryptomus webhook should have failed")
        return False
    except WebhookValidationError:
        print("✅ Invalid Cryptomus webhook correctly rejected")
    
    return True


def test_user_input_validation():
    """
    Тестирует валидацию пользовательского ввода.
    """
    print("\n👤 Testing user input validation...")
    
    # Тест валидных callback данных
    valid_callbacks = [
        "pay_kassa_simple_vpn",
        "pay_crypto_premium_vpn",
        "pay_stars_basic_vpn",
        "back",
        "cancel"
    ]
    
    for callback_data in valid_callbacks:
        try:
            validated = parse_callback_data(callback_data)
            print(f"✅ Valid callback '{callback_data}' parsed: {type(validated).__name__}")
        except ValidationError as e:
            print(f"❌ Valid callback '{callback_data}' failed: {e}")
            return False
    
    # Тест невалидных callback данных
    invalid_callbacks = [
        "",  # Пустой
        "invalid<script>",  # Небезопасные символы
        "pay_kassa_",  # Без данных
        "a" * 100,  # Слишком длинный
    ]
    
    for callback_data in invalid_callbacks:
        try:
            parse_callback_data(callback_data)
            print(f"❌ Invalid callback '{callback_data}' should have failed")
            return False
        except ValidationError:
            print(f"✅ Invalid callback '{callback_data}' correctly rejected")
    
    # Тест валидации пользовательских данных
    valid_user_data = {
        "id": 123456789,
        "first_name": "Test User",
        "last_name": "Last",
        "username": "testuser",
        "language_code": "en",
        "is_bot": False
    }
    
    try:
        validated = validate_user_input("/start", valid_user_data, 123456789)
        print(f"✅ Valid user input validated: {type(validated).__name__}")
    except ValidationError as e:
        print(f"❌ Valid user input failed: {e}")
        return False
    
    # Тест невалидных пользовательских данных
    invalid_user_data = {
        "id": -1,  # Отрицательный ID
        "first_name": "",  # Пустое имя
        "username": "a",  # Слишком короткий username
        "language_code": "invalid",  # Неверный код языка
        "is_bot": False
    }
    
    try:
        validate_user_input("/start", invalid_user_data, 123456789)
        print("❌ Invalid user input should have failed")
        return False
    except ValidationError:
        print("✅ Invalid user input correctly rejected")
    
    return True


def test_product_validation():
    """
    Тестирует валидацию товаров.
    """
    print("\n🛍️ Testing product validation...")
    
    # Тест валидного каталога товаров
    valid_products_data = [
        {
            "title": "Basic VPN",
            "price": {
                "ru": 100,
                "en": 1.5,
                "stars": 50
            },
            "callback": "basic_vpn",
            "months": 1
        },
        {
            "title": "Premium VPN",
            "price": {
                "ru": 500,
                "en": 7.5,
                "stars": 250
            },
            "callback": "premium_vpn",
            "months": 6
        }
    ]
    
    try:
        catalog = validate_product_catalog(valid_products_data)
        print(f"✅ Valid product catalog validated: {len(catalog.products)} products")
        
        # Тест получения товара
        product = catalog.get_product("basic_vpn")
        if product:
            print(f"✅ Product found: {product.title}")
        else:
            print("❌ Product not found")
            return False
            
    except ValueError as e:
        print(f"❌ Valid product catalog failed: {e}")
        return False
    
    # Тест невалидного каталога
    invalid_products_data = [
        {
            "title": "",  # Пустое название
            "price": {
                "ru": -100,  # Отрицательная цена
                "en": 1.5,
                "stars": 3000  # Превышает лимит
            },
            "callback": "invalid callback!",  # Неверный callback
            "months": 0  # Нулевое количество месяцев
        }
    ]
    
    try:
        validate_product_catalog(invalid_products_data)
        print("❌ Invalid product catalog should have failed")
        return False
    except ValueError:
        print("✅ Invalid product catalog correctly rejected")
    
    return True


def test_edge_cases():
    """
    Тестирует граничные случаи валидации.
    """
    print("\n🧪 Testing edge cases...")
    
    # Тест с Unicode символами
    unicode_user_data = {
        "id": 123456789,
        "first_name": "Тест Юзер 🚀",
        "last_name": "Фамилия",
        "username": "testuser123",
        "language_code": "ru",
        "is_bot": False
    }
    
    try:
        validate_user_input("Привет! 👋", unicode_user_data, 123456789)
        print("✅ Unicode input handled correctly")
    except ValidationError as e:
        print(f"❌ Unicode input failed: {e}")
        return False

    # Тест с максимальными значениями
    max_values_data = {
        "id": 2**63 - 1,  # Максимальный int64
        "first_name": "A" * 64,  # Максимальная длина
        "last_name": "B" * 64,
        "username": "a" * 32,  # Максимальная длина username
        "language_code": "en-US",
        "is_bot": False
    }

    try:
        validate_user_input("/start", max_values_data, -(2**63 - 1))
        print("✅ Maximum values handled correctly")
    except ValidationError as e:
        print(f"❌ Maximum values failed: {e}")
        return False

    # Тест с пустыми опциональными полями
    minimal_user_data = {
        "id": 123456789,
        "first_name": "Test",
        "last_name": None,
        "username": None,
        "language_code": None,
        "is_bot": False
    }

    try:
        validate_user_input("/help", minimal_user_data, 123456789)
        print("✅ Minimal user data handled correctly")
    except ValidationError as e:
        print(f"❌ Minimal user data failed: {e}")
        return False
    
    return True


def main():
    """
    Основная функция тестирования.
    """
    print("🧪 Starting validation system tests...\n")
    
    try:
        # Запускаем все тесты
        webhook_test = test_webhook_validation()
        user_input_test = test_user_input_validation()
        product_test = test_product_validation()
        edge_cases_test = test_edge_cases()
        
        # Общий результат
        if webhook_test and user_input_test and product_test and edge_cases_test:
            print("\n🎉 All validation tests passed!")
            print("✅ System is ready for production use")
            return 0
        else:
            print("\n❌ Some validation tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"Test suite failed: {e}", exc_info=True)
        print(f"\n💥 Test suite failed with exception: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
