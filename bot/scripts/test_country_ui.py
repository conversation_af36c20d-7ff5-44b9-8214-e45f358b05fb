#!/usr/bin/env python3
"""
Скрипт для тестирования пользовательского интерфейса выбора стран.
Проверяет работу клавиатур, обработчиков и интеграцию с сервисами.
"""

import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from keyboards.country_selection import CountrySelectionKeyboards
from handlers.country_callbacks import CountryCallbackHandlers
from schemas.country_callbacks import (
    parse_country_callback, is_country_callback, ValidationError,
    CountryCallbackData, NodeCallbackData, ProtocolCallbackData
)
from services.marzban_country_node_service import CountryNodeInfo, NodeInfo
from services.user_preference_service import CountryInfo
from db.models_countries_nodes import Country, MarzbanNode, NodeStatus
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="test-country-ui", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class MockCountry:
    """Мок модель страны."""
    def __init__(self, id: str, name_ru: str, flag: str, continent: str = "Europe"):
        self.id = id
        self.name_ru = name_ru
        self.name_en = name_ru  # Для совместимости
        self.flag = flag
        self.continent = continent
        self.priority = 1
        self.is_active = True


class MockNode:
    """Мок модель ноды."""
    def __init__(self, id: str, name: str, country_id: str, status: str = "connected"):
        self.id = id
        self.name = name
        self.country_id = country_id
        self.status = NodeStatus.CONNECTED if status == "connected" else NodeStatus.DISCONNECTED
        self.current_users = 150
        self.max_users = 1000
        self.is_available = status == "connected"
        self.load_percentage = 15.0
        self.supported_protocols = ["vless", "vmess"]


class CountryUITester:
    """Тестер пользовательского интерфейса стран."""
    
    def __init__(self):
        self.test_results = []
        self.keyboards = CountrySelectionKeyboards()
        self.handlers = CountryCallbackHandlers()
        
        # Создаем тестовые данные
        self.mock_countries = [
            MockCountry("DE", "Германия", "🇩🇪", "Europe"),
            MockCountry("NL", "Нидерланды", "🇳🇱", "Europe"),
            MockCountry("US", "США", "🇺🇸", "North America"),
            MockCountry("JP", "Япония", "🇯🇵", "Asia"),
            MockCountry("SG", "Сингапур", "🇸🇬", "Asia")
        ]
        
        self.mock_nodes = [
            MockNode("de-fra-01", "Germany Frankfurt #1", "DE"),
            MockNode("nl-ams-01", "Netherlands Amsterdam #1", "NL"),
            MockNode("us-ny-01", "United States New York #1", "US", "disconnected"),
            MockNode("jp-tok-01", "Japan Tokyo #1", "JP"),
            MockNode("sg-sin-01", "Singapore #1", "SG")
        ]
        
        self.mock_countries_info = []
        for country in self.mock_countries:
            country_nodes = [node for node in self.mock_nodes if node.country_id == country.id]
            available_nodes = len([node for node in country_nodes if node.is_available])

            # Создаем CountryInfo объект
            country_info_obj = CountryInfo(
                id=country.id,
                name=country.name_ru,
                name_ru=country.name_ru,
                flag=country.flag,
                continent=country.continent,
                region="Test Region",
                active_nodes_count=available_nodes,
                is_available=available_nodes > 0
            )

            country_info = CountryNodeInfo(
                country=country_info_obj,
                nodes=[],
                total_nodes=len(country_nodes),
                available_nodes=available_nodes,
                total_capacity=1000,
                current_load=150,
                average_load_percentage=15.0,
                best_node=None
            )
            self.mock_countries_info.append(country_info)
    
    def test_keyboard_generation(self):
        """Тест генерации клавиатур."""
        try:
            # Тест 1: Главное меню
            main_menu = self.keyboards.get_countries_menu_keyboard()
            if main_menu and main_menu.inline_keyboard:
                logger.info("✅ Main menu keyboard generated successfully")
                self.test_results.append(("Main Menu Keyboard", True, f"{len(main_menu.inline_keyboard)} rows"))
            else:
                self.test_results.append(("Main Menu Keyboard", False, "No keyboard generated"))
            
            # Тест 2: Выбор стран
            countries_keyboard = self.keyboards.get_country_selection_keyboard(
                countries=self.mock_countries_info,
                user_preferences=["DE", "NL"],
                page=0
            )
            if countries_keyboard and countries_keyboard.inline_keyboard:
                logger.info(f"✅ Countries selection keyboard generated: {len(countries_keyboard.inline_keyboard)} rows")
                self.test_results.append(("Countries Selection Keyboard", True, f"{len(countries_keyboard.inline_keyboard)} rows"))
            else:
                self.test_results.append(("Countries Selection Keyboard", False, "No keyboard generated"))
            
            # Тест 3: Информация о стране
            country_info_keyboard = self.keyboards.get_country_info_keyboard(
                country_id="DE",
                country_info=self.mock_countries_info[0]
            )
            if country_info_keyboard and country_info_keyboard.inline_keyboard:
                logger.info("✅ Country info keyboard generated successfully")
                self.test_results.append(("Country Info Keyboard", True, f"{len(country_info_keyboard.inline_keyboard)} rows"))
            else:
                self.test_results.append(("Country Info Keyboard", False, "No keyboard generated"))
            
            # Тест 4: Выбор нод
            mock_node_infos = []
            for node in self.mock_nodes[:3]:  # Первые 3 ноды
                country_obj = next((c for c in self.mock_countries if c.id == node.country_id), self.mock_countries[0])
                node_info = NodeInfo(
                    id=node.id,
                    name=node.name,
                    country_id=node.country_id,
                    country_name=country_obj.name_ru,
                    country_flag=country_obj.flag,
                    city="Test City",
                    status=node.status,
                    current_users=node.current_users,
                    max_users=node.max_users,
                    load_percentage=node.load_percentage,
                    is_available=node.is_available,
                    supported_protocols=node.supported_protocols,
                    response_time_ms=25.0,
                    cpu_usage=45.5,
                    memory_usage=60.2
                )
                mock_node_infos.append(node_info)
            
            nodes_keyboard = self.keyboards.get_node_selection_keyboard(
                nodes=mock_node_infos,
                country_id="DE"
            )
            if nodes_keyboard and nodes_keyboard.inline_keyboard:
                logger.info(f"✅ Nodes selection keyboard generated: {len(nodes_keyboard.inline_keyboard)} rows")
                self.test_results.append(("Nodes Selection Keyboard", True, f"{len(nodes_keyboard.inline_keyboard)} rows"))
            else:
                self.test_results.append(("Nodes Selection Keyboard", False, "No keyboard generated"))
            
            # Тест 5: Предпочтения
            preferences_keyboard = self.keyboards.get_preferences_keyboard(
                user_preferences=["DE", "NL"],
                available_countries=["DE", "NL", "US", "JP", "SG"]
            )
            if preferences_keyboard and preferences_keyboard.inline_keyboard:
                logger.info("✅ Preferences keyboard generated successfully")
                self.test_results.append(("Preferences Keyboard", True, f"{len(preferences_keyboard.inline_keyboard)} rows"))
            else:
                self.test_results.append(("Preferences Keyboard", False, "No keyboard generated"))
            
            # Тест 6: Протоколы
            protocols_keyboard = self.keyboards.get_protocol_preferences_keyboard(
                current_protocols=["vless", "vmess"]
            )
            if protocols_keyboard and protocols_keyboard.inline_keyboard:
                logger.info("✅ Protocol preferences keyboard generated successfully")
                self.test_results.append(("Protocol Preferences Keyboard", True, f"{len(protocols_keyboard.inline_keyboard)} rows"))
            else:
                self.test_results.append(("Protocol Preferences Keyboard", False, "No keyboard generated"))
            
        except Exception as e:
            logger.error(f"❌ Error testing keyboard generation: {e}")
            self.test_results.append(("Keyboard Generation", False, str(e)))
    
    def test_callback_parsing(self):
        """Тест парсинга callback данных."""
        try:
            test_callbacks = [
                # Простые callback
                ("select_countries", CountryCallbackData),
                ("auto_select_country", CountryCallbackData),
                ("my_preferences", CountryCallbackData),
                ("nodes_statistics", CountryCallbackData),
                
                # Callback с параметрами
                ("toggle_country_DE", CountryCallbackData),
                ("country_info_US", CountryCallbackData),
                ("countries_page_1", CountryCallbackData),
                ("remove_pref_NL", CountryCallbackData),
                
                # Callback нод
                ("select_node_de-fra-01", NodeCallbackData),
                ("best_node_DE", NodeCallbackData),
                ("auto_select_node_US", NodeCallbackData),
                ("refresh_nodes_JP", NodeCallbackData),
                ("speed_test_SG", NodeCallbackData),
                
                # Callback протоколов
                ("toggle_protocol_vless", ProtocolCallbackData),
                ("save_protocol_preferences", ProtocolCallbackData),
                ("reset_protocol_preferences", ProtocolCallbackData),
                
                # Callback подтверждения
                ("confirm_clear_preferences_all", None),  # ConfirmationCallbackData
                ("cancel_remove_country_DE", None),
            ]
            
            successful_parses = 0
            total_tests = len(test_callbacks)
            
            for callback_data, expected_type in test_callbacks:
                try:
                    parsed = parse_country_callback(callback_data)
                    
                    if expected_type and isinstance(parsed, expected_type):
                        successful_parses += 1
                        logger.debug(f"✅ Parsed {callback_data} as {type(parsed).__name__}")
                    elif expected_type is None:  # Для ConfirmationCallbackData
                        successful_parses += 1
                        logger.debug(f"✅ Parsed {callback_data} as {type(parsed).__name__}")
                    else:
                        logger.warning(f"❌ Expected {expected_type.__name__} for {callback_data}, got {type(parsed).__name__}")
                        
                except ValidationError as e:
                    logger.warning(f"❌ Failed to parse {callback_data}: {e}")
                except Exception as e:
                    logger.error(f"❌ Unexpected error parsing {callback_data}: {e}")
            
            success_rate = (successful_parses / total_tests) * 100
            logger.info(f"✅ Callback parsing: {successful_parses}/{total_tests} ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                self.test_results.append(("Callback Parsing", True, f"{successful_parses}/{total_tests} parsed"))
            else:
                self.test_results.append(("Callback Parsing", False, f"Only {successful_parses}/{total_tests} parsed"))
            
        except Exception as e:
            logger.error(f"❌ Error testing callback parsing: {e}")
            self.test_results.append(("Callback Parsing", False, str(e)))
    
    def test_callback_detection(self):
        """Тест определения callback стран."""
        try:
            country_callbacks = [
                "select_countries", "toggle_country_DE", "auto_select_country",
                "my_preferences", "nodes_statistics", "select_node_de-fra-01",
                "protocol_preferences", "confirm_clear_preferences_all"
            ]
            
            non_country_callbacks = [
                "pay_kassa_month", "buy_subscription", "main_menu",
                "support_contact", "faq_section"
            ]
            
            # Тест положительных случаев
            positive_correct = 0
            for callback in country_callbacks:
                if is_country_callback(callback):
                    positive_correct += 1
                else:
                    logger.warning(f"❌ Failed to detect country callback: {callback}")
            
            # Тест отрицательных случаев
            negative_correct = 0
            for callback in non_country_callbacks:
                if not is_country_callback(callback):
                    negative_correct += 1
                else:
                    logger.warning(f"❌ False positive for country callback: {callback}")
            
            total_correct = positive_correct + negative_correct
            total_tests = len(country_callbacks) + len(non_country_callbacks)
            accuracy = (total_correct / total_tests) * 100
            
            logger.info(f"✅ Callback detection accuracy: {total_correct}/{total_tests} ({accuracy:.1f}%)")
            
            if accuracy >= 90:
                self.test_results.append(("Callback Detection", True, f"{accuracy:.1f}% accuracy"))
            else:
                self.test_results.append(("Callback Detection", False, f"Only {accuracy:.1f}% accuracy"))
            
        except Exception as e:
            logger.error(f"❌ Error testing callback detection: {e}")
            self.test_results.append(("Callback Detection", False, str(e)))
    
    def test_keyboard_button_callbacks(self):
        """Тест callback данных в кнопках клавиатур."""
        try:
            # Генерируем клавиатуры и проверяем callback данные
            keyboards_to_test = [
                ("Main Menu", self.keyboards.get_countries_menu_keyboard()),
                ("Countries Selection", self.keyboards.get_country_selection_keyboard(
                    self.mock_countries_info, ["DE"], 0)),
                ("Country Info", self.keyboards.get_country_info_keyboard("DE", self.mock_countries_info[0])),
                ("Preferences", self.keyboards.get_preferences_keyboard(["DE"], ["DE", "NL", "US"])),
                ("Protocols", self.keyboards.get_protocol_preferences_keyboard(["vless"]))
            ]
            
            total_buttons = 0
            valid_callbacks = 0
            
            for keyboard_name, keyboard in keyboards_to_test:
                if not keyboard or not keyboard.inline_keyboard:
                    continue
                
                for row in keyboard.inline_keyboard:
                    for button in row:
                        total_buttons += 1
                        callback_data = button.callback_data
                        
                        if callback_data:
                            try:
                                # Проверяем, что callback можно распарсить
                                if is_country_callback(callback_data):
                                    parse_country_callback(callback_data)
                                valid_callbacks += 1
                            except ValidationError:
                                logger.warning(f"❌ Invalid callback in {keyboard_name}: {callback_data}")
                            except Exception as e:
                                logger.warning(f"❌ Error parsing callback in {keyboard_name}: {callback_data} - {e}")
            
            if total_buttons > 0:
                validity_rate = (valid_callbacks / total_buttons) * 100
                logger.info(f"✅ Button callbacks validity: {valid_callbacks}/{total_buttons} ({validity_rate:.1f}%)")
                
                if validity_rate >= 95:
                    self.test_results.append(("Button Callbacks", True, f"{validity_rate:.1f}% valid"))
                else:
                    self.test_results.append(("Button Callbacks", False, f"Only {validity_rate:.1f}% valid"))
            else:
                self.test_results.append(("Button Callbacks", False, "No buttons found"))
            
        except Exception as e:
            logger.error(f"❌ Error testing button callbacks: {e}")
            self.test_results.append(("Button Callbacks", False, str(e)))
    
    def test_pagination(self):
        """Тест пагинации стран."""
        try:
            # Создаем больше стран для тестирования пагинации
            extended_countries = []
            for i in range(15):  # 15 стран для тестирования пагинации
                country = MockCountry(f"T{i:02d}", f"Test Country {i+1}", f"🏳️", "Test")

                # Создаем CountryInfo объект
                country_info_obj = CountryInfo(
                    id=country.id,
                    name=country.name_ru,
                    name_ru=country.name_ru,
                    flag=country.flag,
                    continent=country.continent,
                    region="Test Region",
                    active_nodes_count=1,
                    is_available=True
                )

                country_info = CountryNodeInfo(
                    country=country_info_obj,
                    nodes=[],
                    total_nodes=1,
                    available_nodes=1,
                    total_capacity=1000,
                    current_load=200,
                    average_load_percentage=20.0,
                    best_node=None
                )
                extended_countries.append(country_info)
            
            # Тест первой страницы
            page_0 = self.keyboards.get_country_selection_keyboard(
                countries=extended_countries,
                user_preferences=[],
                page=0,
                per_page=5
            )
            
            # Тест второй страницы
            page_1 = self.keyboards.get_country_selection_keyboard(
                countries=extended_countries,
                user_preferences=[],
                page=1,
                per_page=5
            )
            
            # Проверяем, что обе страницы сгенерированы
            if (page_0 and page_0.inline_keyboard and 
                page_1 and page_1.inline_keyboard):
                
                # Проверяем наличие кнопок пагинации
                has_pagination_buttons = False
                for row in page_0.inline_keyboard:
                    for button in row:
                        if button.callback_data and "page" in button.callback_data:
                            has_pagination_buttons = True
                            break
                
                if has_pagination_buttons:
                    logger.info("✅ Pagination working correctly")
                    self.test_results.append(("Pagination", True, "Navigation buttons present"))
                else:
                    logger.info("✅ Pagination generated but no nav buttons (expected for small datasets)")
                    self.test_results.append(("Pagination", True, "Pages generated successfully"))
            else:
                self.test_results.append(("Pagination", False, "Failed to generate paginated keyboards"))
            
        except Exception as e:
            logger.error(f"❌ Error testing pagination: {e}")
            self.test_results.append(("Pagination", False, str(e)))
    
    def print_results(self):
        """Выводит результаты тестов."""
        logger.info("\n" + "="*60)
        logger.info("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ UI ВЫБОРА СТРАН")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {details}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📈 ИТОГО: {passed}/{total} тестов пройдено")
        
        if passed == total:
            logger.info("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
            return True
        else:
            logger.error(f"💥 {total - passed} тестов провалено")
            return False


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Country UI Testing")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = CountryUITester()
    
    try:
        # Запускаем все тесты
        tester.test_keyboard_generation()
        tester.test_callback_parsing()
        tester.test_callback_detection()
        tester.test_keyboard_button_callbacks()
        tester.test_pagination()
        
        # Выводим результаты
        success = tester.print_results()
        
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success
        
    except Exception as e:
        logger.error(f"💥 Testing failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
