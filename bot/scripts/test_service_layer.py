#!/usr/bin/env python3
"""
Скрипт для тестирования Service Layer в VPN боте.
"""

import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.base import (
    ServiceContainer, 
    setup_service_container, 
    initialize_services, 
    cleanup_services,
    get_service_container
)
from services.user_service import UserService
from services.payment_service import PaymentService
from services.subscription_service import SubscriptionService
from services.notification_service import NotificationService
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="service-layer-test", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class ServiceLayerTester:
    """Класс для тестирования Service Layer."""
    
    def __init__(self):
        self.test_results = []
        self.test_user_id = 888888888  # Тестовый Telegram ID
        self.container = None
    
    async def test_service_container_setup(self):
        """Тестирует настройку контейнера сервисов."""
        logger.info("🧪 Testing Service Container setup...")
        
        try:
            # Настраиваем контейнер
            self.container = setup_service_container()
            
            # Проверяем статистику
            stats = self.container.get_stats()
            
            assert stats['singletons_count'] == 0
            assert stats['factories_count'] == 4  # 4 сервиса
            assert stats['transients_count'] == 0
            assert 'UserService' in stats['registered_services']
            assert 'PaymentService' in stats['registered_services']
            assert 'SubscriptionService' in stats['registered_services']
            assert 'NotificationService' in stats['registered_services']
            
            logger.info("✅ Service container setup successful")
            self.test_results.append(("Service Container Setup", True, "All services registered"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Service container setup failed: {e}")
            self.test_results.append(("Service Container Setup", False, str(e)))
            return False
    
    async def test_service_initialization(self):
        """Тестирует инициализацию сервисов."""
        logger.info("🧪 Testing Service initialization...")
        
        try:
            # Инициализируем сервисы
            await initialize_services()
            
            # Проверяем что контейнер инициализирован
            container = get_service_container()
            stats = container.get_stats()
            
            assert stats['initialized'] is True
            
            logger.info("✅ Service initialization successful")
            self.test_results.append(("Service Initialization", True, "All services initialized"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Service initialization failed: {e}")
            self.test_results.append(("Service Initialization", False, str(e)))
            return False
    
    async def test_service_dependency_injection(self):
        """Тестирует внедрение зависимостей."""
        logger.info("🧪 Testing Dependency Injection...")
        
        try:
            container = get_service_container()
            
            # Получаем сервисы
            user_service = container.get_service(UserService)
            payment_service = container.get_service(PaymentService)
            subscription_service = container.get_service(SubscriptionService)
            notification_service = container.get_service(NotificationService)
            
            # Проверяем типы
            assert isinstance(user_service, UserService)
            assert isinstance(payment_service, PaymentService)
            assert isinstance(subscription_service, SubscriptionService)
            assert isinstance(notification_service, NotificationService)
            
            # Проверяем что у сервисов есть контейнер
            assert user_service.container == container
            assert payment_service.container == container
            assert subscription_service.container == container
            assert notification_service.container == container
            
            logger.info("✅ Dependency injection working correctly")
            self.test_results.append(("Dependency Injection", True, "All services injected correctly"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Dependency injection failed: {e}")
            self.test_results.append(("Dependency Injection", False, str(e)))
            return False
    
    async def test_user_service_functionality(self):
        """Тестирует функциональность UserService."""
        logger.info("🧪 Testing UserService functionality...")
        
        try:
            container = get_service_container()
            user_service = container.get_service(UserService)
            
            # Тест создания пользователя
            user = await user_service.create_or_get_user(self.test_user_id)
            assert user is not None
            assert user.tg_id == self.test_user_id
            logger.info(f"✅ User created: {user.tg_id}")
            
            # Тест получения пользователя
            retrieved_user = await user_service.get_user_profile(self.test_user_id)
            assert retrieved_user is not None
            assert retrieved_user.tg_id == self.test_user_id
            logger.info(f"✅ User retrieved: {retrieved_user.tg_id}")
            
            # Тест проверки тестовой подписки
            has_test = await user_service.check_test_subscription_used(self.test_user_id)
            assert has_test is False
            logger.info(f"✅ Test subscription check: {has_test}")
            
            # Тест обновления тестовой подписки
            updated = await user_service.mark_test_subscription_used(self.test_user_id)
            assert updated is True
            logger.info("✅ Test subscription updated")
            
            # Проверяем обновление
            has_test_after = await user_service.check_test_subscription_used(self.test_user_id)
            assert has_test_after is True
            logger.info(f"✅ Test subscription verified: {has_test_after}")
            
            self.test_results.append(("UserService Functionality", True, "All operations successful"))
            return True
            
        except Exception as e:
            logger.error(f"❌ UserService functionality test failed: {e}")
            self.test_results.append(("UserService Functionality", False, str(e)))
            return False
    
    async def test_payment_service_functionality(self):
        """Тестирует функциональность PaymentService."""
        logger.info("🧪 Testing PaymentService functionality...")
        
        try:
            container = get_service_container()
            payment_service = container.get_service(PaymentService)
            
            # Тест валидации товара (используем существующий товар)
            # Получаем список доступных товаров
            from utils import goods
            available_callbacks = goods.get_callbacks()
            
            if available_callbacks:
                test_product = available_callbacks[0]
                
                # Тест валидации существующего товара
                is_valid = await payment_service.validate_product(test_product)
                assert is_valid is True
                logger.info(f"✅ Product validation (valid): {test_product}")
                
                # Тест получения информации о товаре
                product_info = await payment_service.get_product_info(test_product)
                assert product_info is not None
                logger.info(f"✅ Product info retrieved: {test_product}")
            
            # Тест валидации несуществующего товара
            is_invalid = await payment_service.validate_product("nonexistent_product")
            assert is_invalid is False
            logger.info("✅ Product validation (invalid): nonexistent_product")
            
            # Тест получения статистики платежей
            stats = await payment_service.get_payment_stats()
            assert isinstance(stats, dict)
            logger.info(f"✅ Payment stats retrieved: {stats}")
            
            self.test_results.append(("PaymentService Functionality", True, "All operations successful"))
            return True
            
        except Exception as e:
            logger.error(f"❌ PaymentService functionality test failed: {e}")
            self.test_results.append(("PaymentService Functionality", False, str(e)))
            return False
    
    async def test_subscription_service_functionality(self):
        """Тестирует функциональность SubscriptionService."""
        logger.info("🧪 Testing SubscriptionService functionality...")
        
        try:
            container = get_service_container()
            subscription_service = container.get_service(SubscriptionService)
            
            # Тест получения информации о подписке (может вернуть None)
            subscription_info = await subscription_service.get_user_subscription_info(self.test_user_id)
            logger.info(f"✅ Subscription info retrieved: {'found' if subscription_info else 'not found'}")
            
            # Тест проверки статуса подписки
            status = await subscription_service.check_subscription_status(self.test_user_id)
            assert isinstance(status, dict)
            assert 'has_subscription' in status
            assert 'status' in status
            logger.info(f"✅ Subscription status checked: {status['status']}")
            
            # Тест валидации доступа к подписке
            has_access = await subscription_service.validate_subscription_access(self.test_user_id)
            assert isinstance(has_access, bool)
            logger.info(f"✅ Subscription access validated: {has_access}")
            
            self.test_results.append(("SubscriptionService Functionality", True, "All operations successful"))
            return True
            
        except Exception as e:
            logger.error(f"❌ SubscriptionService functionality test failed: {e}")
            self.test_results.append(("SubscriptionService Functionality", False, str(e)))
            return False
    
    async def test_notification_service_functionality(self):
        """Тестирует функциональность NotificationService."""
        logger.info("🧪 Testing NotificationService functionality...")
        
        try:
            container = get_service_container()
            notification_service = container.get_service(NotificationService)
            
            # Создаем мок бота
            mock_bot = AsyncMock()
            notification_service.set_bot(mock_bot)
            
            # Тест отправки приветственного сообщения
            result = await notification_service.send_welcome_message(
                self.test_user_id, "Test User", False
            )
            assert result is True
            mock_bot.send_message.assert_called()
            logger.info("✅ Welcome message sent")
            
            # Тест отправки кастомного уведомления
            result = await notification_service.send_custom_notification(
                self.test_user_id, "Test notification"
            )
            assert result is True
            logger.info("✅ Custom notification sent")
            
            # Тест рассылки
            result = await notification_service.broadcast_notification(
                [self.test_user_id, self.test_user_id + 1], "Broadcast test"
            )
            assert result['sent'] == 2
            assert result['failed'] == 0
            logger.info("✅ Broadcast notification sent")
            
            self.test_results.append(("NotificationService Functionality", True, "All operations successful"))
            return True
            
        except Exception as e:
            logger.error(f"❌ NotificationService functionality test failed: {e}")
            self.test_results.append(("NotificationService Functionality", False, str(e)))
            return False
    
    async def test_service_cleanup(self):
        """Тестирует очистку сервисов."""
        logger.info("🧪 Testing Service cleanup...")
        
        try:
            # Очищаем сервисы
            await cleanup_services()
            
            # Проверяем что контейнер больше не инициализирован
            container = get_service_container()
            stats = container.get_stats()
            
            assert stats['initialized'] is False
            
            logger.info("✅ Service cleanup successful")
            self.test_results.append(("Service Cleanup", True, "All services cleaned up"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Service cleanup failed: {e}")
            self.test_results.append(("Service Cleanup", False, str(e)))
            return False
    
    def print_test_results(self):
        """Выводит результаты тестирования."""
        logger.info("\n" + "="*60)
        logger.info("📋 SERVICE LAYER TEST RESULTS")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {message}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📊 SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Service Layer is working correctly.")
        else:
            logger.error(f"⚠️ {total - passed} tests failed. Please check the implementation.")
        
        return passed == total


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Service Layer Tests")
    logger.info(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = ServiceLayerTester()
    
    # Запуск тестов
    tests = [
        tester.test_service_container_setup,
        tester.test_service_initialization,
        tester.test_service_dependency_injection,
        tester.test_user_service_functionality,
        tester.test_payment_service_functionality,
        tester.test_subscription_service_functionality,
        tester.test_notification_service_functionality,
        tester.test_service_cleanup
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
            tester.test_results.append((test.__name__, False, str(e)))
    
    # Результаты
    success = tester.print_test_results()
    
    logger.info(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
