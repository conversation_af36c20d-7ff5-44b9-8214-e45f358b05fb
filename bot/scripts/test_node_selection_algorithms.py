#!/usr/bin/env python3
"""
Простой тест алгоритмов выбора нод без зависимостей от БД.
Тестирует логику выбора оптимальных нод.
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Any
from dataclasses import dataclass

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="test-node-algorithms", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


@dataclass
class MockNode:
    """Мок ноды для тестирования."""
    id: str
    name: str
    country_id: str
    current_users: int
    max_users: int
    cpu_usage: float
    memory_usage: float
    response_time_ms: float
    is_active: bool = True
    status: str = "connected"


@dataclass
class MockUserPreferences:
    """Мок предпочтений пользователя."""
    preferred_countries: List[str]
    preferred_protocols: List[str]
    prefer_low_latency: bool = True
    prefer_low_load: bool = True


class NodeSelectionAlgorithms:
    """Алгоритмы выбора нод."""
    
    @staticmethod
    def calculate_load_score(node: MockNode) -> float:
        """Вычислить оценку загрузки ноды (0-100)."""
        if node.max_users == 0:
            return 0.0
        
        load_percent = (node.current_users / node.max_users) * 100
        
        # Инвертируем: меньше загрузка = выше оценка
        if load_percent <= 50:
            return 100.0
        elif load_percent <= 70:
            return 80.0
        elif load_percent <= 85:
            return 60.0
        else:
            return 20.0
    
    @staticmethod
    def calculate_performance_score(node: MockNode) -> float:
        """Вычислить оценку производительности ноды (0-100)."""
        cpu_score = max(0, 100 - node.cpu_usage)
        memory_score = max(0, 100 - node.memory_usage)
        
        # Оценка времени отклика (меньше = лучше)
        if node.response_time_ms <= 50:
            response_score = 100.0
        elif node.response_time_ms <= 100:
            response_score = 80.0
        elif node.response_time_ms <= 200:
            response_score = 60.0
        else:
            response_score = 30.0
        
        # Средневзвешенная оценка
        return (cpu_score * 0.3 + memory_score * 0.3 + response_score * 0.4)
    
    @staticmethod
    def calculate_geographic_score(node: MockNode, preferences: MockUserPreferences) -> float:
        """Вычислить географическую оценку ноды (0-100)."""
        # Если нода в предпочитаемых странах
        if node.country_id in preferences.preferred_countries:
            return 100.0
        
        # Базовая оценка для других стран
        return 50.0
    
    @staticmethod
    def calculate_availability_score(node: MockNode) -> float:
        """Вычислить оценку доступности ноды (0-100)."""
        if node.status != "connected":
            return 0.0
        
        if not node.is_active:
            return 0.0
        
        return 100.0
    
    @classmethod
    def select_optimal_nodes(cls, 
                           nodes: List[MockNode], 
                           preferences: MockUserPreferences,
                           strategy: str = "hybrid") -> List[Dict[str, Any]]:
        """Выбрать оптимальные ноды."""
        scored_nodes = []
        
        for node in nodes:
            # Фильтруем неактивные ноды
            if not node.is_active or node.status != "connected":
                continue
            
            # Вычисляем оценки
            load_score = cls.calculate_load_score(node)
            performance_score = cls.calculate_performance_score(node)
            geographic_score = cls.calculate_geographic_score(node, preferences)
            availability_score = cls.calculate_availability_score(node)
            
            # Вычисляем общую оценку в зависимости от стратегии
            if strategy == "load_balanced":
                total_score = load_score * 0.6 + performance_score * 0.2 + availability_score * 0.2
            elif strategy == "performance":
                total_score = performance_score * 0.6 + load_score * 0.2 + availability_score * 0.2
            elif strategy == "geographic":
                total_score = geographic_score * 0.6 + load_score * 0.2 + availability_score * 0.2
            else:  # hybrid
                total_score = (load_score * 0.3 + performance_score * 0.3 + 
                             geographic_score * 0.2 + availability_score * 0.2)
            
            scored_nodes.append({
                'node': node,
                'total_score': total_score,
                'load_score': load_score,
                'performance_score': performance_score,
                'geographic_score': geographic_score,
                'availability_score': availability_score
            })
        
        # Сортируем по общей оценке
        scored_nodes.sort(key=lambda x: x['total_score'], reverse=True)
        
        return scored_nodes


class NodeSelectionTester:
    """Тестер алгоритмов выбора нод."""
    
    def __init__(self):
        self.test_results = []
        self.algorithms = NodeSelectionAlgorithms()
    
    def create_test_nodes(self) -> List[MockNode]:
        """Создать тестовые ноды."""
        return [
            MockNode(
                id="de-fra-01",
                name="Germany Frankfurt #1",
                country_id="DE",
                current_users=150,
                max_users=1000,
                cpu_usage=45.5,
                memory_usage=60.2,
                response_time_ms=25
            ),
            MockNode(
                id="nl-ams-01",
                name="Netherlands Amsterdam #1",
                country_id="NL",
                current_users=200,
                max_users=800,
                cpu_usage=55.8,
                memory_usage=70.1,
                response_time_ms=30
            ),
            MockNode(
                id="us-ny-01",
                name="United States New York #1",
                country_id="US",
                current_users=800,
                max_users=1200,
                cpu_usage=75.2,
                memory_usage=85.5,
                response_time_ms=120
            ),
            MockNode(
                id="de-ber-01",
                name="Germany Berlin #1",
                country_id="DE",
                current_users=50,
                max_users=500,
                cpu_usage=25.0,
                memory_usage=40.0,
                response_time_ms=15
            ),
            MockNode(
                id="fr-par-01",
                name="France Paris #1",
                country_id="FR",
                current_users=300,
                max_users=600,
                cpu_usage=65.0,
                memory_usage=75.0,
                response_time_ms=45
            )
        ]
    
    def test_load_scoring(self):
        """Тест оценки загрузки."""
        try:
            nodes = self.create_test_nodes()
            
            # Тестируем разные уровни загрузки
            test_cases = [
                (nodes[0], "Low load (15%)"),      # 150/1000 = 15%
                (nodes[1], "Medium load (25%)"),   # 200/800 = 25%
                (nodes[2], "High load (67%)"),     # 800/1200 = 67%
                (nodes[3], "Very low load (10%)"), # 50/500 = 10%
                (nodes[4], "Medium load (50%)")    # 300/600 = 50%
            ]
            
            results = []
            for node, description in test_cases:
                score = self.algorithms.calculate_load_score(node)
                results.append(f"{description}: {score:.1f}")
                logger.info(f"Load score for {node.name}: {score:.1f}")
            
            self.test_results.append(("Load Scoring", True, f"Tested {len(test_cases)} cases"))
            
        except Exception as e:
            logger.error(f"❌ Error in load scoring test: {e}")
            self.test_results.append(("Load Scoring", False, str(e)))
    
    def test_performance_scoring(self):
        """Тест оценки производительности."""
        try:
            nodes = self.create_test_nodes()
            
            results = []
            for node in nodes:
                score = self.algorithms.calculate_performance_score(node)
                results.append(f"{node.name}: {score:.1f}")
                logger.info(f"Performance score for {node.name}: {score:.1f}")
            
            self.test_results.append(("Performance Scoring", True, f"Tested {len(nodes)} nodes"))
            
        except Exception as e:
            logger.error(f"❌ Error in performance scoring test: {e}")
            self.test_results.append(("Performance Scoring", False, str(e)))
    
    def test_geographic_scoring(self):
        """Тест географической оценки."""
        try:
            nodes = self.create_test_nodes()
            preferences = MockUserPreferences(
                preferred_countries=["DE", "NL"],
                preferred_protocols=["vless"]
            )
            
            results = []
            for node in nodes:
                score = self.algorithms.calculate_geographic_score(node, preferences)
                preferred = "✅" if node.country_id in preferences.preferred_countries else "❌"
                results.append(f"{node.country_id} {preferred}: {score:.1f}")
                logger.info(f"Geographic score for {node.name} ({node.country_id}): {score:.1f}")
            
            self.test_results.append(("Geographic Scoring", True, f"Tested {len(nodes)} nodes"))
            
        except Exception as e:
            logger.error(f"❌ Error in geographic scoring test: {e}")
            self.test_results.append(("Geographic Scoring", False, str(e)))
    
    def test_node_selection_strategies(self):
        """Тест различных стратегий выбора."""
        try:
            nodes = self.create_test_nodes()
            preferences = MockUserPreferences(
                preferred_countries=["DE", "NL"],
                preferred_protocols=["vless"]
            )
            
            strategies = ["hybrid", "load_balanced", "performance", "geographic"]
            
            for strategy in strategies:
                scored_nodes = self.algorithms.select_optimal_nodes(nodes, preferences, strategy)
                
                if scored_nodes:
                    best_node = scored_nodes[0]
                    logger.info(f"Strategy '{strategy}': Best node is {best_node['node'].name} "
                              f"(score: {best_node['total_score']:.1f})")
                    
                    # Показываем топ-3
                    for i, scored_node in enumerate(scored_nodes[:3]):
                        node = scored_node['node']
                        score = scored_node['total_score']
                        logger.info(f"  {i+1}. {node.name} ({node.country_id}): {score:.1f}")
                else:
                    logger.warning(f"Strategy '{strategy}': No nodes selected")
            
            self.test_results.append(("Selection Strategies", True, f"Tested {len(strategies)} strategies"))
            
        except Exception as e:
            logger.error(f"❌ Error in selection strategies test: {e}")
            self.test_results.append(("Selection Strategies", False, str(e)))
    
    def test_edge_cases(self):
        """Тест граничных случаев."""
        try:
            # Тест с пустым списком нод
            empty_result = self.algorithms.select_optimal_nodes([], MockUserPreferences([], []))
            assert len(empty_result) == 0, "Empty nodes list should return empty result"
            
            # Тест с неактивными нодами
            inactive_nodes = [
                MockNode("inactive-01", "Inactive Node", "XX", 0, 100, 0, 0, 0, is_active=False),
                MockNode("disconnected-01", "Disconnected Node", "YY", 0, 100, 0, 0, 0, status="disconnected")
            ]
            
            inactive_result = self.algorithms.select_optimal_nodes(
                inactive_nodes, 
                MockUserPreferences([], [])
            )
            assert len(inactive_result) == 0, "Inactive nodes should be filtered out"
            
            # Тест с перегруженными нодами
            overloaded_node = MockNode("overloaded-01", "Overloaded", "ZZ", 950, 1000, 95, 95, 500)
            overloaded_result = self.algorithms.select_optimal_nodes(
                [overloaded_node], 
                MockUserPreferences([], [])
            )
            
            if overloaded_result:
                score = overloaded_result[0]['total_score']
                logger.info(f"Overloaded node score: {score:.1f}")
            
            logger.info("✅ All edge cases passed")
            self.test_results.append(("Edge Cases", True, "All edge cases handled correctly"))
            
        except Exception as e:
            logger.error(f"❌ Error in edge cases test: {e}")
            self.test_results.append(("Edge Cases", False, str(e)))
    
    def print_results(self):
        """Выводит результаты тестов."""
        logger.info("\n" + "="*60)
        logger.info("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ АЛГОРИТМОВ ВЫБОРА НОД")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {details}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📈 ИТОГО: {passed}/{total} тестов пройдено")
        
        if passed == total:
            logger.info("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
            return True
        else:
            logger.error(f"💥 {total - passed} тестов провалено")
            return False


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Node Selection Algorithms Testing")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = NodeSelectionTester()
    
    try:
        # Запускаем все тесты
        tester.test_load_scoring()
        tester.test_performance_scoring()
        tester.test_geographic_scoring()
        tester.test_node_selection_strategies()
        tester.test_edge_cases()
        
        # Выводим результаты
        success = tester.print_results()
        
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success
        
    except Exception as e:
        logger.error(f"💥 Testing failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
