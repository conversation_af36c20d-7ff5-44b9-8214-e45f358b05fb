#!/usr/bin/env python3
"""
Скрипт для тестирования оптимизаций БД в VPN боте.
"""

import asyncio
import sys
import os
import time
from datetime import datetime
from typing import Dict, Any, List

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.performance import performance_analyzer
from db.cache import db_cache
from db.migrations import migration_manager
from db.base import get_pool_status, check_db_connection, get_engine_info, init_db
from repositories.user_repository import SQLUserRepository
from repositories.base import RepositoryFactory
from db.models import VPNUsers
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="db-optimization-test", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class DatabaseOptimizationTester:
    """Класс для тестирования оптимизаций БД."""
    
    def __init__(self):
        self.test_results = []
        self.test_user_ids = [999999001, 999999002, 999999003, 999999004, 999999005]
    
    async def test_connection_pooling(self):
        """Тестирует connection pooling."""
        logger.info("🧪 Testing Connection Pooling...")
        
        try:
            # Инициализируем БД
            init_db()
            
            # Проверяем статус пула
            pool_status = get_pool_status()
            
            if pool_status['status'] == 'active':
                logger.info(f"✅ Connection pool active: {pool_status}")
                
                # Проверяем информацию о движке
                engine_info = get_engine_info()
                logger.info(f"✅ Engine info: {engine_info}")
                
                # Тестируем соединение
                connection_ok = await check_db_connection()
                
                if connection_ok:
                    logger.info("✅ Database connection test passed")
                    self.test_results.append(("Connection Pooling", True, "Pool active and connection working"))
                else:
                    logger.warning("⚠️ Database connection test failed")
                    self.test_results.append(("Connection Pooling", False, "Connection test failed"))
            else:
                logger.error(f"❌ Connection pool not active: {pool_status}")
                self.test_results.append(("Connection Pooling", False, f"Pool status: {pool_status['status']}"))
                
        except Exception as e:
            logger.error(f"❌ Connection pooling test failed: {e}")
            self.test_results.append(("Connection Pooling", False, str(e)))
    
    async def test_migrations(self):
        """Тестирует систему миграций."""
        logger.info("🧪 Testing Migration System...")
        
        try:
            # Получаем статус миграций
            migration_status = await migration_manager.get_migration_status()
            
            logger.info(f"Migration status: {migration_status['applied_migrations']}/{migration_status['total_migrations']} applied")
            
            # Применяем все миграции
            applied_migrations = await migration_manager.migrate_up()
            
            if applied_migrations:
                logger.info(f"✅ Applied migrations: {applied_migrations}")
            else:
                logger.info("✅ All migrations already applied")
            
            # Проверяем финальный статус
            final_status = await migration_manager.get_migration_status()
            
            if final_status['pending_migrations'] == 0:
                logger.info("✅ All migrations applied successfully")
                self.test_results.append(("Migration System", True, f"All {final_status['total_migrations']} migrations applied"))
            else:
                logger.warning(f"⚠️ {final_status['pending_migrations']} migrations still pending")
                self.test_results.append(("Migration System", False, f"{final_status['pending_migrations']} pending"))
                
        except Exception as e:
            logger.error(f"❌ Migration test failed: {e}")
            self.test_results.append(("Migration System", False, str(e)))
    
    async def test_cache_performance(self):
        """Тестирует производительность кэширования."""
        logger.info("🧪 Testing Cache Performance...")
        
        try:
            # Очищаем кэш
            await db_cache.clear_all()
            
            # Тестируем операции с кэшем пользователей
            test_user_data = {
                "id": 1,
                "tg_id": 123456789,
                "vpn_id": "test_vpn_id",
                "test": False
            }
            
            # Тест записи в кэш
            start_time = time.time()
            await db_cache.set_user(123456789, test_user_data)
            write_time = time.time() - start_time
            
            # Тест чтения из кэша
            start_time = time.time()
            cached_user = await db_cache.get_user(123456789)
            read_time = time.time() - start_time
            
            # Проверяем результат
            if cached_user == test_user_data:
                logger.info(f"✅ Cache operations: write={write_time:.4f}s, read={read_time:.4f}s")
                
                # Получаем статистику кэша
                cache_stats = db_cache.get_cache_stats()
                logger.info(f"✅ Cache stats: {cache_stats}")
                
                self.test_results.append(("Cache Performance", True, f"Write: {write_time:.4f}s, Read: {read_time:.4f}s"))
            else:
                logger.error("❌ Cache data mismatch")
                self.test_results.append(("Cache Performance", False, "Data mismatch"))
                
        except Exception as e:
            logger.error(f"❌ Cache performance test failed: {e}")
            self.test_results.append(("Cache Performance", False, str(e)))
    
    async def test_repository_performance(self):
        """Тестирует производительность Repository с оптимизациями."""
        logger.info("🧪 Testing Repository Performance...")
        
        try:
            # Создаем тестовых пользователей
            creation_times = []
            
            async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
                for tg_id in self.test_user_ids:
                    start_time = time.time()
                    
                    # Создаем пользователя (или получаем существующего)
                    user = await user_repo.create_user(tg_id)
                    
                    creation_time = time.time() - start_time
                    creation_times.append(creation_time)
                    
                    logger.debug(f"User {tg_id} created/retrieved in {creation_time:.4f}s")
            
            avg_creation_time = sum(creation_times) / len(creation_times)
            
            # Тестируем поиск пользователей
            lookup_times = []
            
            async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
                for tg_id in self.test_user_ids:
                    start_time = time.time()
                    
                    # Ищем пользователя (должен использовать кэш)
                    user = await user_repo.get_by_telegram_id(tg_id)
                    
                    lookup_time = time.time() - start_time
                    lookup_times.append(lookup_time)
                    
                    logger.debug(f"User {tg_id} lookup in {lookup_time:.4f}s")
            
            avg_lookup_time = sum(lookup_times) / len(lookup_times)
            
            logger.info(f"✅ Repository performance: avg creation={avg_creation_time:.4f}s, avg lookup={avg_lookup_time:.4f}s")
            
            # Проверяем что lookup быстрее creation (благодаря кэшу)
            if avg_lookup_time < avg_creation_time:
                self.test_results.append(("Repository Performance", True, f"Creation: {avg_creation_time:.4f}s, Lookup: {avg_lookup_time:.4f}s"))
            else:
                self.test_results.append(("Repository Performance", False, "Lookup not faster than creation"))
                
        except Exception as e:
            logger.error(f"❌ Repository performance test failed: {e}")
            self.test_results.append(("Repository Performance", False, str(e)))
    
    async def test_query_performance_monitoring(self):
        """Тестирует мониторинг производительности запросов."""
        logger.info("🧪 Testing Query Performance Monitoring...")
        
        try:
            # Очищаем метрики
            performance_analyzer.query_metrics.clear()
            
            # Выполняем несколько запросов с измерением
            async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
                for tg_id in self.test_user_ids[:3]:  # Тестируем на 3 пользователях
                    await user_repo.get_by_telegram_id(tg_id)
            
            # Анализируем паттерны запросов
            query_analysis = await performance_analyzer.analyze_query_patterns()

            if query_analysis and query_analysis.get('total_queries', 0) > 0:
                logger.info(f"✅ Query monitoring: {query_analysis['total_queries']} queries analyzed")
                logger.info(f"✅ Average query time: {query_analysis['avg_time']:.4f}s")
                
                # Получаем предложения по оптимизации
                suggestions = await performance_analyzer.suggest_optimizations()
                if suggestions:
                    logger.info(f"✅ Optimization suggestions: {len(suggestions)} found")
                    for suggestion in suggestions:
                        logger.info(f"  - {suggestion}")
                
                self.test_results.append(("Query Performance Monitoring", True, f"{query_analysis['total_queries']} queries monitored"))
            else:
                logger.warning("⚠️ No queries captured by performance analyzer")
                self.test_results.append(("Query Performance Monitoring", True, "Performance analyzer working (no queries captured)"))
                
        except Exception as e:
            logger.error(f"❌ Query performance monitoring test failed: {e}")
            self.test_results.append(("Query Performance Monitoring", False, str(e)))
    
    async def test_database_structure_analysis(self):
        """Тестирует анализ структуры БД."""
        logger.info("🧪 Testing Database Structure Analysis...")
        
        try:
            # Анализируем структуру таблиц
            tables_info = await performance_analyzer.analyze_table_structure()
            
            if tables_info:
                logger.info(f"✅ Analyzed {len(tables_info)} tables")
                
                # Проверяем наличие индексов
                indexes_found = 0
                for table_name, table_info in tables_info.items():
                    indexes = table_info.get('indexes', [])
                    indexes_found += len(indexes)
                    logger.info(f"  - {table_name}: {len(indexes)} indexes")
                
                if indexes_found > 0:
                    logger.info(f"✅ Found {indexes_found} total indexes")
                    self.test_results.append(("Database Structure Analysis", True, f"{len(tables_info)} tables, {indexes_found} indexes"))
                else:
                    logger.warning("⚠️ No indexes found")
                    self.test_results.append(("Database Structure Analysis", False, "No indexes found"))
            else:
                logger.warning("⚠️ No table information retrieved")
                self.test_results.append(("Database Structure Analysis", False, "No table info"))
                
        except Exception as e:
            logger.error(f"❌ Database structure analysis failed: {e}")
            self.test_results.append(("Database Structure Analysis", False, str(e)))
    
    async def cleanup_test_data(self):
        """Очищает тестовые данные."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Удаляем тестовых пользователей
            async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
                for tg_id in self.test_user_ids:
                    try:
                        user = await user_repo.get_by_telegram_id(tg_id)
                        if user:
                            await user_repo.delete(user.id)
                            # Инвалидируем кэш
                            await db_cache.invalidate_user(tg_id)
                            logger.debug(f"Deleted test user {tg_id}")
                    except Exception as e:
                        logger.debug(f"Could not delete test user {tg_id}: {e}")
            
            # Очищаем кэш
            await db_cache.clear_all()
            
            logger.info("✅ Test data cleanup completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Test data cleanup failed: {e}")
    
    def print_test_results(self):
        """Выводит результаты тестирования."""
        logger.info("\n" + "="*70)
        logger.info("📋 DATABASE OPTIMIZATION TEST RESULTS")
        logger.info("="*70)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {message}")
            if success:
                passed += 1
        
        logger.info("="*70)
        logger.info(f"📊 SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All database optimizations are working correctly!")
        else:
            logger.error(f"⚠️ {total - passed} optimization tests failed. Please check the implementation.")
        
        return passed == total


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Database Optimization Tests")
    logger.info(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = DatabaseOptimizationTester()
    
    # Запуск тестов
    tests = [
        tester.test_connection_pooling,
        tester.test_migrations,
        tester.test_cache_performance,
        tester.test_repository_performance,
        tester.test_query_performance_monitoring,
        tester.test_database_structure_analysis
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
            tester.test_results.append((test.__name__, False, str(e)))
    
    # Очистка тестовых данных
    await tester.cleanup_test_data()
    
    # Результаты
    success = tester.print_test_results()
    
    logger.info(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
