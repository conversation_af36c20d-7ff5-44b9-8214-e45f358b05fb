#!/usr/bin/env python3
"""
Скрипт для тестирования Repository Pattern в VPN боте.
"""

import asyncio
import sys
import os
from datetime import datetime

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from repositories.user_repository import SQLUserRepository
from repositories.payment_repository import SQLPaymentRepository
from repositories.base import UnitOfWork, RepositoryFactory
from repositories.adapters import (
    create_vpn_profile,
    get_marzban_profile_db,
    had_test_sub,
    update_test_subscription_state,
    add_yookassa_payment,
    get_yookassa_payment,
    get_user_stats,
    get_payment_stats
)
from db.models import VPNUsers, YPayments
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="repository-test", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


class RepositoryTester:
    """Класс для тестирования Repository Pattern."""
    
    def __init__(self):
        self.test_results = []
        self.test_user_id = 999999999  # Тестовый Telegram ID
    
    async def test_user_repository_direct(self):
        """Тестирует UserRepository напрямую."""
        logger.info("🧪 Testing UserRepository directly...")
        
        try:
            async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
                # Тест создания пользователя
                user = await user_repo.create_user(self.test_user_id)
                assert user is not None
                assert user.tg_id == self.test_user_id
                logger.info(f"✅ User created: {user.tg_id}")
                
                # Тест получения пользователя
                retrieved_user = await user_repo.get_by_telegram_id(self.test_user_id)
                assert retrieved_user is not None
                assert retrieved_user.tg_id == self.test_user_id
                logger.info(f"✅ User retrieved: {retrieved_user.tg_id}")
                
                # Тест проверки тестовой подписки
                has_test = await user_repo.has_test_subscription(self.test_user_id)
                assert has_test is False
                logger.info(f"✅ Test subscription check: {has_test}")
                
                # Тест обновления тестовой подписки
                updated = await user_repo.update_test_subscription_state(self.test_user_id, True)
                assert updated is True
                logger.info("✅ Test subscription updated")
                
                # Проверяем обновление
                has_test_after = await user_repo.has_test_subscription(self.test_user_id)
                assert has_test_after is True
                logger.info(f"✅ Test subscription verified: {has_test_after}")
                
                # Тест получения по VPN ID
                user_by_vpn = await user_repo.get_by_vpn_id(user.vpn_id)
                assert user_by_vpn is not None
                assert user_by_vpn.tg_id == self.test_user_id
                logger.info(f"✅ User retrieved by VPN ID: {user_by_vpn.vpn_id}")
                
                self.test_results.append(("UserRepository Direct", True, "All operations successful"))
                return True
                
        except Exception as e:
            logger.error(f"❌ UserRepository direct test failed: {e}")
            self.test_results.append(("UserRepository Direct", False, str(e)))
            return False
    
    async def test_payment_repository_direct(self):
        """Тестирует PaymentRepository напрямую."""
        logger.info("🧪 Testing PaymentRepository directly...")
        
        try:
            async with UnitOfWork() as uow:
                payment_repo = uow.get_repository(SQLPaymentRepository, None)
                
                # Тест создания платежа YooKassa
                payment_id = f"test_payment_{int(datetime.now().timestamp())}"
                payment = await payment_repo.create_yookassa_payment(
                    tg_id=self.test_user_id,
                    payment_id=payment_id,
                    callback="test_callback",
                    chat_id=self.test_user_id,
                    lang_code="ru"
                )
                
                assert payment is not None
                assert payment.payment_id == payment_id
                logger.info(f"✅ YooKassa payment created: {payment_id}")
                
                # Тест получения платежа
                retrieved_payment = await payment_repo.get_yookassa_payment(payment_id)
                assert retrieved_payment is not None
                assert retrieved_payment.payment_id == payment_id
                logger.info(f"✅ YooKassa payment retrieved: {payment_id}")
                
                # Тест получения платежей пользователя
                user_payments = await payment_repo.get_user_payments(self.test_user_id)
                assert len(user_payments) > 0
                logger.info(f"✅ User payments retrieved: {len(user_payments)}")
                
                # Тест удаления платежа
                deleted = await payment_repo.delete_payment(payment_id, "yookassa")
                assert deleted is True
                logger.info(f"✅ Payment deleted: {payment_id}")
                
                self.test_results.append(("PaymentRepository Direct", True, "All operations successful"))
                return True
                
        except Exception as e:
            logger.error(f"❌ PaymentRepository direct test failed: {e}")
            self.test_results.append(("PaymentRepository Direct", False, str(e)))
            return False
    
    async def test_unit_of_work(self):
        """Тестирует Unit of Work pattern."""
        logger.info("🧪 Testing Unit of Work...")
        
        try:
            async with UnitOfWork() as uow:
                user_repo = uow.get_repository(SQLUserRepository, VPNUsers)
                
                # Создаем пользователя в рамках транзакции
                test_user_id = self.test_user_id + 1
                user = await user_repo.create_user(test_user_id)
                assert user is not None
                
                # Обновляем пользователя в той же транзакции
                updated = await user_repo.update_test_subscription_state(test_user_id, True)
                assert updated is True
                
                logger.info("✅ Unit of Work operations completed")
                
                # Транзакция автоматически коммитится при выходе из контекста
            
            # Проверяем, что изменения сохранились
            async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
                saved_user = await user_repo.get_by_telegram_id(test_user_id)
                assert saved_user is not None
                assert saved_user.test is True
                
                logger.info("✅ Unit of Work transaction committed successfully")
                
                self.test_results.append(("Unit of Work", True, "Transaction handling successful"))
                return True
                
        except Exception as e:
            logger.error(f"❌ Unit of Work test failed: {e}")
            self.test_results.append(("Unit of Work", False, str(e)))
            return False
    
    async def test_adapters(self):
        """Тестирует адаптеры для обратной совместимости."""
        logger.info("🧪 Testing Repository Adapters...")
        
        try:
            test_user_id = self.test_user_id + 2
            
            # Тест создания профиля через адаптер
            await create_vpn_profile(test_user_id)
            logger.info(f"✅ VPN profile created via adapter: {test_user_id}")
            
            # Тест получения профиля через адаптер
            user = await get_marzban_profile_db(test_user_id)
            assert user is not None
            assert user.tg_id == test_user_id
            logger.info(f"✅ Profile retrieved via adapter: {test_user_id}")
            
            # Тест проверки тестовой подписки через адаптер
            has_test = await had_test_sub(test_user_id)
            assert has_test is False
            logger.info(f"✅ Test subscription check via adapter: {has_test}")
            
            # Тест обновления тестовой подписки через адаптер
            await update_test_subscription_state(test_user_id)
            logger.info("✅ Test subscription updated via adapter")
            
            # Проверяем обновление
            has_test_after = await had_test_sub(test_user_id)
            assert has_test_after is True
            logger.info(f"✅ Test subscription verified via adapter: {has_test_after}")
            
            # Тест создания платежа через адаптер
            payment_id = f"adapter_test_{int(datetime.now().timestamp())}"
            payment_data = await add_yookassa_payment(
                tg_id=test_user_id,
                callback="adapter_test",
                chat_id=test_user_id,
                lang_code="ru",
                payment_id=payment_id
            )
            assert payment_data is not None
            logger.info(f"✅ Payment created via adapter: {payment_id}")
            
            # Тест получения платежа через адаптер
            payment = await get_yookassa_payment(payment_id)
            assert payment is not None
            assert payment.payment_id == payment_id
            logger.info(f"✅ Payment retrieved via adapter: {payment_id}")
            
            self.test_results.append(("Repository Adapters", True, "All adapter functions working"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Repository Adapters test failed: {e}")
            self.test_results.append(("Repository Adapters", False, str(e)))
            return False
    
    async def test_statistics(self):
        """Тестирует функции статистики."""
        logger.info("🧪 Testing Statistics functions...")
        
        try:
            # Тест статистики пользователей
            user_stats = await get_user_stats()
            assert isinstance(user_stats, dict)
            assert 'total_users' in user_stats
            logger.info(f"✅ User stats retrieved: {user_stats}")
            
            # Тест статистики платежей
            payment_stats = await get_payment_stats()
            assert isinstance(payment_stats, dict)
            assert 'total_payments' in payment_stats
            logger.info(f"✅ Payment stats retrieved: {payment_stats}")
            
            self.test_results.append(("Statistics", True, "All statistics functions working"))
            return True
            
        except Exception as e:
            logger.error(f"❌ Statistics test failed: {e}")
            self.test_results.append(("Statistics", False, str(e)))
            return False
    
    async def cleanup_test_data(self):
        """Очищает тестовые данные."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            async with UnitOfWork() as uow:
                user_repo = uow.get_repository(SQLUserRepository, VPNUsers)
                
                # Удаляем тестовых пользователей
                for i in range(3):
                    test_id = self.test_user_id + i
                    user = await user_repo.get_by_telegram_id(test_id)
                    if user:
                        await user_repo.delete(user.id)
                        logger.info(f"✅ Cleaned up test user: {test_id}")
            
            logger.info("✅ Test data cleanup completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Cleanup failed (non-critical): {e}")
    
    def print_test_results(self):
        """Выводит результаты тестирования."""
        logger.info("\n" + "="*60)
        logger.info("📋 REPOSITORY PATTERN TEST RESULTS")
        logger.info("="*60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} {test_name}: {message}")
            if success:
                passed += 1
        
        logger.info("="*60)
        logger.info(f"📊 SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Repository Pattern is working correctly.")
        else:
            logger.error(f"⚠️ {total - passed} tests failed. Please check the implementation.")
        
        return passed == total


async def main():
    """Основная функция тестирования."""
    logger.info("🚀 Starting Repository Pattern Tests")
    logger.info(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = RepositoryTester()
    
    # Запуск тестов
    tests = [
        tester.test_user_repository_direct,
        tester.test_payment_repository_direct,
        tester.test_unit_of_work,
        tester.test_adapters,
        tester.test_statistics
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
            tester.test_results.append((test.__name__, False, str(e)))
    
    # Очистка тестовых данных
    await tester.cleanup_test_data()
    
    # Результаты
    success = tester.print_test_results()
    
    logger.info(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
