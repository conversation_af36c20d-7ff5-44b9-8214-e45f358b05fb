#!/usr/bin/env python3
"""
Скрипт для применения миграций БД.
"""

import asyncio
import sys
import os
from datetime import datetime

# Добавляем путь к проекту
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from db.migrations import migration_manager
from utils.logging_config import setup_logging, get_logger

# Настраиваем логирование
setup_logging(service_name="migration-apply", log_level="INFO", enable_json=False)
logger = get_logger(__name__)


async def main():
    """Основная функция применения миграций."""
    logger.info("🚀 Starting Database Migrations")
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Получаем статус миграций
        logger.info("📋 Checking migration status...")
        status = await migration_manager.get_migration_status()
        
        if 'error' in status:
            logger.error(f"❌ Error getting migration status: {status['error']}")
            return False
        
        logger.info(f"📊 Migration status: {status['applied_migrations']}/{status['total_migrations']} applied")
        logger.info(f"📊 Pending migrations: {status['pending_migrations']}")
        
        if status['pending_migrations'] == 0:
            logger.info("✅ All migrations already applied!")
            return True
        
        # Показываем список миграций
        logger.info("📋 Migration details:")
        for migration in status['migrations']:
            status_icon = "✅" if migration['applied'] else "⏳"
            logger.info(f"  {status_icon} {migration['version']}: {migration['name']} - {migration['description']}")
        
        # Применяем миграции
        logger.info("🔄 Applying pending migrations...")
        applied_migrations = await migration_manager.migrate_up()
        
        if applied_migrations:
            logger.info(f"✅ Successfully applied {len(applied_migrations)} migrations:")
            for version in applied_migrations:
                logger.info(f"  ✅ Migration {version} applied")
        else:
            logger.info("ℹ️ No migrations were applied")
        
        # Проверяем финальный статус
        final_status = await migration_manager.get_migration_status()
        logger.info(f"📊 Final status: {final_status['applied_migrations']}/{final_status['total_migrations']} applied")
        
        if final_status['pending_migrations'] == 0:
            logger.info("🎉 All migrations applied successfully!")
            return True
        else:
            logger.error(f"❌ {final_status['pending_migrations']} migrations still pending")
            return False
            
    except Exception as e:
        logger.error(f"💥 Migration failed: {e}", exc_info=True)
        return False
    
    finally:
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}", exc_info=True)
        sys.exit(1)
