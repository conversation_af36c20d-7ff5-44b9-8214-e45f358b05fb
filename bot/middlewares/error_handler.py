"""
Middleware для централизованной обработки ошибок в VPN боте.
Обеспечивает graceful degradation и retry механизмы.
"""

import asyncio
import traceback
from typing import Callable, Dict, Any, Awaitable, Optional
from datetime import datetime, timedelta

from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, Update, Message, CallbackQuery
from aiogram.utils.i18n import gettext as _

from utils.logging_config import get_logger, set_correlation_id, log_user_action
from utils.marzban_api import MarzbanAPIError


logger = get_logger(__name__)


class ErrorType:
    """Типы ошибок для классификации"""
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    DATABASE_ERROR = "database_error"
    VALIDATION_ERROR = "validation_error"
    PERMISSION_ERROR = "permission_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    UNKNOWN_ERROR = "unknown_error"


class RetryConfig:
    """Конфигурация для retry механизма"""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
    
    def get_delay(self, attempt: int) -> float:
        """Вычисляет задержку для попытки с exponential backoff"""
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        return min(delay, self.max_delay)


class ErrorHandler(BaseMiddleware):
    """
    Middleware для обработки ошибок с retry механизмом и graceful degradation.
    """
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        self.retry_config = retry_config or RetryConfig()
        self._error_counts = {}  # Счетчик ошибок для rate limiting
        self._last_error_time = {}  # Время последней ошибки
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        """Основной метод middleware"""
        
        # Устанавливаем correlation ID для трассировки
        correlation_id = set_correlation_id()
        
        # Получаем информацию о пользователе
        user_id = self._get_user_id(event)
        event_type = self._get_event_type(event)
        
        logger.info(
            f"Processing {event_type} from user {user_id}",
            extra={
                "user_id": user_id,
                "event_type": event_type,
                "correlation_id": correlation_id
            }
        )
        
        # Проверяем rate limiting для пользователя
        if await self._is_rate_limited(user_id):
            await self._send_rate_limit_message(event)
            return
        
        # Выполняем handler с retry механизмом
        for attempt in range(1, self.retry_config.max_attempts + 1):
            try:
                result = await handler(event, data)
                
                # Сбрасываем счетчик ошибок при успешном выполнении
                if user_id in self._error_counts:
                    del self._error_counts[user_id]
                
                logger.info(
                    f"Successfully processed {event_type} for user {user_id}",
                    extra={
                        "user_id": user_id,
                        "event_type": event_type,
                        "attempt": attempt
                    }
                )
                
                return result
                
            except Exception as e:
                error_type = self._classify_error(e)
                
                logger.error(
                    f"Error processing {event_type} for user {user_id}: {str(e)}",
                    extra={
                        "user_id": user_id,
                        "event_type": event_type,
                        "error_type": error_type,
                        "attempt": attempt,
                        "max_attempts": self.retry_config.max_attempts,
                        "exception_type": type(e).__name__,
                        "traceback": traceback.format_exc()
                    },
                    exc_info=True
                )
                
                # Увеличиваем счетчик ошибок
                self._increment_error_count(user_id)
                
                # Проверяем, нужно ли повторить попытку
                if attempt < self.retry_config.max_attempts and self._should_retry(e, error_type):
                    delay = self.retry_config.get_delay(attempt)
                    logger.info(
                        f"Retrying in {delay}s (attempt {attempt + 1}/{self.retry_config.max_attempts})",
                        extra={
                            "user_id": user_id,
                            "delay": delay,
                            "attempt": attempt + 1
                        }
                    )
                    await asyncio.sleep(delay)
                    continue
                
                # Отправляем пользователю понятное сообщение об ошибке
                await self._handle_error_response(event, e, error_type, user_id)
                
                # Логируем финальную неудачу
                log_user_action(
                    user_id=user_id,
                    action="error_final_failure",
                    error_type=error_type,
                    attempts=attempt,
                    exception=str(e)
                )
                
                break
    
    def _get_user_id(self, event: TelegramObject) -> Optional[int]:
        """Извлекает ID пользователя из события"""
        if hasattr(event, 'from_user') and event.from_user:
            return event.from_user.id
        elif hasattr(event, 'message') and event.message and event.message.from_user:
            return event.message.from_user.id
        return None
    
    def _get_event_type(self, event: TelegramObject) -> str:
        """Определяет тип события"""
        if isinstance(event, Message):
            return "message"
        elif isinstance(event, CallbackQuery):
            return "callback_query"
        elif isinstance(event, Update):
            return "update"
        return type(event).__name__.lower()
    
    def _classify_error(self, error: Exception) -> str:
        """Классифицирует тип ошибки"""
        error_name = type(error).__name__.lower()
        error_message = str(error).lower()
        
        # Сетевые ошибки
        if any(keyword in error_name for keyword in ['connection', 'timeout', 'network']):
            return ErrorType.NETWORK_ERROR
        
        # Ошибки API
        if isinstance(error, MarzbanAPIError) or 'api' in error_message:
            return ErrorType.API_ERROR
        
        # Ошибки базы данных
        if any(keyword in error_name for keyword in ['database', 'sql', 'connection']):
            return ErrorType.DATABASE_ERROR
        
        # Ошибки валидации
        if any(keyword in error_name for keyword in ['validation', 'value', 'type']):
            return ErrorType.VALIDATION_ERROR
        
        # Ошибки прав доступа
        if any(keyword in error_name for keyword in ['permission', 'access', 'forbidden']):
            return ErrorType.PERMISSION_ERROR
        
        # Rate limiting
        if 'rate' in error_message or 'limit' in error_message:
            return ErrorType.RATE_LIMIT_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    def _should_retry(self, error: Exception, error_type: str) -> bool:
        """Определяет, стоит ли повторить попытку"""
        # Не повторяем для ошибок валидации и прав доступа
        if error_type in [ErrorType.VALIDATION_ERROR, ErrorType.PERMISSION_ERROR]:
            return False
        
        # Повторяем для сетевых ошибок и временных проблем API
        if error_type in [ErrorType.NETWORK_ERROR, ErrorType.API_ERROR, ErrorType.DATABASE_ERROR]:
            return True
        
        return False
    
    async def _handle_error_response(
        self, 
        event: TelegramObject, 
        error: Exception, 
        error_type: str, 
        user_id: Optional[int]
    ) -> None:
        """Отправляет пользователю понятное сообщение об ошибке"""
        
        # Определяем сообщение в зависимости от типа ошибки
        if error_type == ErrorType.NETWORK_ERROR:
            message = _("🔄 Временные проблемы с сетью. Попробуйте позже.")
        elif error_type == ErrorType.API_ERROR:
            message = _("⚠️ Временные проблемы с сервисом. Мы уже работаем над решением.")
        elif error_type == ErrorType.DATABASE_ERROR:
            message = _("💾 Временные проблемы с базой данных. Попробуйте позже.")
        elif error_type == ErrorType.VALIDATION_ERROR:
            message = _("❌ Некорректные данные. Проверьте введенную информацию.")
        elif error_type == ErrorType.PERMISSION_ERROR:
            message = _("🚫 Недостаточно прав для выполнения операции.")
        elif error_type == ErrorType.RATE_LIMIT_ERROR:
            message = _("⏱️ Слишком много запросов. Подождите немного.")
        else:
            message = _("❌ Произошла ошибка. Попробуйте позже или обратитесь в поддержку.")
        
        # Отправляем сообщение пользователю
        try:
            if isinstance(event, Message):
                await event.answer(message)
            elif isinstance(event, CallbackQuery):
                await event.answer(message, show_alert=True)
        except Exception as e:
            logger.error(f"Failed to send error message to user {user_id}: {e}")
    
    def _increment_error_count(self, user_id: Optional[int]) -> None:
        """Увеличивает счетчик ошибок для пользователя"""
        if user_id is None:
            return
        
        current_time = datetime.utcnow()
        self._error_counts[user_id] = self._error_counts.get(user_id, 0) + 1
        self._last_error_time[user_id] = current_time
    
    async def _is_rate_limited(self, user_id: Optional[int]) -> bool:
        """Проверяет, не превышен ли лимит ошибок для пользователя"""
        if user_id is None:
            return False
        
        current_time = datetime.utcnow()
        error_count = self._error_counts.get(user_id, 0)
        last_error_time = self._last_error_time.get(user_id)
        
        # Сбрасываем счетчик если прошло больше часа
        if last_error_time and current_time - last_error_time > timedelta(hours=1):
            if user_id in self._error_counts:
                del self._error_counts[user_id]
            if user_id in self._last_error_time:
                del self._last_error_time[user_id]
            return False
        
        # Блокируем если больше 10 ошибок за час
        return error_count > 10
    
    async def _send_rate_limit_message(self, event: TelegramObject) -> None:
        """Отправляет сообщение о превышении лимита"""
        message = _("⏱️ Слишком много ошибок. Попробуйте через час.")
        
        try:
            if isinstance(event, Message):
                await event.answer(message)
            elif isinstance(event, CallbackQuery):
                await event.answer(message, show_alert=True)
        except Exception as e:
            logger.error(f"Failed to send rate limit message: {e}")


# Создаем глобальный экземпляр для использования
error_handler = ErrorHandler()
