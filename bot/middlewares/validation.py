"""
Middleware для валидации входящих данных в Telegram боте.
"""

from typing import Callable, Dict, Any, Awaitable, Union
from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery, PreCheckoutQuery
from aiohttp.web_request import Request
from aiohttp import web

from schemas.user_input import (
    validate_user_input, 
    parse_callback_data, 
    ValidationError,
    TelegramUserData
)
from schemas.webhooks import (
    validate_yookassa_webhook,
    validate_cryptomus_webhook,
    WebhookValidationError
)
from utils.logging_config import get_logger

logger = get_logger(__name__)


class TelegramValidationMiddleware(BaseMiddleware):
    """
    Middleware для валидации данных от Telegram.
    Проверяет и нормализует входящие сообщения, callback'и и другие данные.
    """
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        """
        Обрабатывает входящие события от Telegram.
        
        Args:
            handler: Следующий обработчик в цепочке
            event: Событие от Telegram
            data: Данные события
            
        Returns:
            Результат обработки
        """
        try:
            # Валидируем разные типы событий
            if isinstance(event, Message):
                await self._validate_message(event, data)
            elif isinstance(event, CallbackQuery):
                await self._validate_callback_query(event, data)
            elif isinstance(event, PreCheckoutQuery):
                await self._validate_pre_checkout_query(event, data)
            
            # Продолжаем обработку
            return await handler(event, data)
            
        except ValidationError as e:
            logger.warning(
                f"Validation error for {type(event).__name__}: {e}",
                extra={
                    "event_type": type(event).__name__,
                    "user_id": getattr(event.from_user, 'id', None) if hasattr(event, 'from_user') else None,
                    "error": str(e),
                    "validation_error": True
                }
            )
            
            # Отправляем пользователю понятное сообщение об ошибке
            await self._send_validation_error_message(event, str(e))
            return None
            
        except Exception as e:
            logger.error(
                f"Unexpected error in validation middleware: {e}",
                exc_info=True,
                extra={
                    "event_type": type(event).__name__,
                    "user_id": getattr(event.from_user, 'id', None) if hasattr(event, 'from_user') else None,
                    "error": str(e)
                }
            )
            # Пропускаем событие дальше при неожиданных ошибках
            return await handler(event, data)
    
    async def _validate_message(self, message: Message, data: Dict[str, Any]) -> None:
        """Валидирует текстовое сообщение."""
        if not message.text:
            return  # Пропускаем не-текстовые сообщения
        
        user_data = {
            "id": message.from_user.id,
            "first_name": message.from_user.first_name,
            "last_name": message.from_user.last_name,
            "username": message.from_user.username,
            "language_code": message.from_user.language_code,
            "is_bot": message.from_user.is_bot
        }
        
        validated_input = validate_user_input(
            text=message.text,
            user_data=user_data,
            chat_id=message.chat.id
        )
        
        # Добавляем валидированные данные в контекст
        data['validated_input'] = validated_input
        data['validated_user'] = validated_input.user
        
        logger.debug(
            f"Message validated successfully",
            extra={
                "user_id": message.from_user.id,
                "chat_id": message.chat.id,
                "message_type": type(validated_input).__name__,
                "event_type": "message_validation"
            }
        )
    
    async def _validate_callback_query(self, callback: CallbackQuery, data: Dict[str, Any]) -> None:
        """Валидирует callback query."""
        if not callback.data:
            raise ValidationError("Callback data is empty")
        
        # Валидируем callback данные
        validated_callback = parse_callback_data(callback.data)
        
        # Валидируем данные пользователя
        user_data = {
            "id": callback.from_user.id,
            "first_name": callback.from_user.first_name,
            "last_name": callback.from_user.last_name,
            "username": callback.from_user.username,
            "language_code": callback.from_user.language_code,
            "is_bot": callback.from_user.is_bot
        }
        
        validated_user = TelegramUserData(**user_data)
        
        # Добавляем валидированные данные в контекст
        data['validated_callback'] = validated_callback
        data['validated_user'] = validated_user
        
        logger.debug(
            f"Callback query validated successfully",
            extra={
                "user_id": callback.from_user.id,
                "callback_data": callback.data,
                "callback_type": type(validated_callback).__name__,
                "event_type": "callback_validation"
            }
        )
    
    async def _validate_pre_checkout_query(self, query: PreCheckoutQuery, data: Dict[str, Any]) -> None:
        """Валидирует pre-checkout query."""
        if not query.invoice_payload:
            raise ValidationError("Invoice payload is empty")
        
        # Валидируем payload как callback товара
        if not query.invoice_payload.replace('_', '').replace('-', '').isalnum():
            raise ValidationError("Invalid invoice payload format")
        
        # Валидируем сумму
        if query.total_amount <= 0:
            raise ValidationError("Invalid payment amount")
        
        if query.total_amount > 2500:  # Максимум для Telegram Stars
            raise ValidationError("Payment amount exceeds maximum limit")
        
        # Валидируем валюту
        if query.currency != "XTR":
            raise ValidationError("Invalid payment currency")
        
        logger.debug(
            f"Pre-checkout query validated successfully",
            extra={
                "user_id": query.from_user.id,
                "invoice_payload": query.invoice_payload,
                "total_amount": query.total_amount,
                "currency": query.currency,
                "event_type": "pre_checkout_validation"
            }
        )
    
    async def _send_validation_error_message(self, event: TelegramObject, error_message: str) -> None:
        """Отправляет пользователю сообщение об ошибке валидации."""
        try:
            # Определяем, как отправить сообщение об ошибке
            if isinstance(event, Message):
                await event.answer("❌ Некорректные данные. Попробуйте еще раз.")
            elif isinstance(event, CallbackQuery):
                await event.answer("❌ Некорректный запрос", show_alert=True)
            elif isinstance(event, PreCheckoutQuery):
                await event.answer(ok=False, error_message="❌ Ошибка валидации платежа")
                
        except Exception as e:
            logger.error(f"Failed to send validation error message: {e}")


class WebhookValidationMiddleware:
    """
    Middleware для валидации webhook данных от платежных систем.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    async def validate_yookassa_webhook(self, request: Request) -> web.Response:
        """
        Валидирует webhook от YooKassa.
        
        Args:
            request: HTTP запрос
            
        Returns:
            HTTP ответ или None для продолжения обработки
        """
        try:
            # Получаем JSON данные
            data = await request.json()
            
            # Валидируем структуру данных
            validated_webhook = validate_yookassa_webhook(data)
            
            # Добавляем валидированные данные в request
            request['validated_webhook'] = validated_webhook
            
            self.logger.info(
                f"YooKassa webhook validated successfully",
                extra={
                    "payment_id": validated_webhook.object.id,
                    "status": validated_webhook.object.status,
                    "amount": str(validated_webhook.object.amount.value),
                    "event_type": "yookassa_webhook_validation"
                }
            )
            
            return None  # Продолжаем обработку
            
        except WebhookValidationError as e:
            self.logger.warning(
                f"YooKassa webhook validation failed: {e}",
                extra={
                    "error": str(e),
                    "client_ip": request.remote,
                    "event_type": "yookassa_webhook_validation_error"
                }
            )
            return web.Response(status=400, text="Invalid webhook data")
            
        except Exception as e:
            self.logger.error(
                f"Unexpected error in YooKassa webhook validation: {e}",
                exc_info=True,
                extra={
                    "error": str(e),
                    "client_ip": request.remote
                }
            )
            return web.Response(status=500, text="Internal server error")
    
    async def validate_cryptomus_webhook(self, request: Request) -> web.Response:
        """
        Валидирует webhook от Cryptomus.
        
        Args:
            request: HTTP запрос
            
        Returns:
            HTTP ответ или None для продолжения обработки
        """
        try:
            # Получаем JSON данные
            data = await request.json()
            
            # Валидируем структуру данных
            validated_webhook = validate_cryptomus_webhook(data)
            
            # Добавляем валидированные данные в request
            request['validated_webhook'] = validated_webhook
            
            self.logger.info(
                f"Cryptomus webhook validated successfully",
                extra={
                    "order_id": validated_webhook.order_id,
                    "status": validated_webhook.status,
                    "amount": validated_webhook.amount,
                    "currency": validated_webhook.currency,
                    "event_type": "cryptomus_webhook_validation"
                }
            )
            
            return None  # Продолжаем обработку
            
        except WebhookValidationError as e:
            self.logger.warning(
                f"Cryptomus webhook validation failed: {e}",
                extra={
                    "error": str(e),
                    "client_ip": request.remote,
                    "event_type": "cryptomus_webhook_validation_error"
                }
            )
            return web.Response(status=400, text="Invalid webhook data")
            
        except Exception as e:
            self.logger.error(
                f"Unexpected error in Cryptomus webhook validation: {e}",
                exc_info=True,
                extra={
                    "error": str(e),
                    "client_ip": request.remote
                }
            )
            return web.Response(status=500, text="Internal server error")


# Функции для регистрации middleware

def setup_telegram_validation_middleware(dp):
    """Регистрирует middleware валидации для Telegram бота."""
    dp.message.middleware(TelegramValidationMiddleware())
    dp.callback_query.middleware(TelegramValidationMiddleware())
    dp.pre_checkout_query.middleware(TelegramValidationMiddleware())
    
    logger.info("Telegram validation middleware registered")


def setup_webhook_validation_middleware(app):
    """Регистрирует middleware валидации для webhook'ов."""
    webhook_middleware = WebhookValidationMiddleware()
    
    # Добавляем middleware для конкретных маршрутов
    # Это будет сделано в routes.py при обновлении handlers
    
    logger.info("Webhook validation middleware setup completed")
