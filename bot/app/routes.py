import uuid
import logging
import ipaddress
import time

from aiohttp.web_request import Request
from aiohttp import web

from repositories.adapters import (
    get_marzban_profile_db,
    get_yookassa_payment,
    get_cryptomus_payment,
    delete_payment
)
from keyboards import get_main_menu_keyboard
from utils import webhook_data, goods, marzban_api
from utils import get_i18n_string
from schemas.webhooks_simple import (
    validate_yookassa_webhook,
    validate_cryptomus_webhook,
    WebhookValidationError
)
from utils.logging_config import get_logger
import glv

logger = get_logger(__name__)

YOOKASSA_IPS = (
    "***********/27",
    "***********/27",
    "***********/25",
    "************",
    "************",
    "*************/25",
    "2a02:5180::/32"
)

async def check_crypto_payment(request: Request):
    client_ip = request.headers.get('CF-Connecting-IP') or request.headers.get('X-Real-IP') or request.headers.get('X-Forwarded-For') or request.remote

    # Проверка IP адреса
    if client_ip not in ["*************"]:
        logger.warning(f"Cryptomus webhook from unauthorized IP: {client_ip}")
        return web.Response(status=403)

    try:
        # Получаем и валидируем данные
        data = await request.json()

        # Валидируем структуру webhook данных
        try:
            validated_webhook = validate_cryptomus_webhook(data)
        except WebhookValidationError as e:
            logger.warning(f"Cryptomus webhook validation failed: {e}")
            return web.Response(status=400, text="Invalid webhook data")

        # Проверяем подпись
        if not webhook_data.check(data, glv.config['CRYPTO_TOKEN']):
            logger.warning("Cryptomus webhook signature verification failed")
            return web.Response(status=403)

        # Получаем платеж из БД
        payment = await get_cryptomus_payment(validated_webhook.order_id)
        if payment is None:
            logger.warning(f"Payment not found for order_id: {validated_webhook.order_id}")
            return web.Response()

        logger.info(
            f"Processing Cryptomus webhook",
            extra={
                "order_id": validated_webhook.order_id,
                "status": validated_webhook.status,
                "amount": validated_webhook.amount,
                "user_id": payment.tg_id,
                "event_type": "cryptomus_webhook_processing"
            }
        )

        # Обрабатываем успешные платежи
        if validated_webhook.status in ['paid', 'paid_over']:
            good = goods.get(payment.callback)
            user = await get_marzban_profile_db(payment.tg_id)
            await marzban_api.generate_marzban_subscription(user.vpn_id, good)

            text = get_i18n_string(
                "Thank you for choice ❤️\n️\n<a href=\"{link}\">Subscribe</a> so you don't miss any announcements ✅\n️\nYour subscription is purchased and available in the \"My subscription 👤\" section.",
                payment.lang
            )

            await glv.bot.send_message(
                payment.chat_id,
                text.format(link=glv.config['TG_INFO_CHANEL']),
                reply_markup=get_main_menu_keyboard(True, payment.lang)
            )

            await delete_payment(payment.payment_uuid)

            logger.info(
                f"Cryptomus payment processed successfully",
                extra={
                    "order_id": validated_webhook.order_id,
                    "user_id": payment.tg_id,
                    "amount": validated_webhook.amount,
                    "event_type": "cryptomus_payment_success"
                }
            )

        # Обрабатываем отмененные платежи
        elif validated_webhook.status == 'cancel':
            await delete_payment(payment.payment_uuid)

            logger.info(
                f"Cryptomus payment canceled",
                extra={
                    "order_id": validated_webhook.order_id,
                    "user_id": payment.tg_id,
                    "event_type": "cryptomus_payment_canceled"
                }
            )

        return web.Response()

    except Exception as e:
        logger.error(
            f"Error processing Cryptomus webhook: {e}",
            exc_info=True,
            extra={
                "client_ip": client_ip,
                "event_type": "cryptomus_webhook_error"
            }
        )
        return web.Response(status=500)

async def check_yookassa_payment(request: Request):
    client_ip = request.headers.get('CF-Connecting-IP') or request.headers.get('X-Real-IP') or request.headers.get('X-Forwarded-For') or request.remote

    # Проверка IP адреса
    ip_allowed = False
    try:
        for subnet in YOOKASSA_IPS:
            if "/" in subnet:
                if ipaddress.ip_address(client_ip) in ipaddress.ip_network(subnet):
                    ip_allowed = True
                    break
            else:
                if client_ip == subnet:
                    ip_allowed = True
                    break
    except (ipaddress.AddressValueError, ValueError) as e:
        logger.warning(f"Invalid IP address in YooKassa webhook: {client_ip}, error: {e}")
        return web.Response(status=403)

    if not ip_allowed:
        logger.warning(f"YooKassa webhook from unauthorized IP: {client_ip}")
        return web.Response(status=403)

    try:
        # Получаем и валидируем данные
        raw_data = await request.json()

        # Валидируем структуру webhook данных
        try:
            validated_webhook = validate_yookassa_webhook(raw_data)
        except WebhookValidationError as e:
            logger.warning(f"YooKassa webhook validation failed: {e}")
            return web.Response(status=400, text="Invalid webhook data")

        payment_object = validated_webhook.object

        # Получаем платеж из БД
        payment = await get_yookassa_payment(payment_object.id)
        if payment is None:
            logger.warning(f"Payment not found for payment_id: {payment_object.id}")
            return web.Response()

        logger.info(
            f"Processing YooKassa webhook",
            extra={
                "payment_id": payment_object.id,
                "status": payment_object.status,
                "amount": payment_object.amount.value,
                "currency": payment_object.amount.currency,
                "user_id": payment.tg_id,
                "event_type": "yookassa_webhook_processing"
            }
        )

        # Обрабатываем успешные платежи
        if payment_object.status == 'succeeded':
            good = goods.get(payment.callback)
            user = await get_marzban_profile_db(payment.tg_id)
            await marzban_api.generate_marzban_subscription(user.vpn_id, good)

            text = get_i18n_string(
                "Thank you for choice ❤️\n️\n<a href=\"{link}\">Subscribe</a> so you don't miss any announcements ✅\n️\nYour subscription is purchased and available in the \"My subscription 👤\" section.",
                payment.lang
            )

            await glv.bot.send_message(
                payment.chat_id,
                text.format(link=glv.config['TG_INFO_CHANEL']),
                reply_markup=get_main_menu_keyboard(True, payment.lang)
            )

            await delete_payment(payment.payment_id)

            logger.info(
                f"YooKassa payment processed successfully",
                extra={
                    "payment_id": payment_object.id,
                    "user_id": payment.tg_id,
                    "amount": payment_object.amount.value,
                    "event_type": "yookassa_payment_success"
                }
            )

        # Обрабатываем отмененные платежи
        elif payment_object.status == 'canceled':
            await delete_payment(payment.payment_id)

            logger.info(
                f"YooKassa payment canceled",
                extra={
                    "payment_id": payment_object.id,
                    "user_id": payment.tg_id,
                    "event_type": "yookassa_payment_canceled"
                }
            )

        return web.Response()

    except Exception as e:
        logger.error(
            f"Error processing YooKassa webhook: {e}",
            exc_info=True,
            extra={
                "client_ip": client_ip,
                "event_type": "yookassa_webhook_error"
            }
        )
        return web.Response(status=500)
