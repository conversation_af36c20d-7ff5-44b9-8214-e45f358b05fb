"""
HTTP сервер для мониторинга VPN бота.
Предоставляет endpoints для health checks и метрик Prometheus.
"""

import json
import asyncio
from datetime import datetime
from typing import Optional
from aiohttp import web, web_request, web_response
from aiohttp.web_runner import GracefulExit

# Импортируем health_checker только при необходимости, чтобы избежать циклических импортов
from utils.health_checks import HealthStatus
from utils.metrics import metrics, get_metrics_content
from utils.logging_config import get_logger

# Импорты для системы алертов
from monitoring.alerts import AlertManager, TelegramAlertChannel
from monitoring.alert_rules import create_default_alert_rules
from monitoring.dashboard import AlertDashboard
from monitoring.log_monitor import setup_log_monitoring, get_log_metrics


logger = get_logger(__name__)


class MonitoringServer:
    """
    HTTP сервер для мониторинга состояния бота.
    """
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8000):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None

        # Инициализация системы алертов
        self.alert_manager: Optional[AlertManager] = None
        self.alert_dashboard: Optional[AlertDashboard] = None

        self._setup_routes()
        self._setup_middleware()
        self._setup_alerts()
    
    def _setup_routes(self) -> None:
        """Настраивает маршруты"""
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/health/ready', self.readiness_check)
        self.app.router.add_get('/health/live', self.liveness_check)
        self.app.router.add_get('/metrics', self.metrics_endpoint)
        self.app.router.add_get('/status', self.status_endpoint)
        self.app.router.add_get('/', self.root_endpoint)

        # Маршруты для алертов
        self.app.router.add_get('/alerts', self.alerts_dashboard)
        self.app.router.add_get('/api/alerts/status', self.alerts_status_api)
        self.app.router.add_get('/api/alerts/active', self.alerts_active_api)

        # Маршруты для БД метрик
        self.app.router.add_get('/api/db/status', self.db_status_endpoint)
        self.app.router.add_get('/api/db/performance', self.db_performance_endpoint)
        self.app.router.add_get('/api/db/cache', self.db_cache_endpoint)
    
    def _setup_middleware(self) -> None:
        """Настраивает middleware"""
        @web.middleware
        async def logging_middleware(request: web_request.Request, handler):
            start_time = asyncio.get_event_loop().time()
            
            try:
                response = await handler(request)
                duration = asyncio.get_event_loop().time() - start_time
                
                logger.info(
                    f"HTTP {request.method} {request.path} - {response.status}",
                    extra={
                        "method": request.method,
                        "path": request.path,
                        "status": response.status,
                        "duration_ms": duration * 1000,
                        "remote_addr": request.remote,
                        "user_agent": request.headers.get('User-Agent', ''),
                        "event_type": "http_request"
                    }
                )
                
                # Записываем метрику
                metrics.record_api_request(
                    endpoint=request.path,
                    method=request.method,
                    status=str(response.status),
                    duration=duration
                )
                
                return response
                
            except Exception as e:
                duration = asyncio.get_event_loop().time() - start_time
                
                logger.error(
                    f"HTTP {request.method} {request.path} - ERROR: {str(e)}",
                    extra={
                        "method": request.method,
                        "path": request.path,
                        "error": str(e),
                        "duration_ms": duration * 1000,
                        "remote_addr": request.remote,
                        "event_type": "http_error"
                    },
                    exc_info=True
                )
                
                # Записываем метрику ошибки
                metrics.record_api_request(
                    endpoint=request.path,
                    method=request.method,
                    status="500",
                    duration=duration
                )
                
                raise
        
        self.app.middlewares.append(logging_middleware)

    def _setup_alerts(self) -> None:
        """Настраивает систему алертов."""
        try:
            # Инициализируем менеджер алертов
            self.alert_manager = AlertManager()

            # Добавляем правила алертов
            rules = create_default_alert_rules()
            for rule in rules.values():
                self.alert_manager.add_rule(rule)

            # Создаем dashboard
            self.alert_dashboard = AlertDashboard(self.alert_manager)

            # Настраиваем мониторинг логов
            setup_log_monitoring()

            logger.info("Alert system configured successfully")

        except Exception as e:
            logger.error(f"Failed to setup alerts: {e}", exc_info=True)

    def _get_health_checker(self):
        """Ленивый импорт health_checker для избежания циклических импортов"""
        from utils.health_checks import health_checker
        return health_checker

    async def health_check(self, request: web_request.Request) -> web_response.Response:
        """
        Полная проверка здоровья всех компонентов.

        Returns:
            200 - если все компоненты здоровы
            503 - если есть критические проблемы
            200 - если есть незначительные проблемы (degraded)
        """
        try:
            health_checker = self._get_health_checker()
            results = await health_checker.check_all()
            overall_status = await health_checker.get_overall_status()
            
            # Определяем HTTP статус код
            if overall_status == HealthStatus.UNHEALTHY:
                status_code = 503
            elif overall_status == HealthStatus.DEGRADED:
                status_code = 200  # Degraded но все еще работает
            else:
                status_code = 200
            
            response_data = {
                "status": overall_status.value,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "components": {}
            }
            
            # Добавляем информацию о каждом компоненте
            for name, component in results.items():
                response_data["components"][name] = {
                    "status": component.status.value,
                    "message": component.message,
                    "response_time_ms": component.response_time_ms,
                    "last_check": component.last_check.isoformat() + "Z" if component.last_check else None,
                    "details": component.details
                }
            
            return web.json_response(
                response_data,
                status=status_code,
                headers={"Cache-Control": "no-cache"}
            )
            
        except Exception as e:
            logger.error(f"Health check failed: {e}", exc_info=True)
            return web.json_response(
                {
                    "status": "unhealthy",
                    "message": f"Health check failed: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                },
                status=503
            )
    
    async def readiness_check(self, request: web_request.Request) -> web_response.Response:
        """
        Проверка готовности к обслуживанию запросов.
        Проверяет только критические компоненты.
        
        Returns:
            200 - если система готова
            503 - если система не готова
        """
        try:
            health_checker = self._get_health_checker()
            is_ready = await health_checker.is_ready()
            
            if is_ready:
                return web.json_response(
                    {
                        "status": "ready",
                        "timestamp": datetime.utcnow().isoformat() + "Z"
                    },
                    status=200
                )
            else:
                return web.json_response(
                    {
                        "status": "not ready",
                        "timestamp": datetime.utcnow().isoformat() + "Z"
                    },
                    status=503
                )
                
        except Exception as e:
            logger.error(f"Readiness check failed: {e}", exc_info=True)
            return web.json_response(
                {
                    "status": "not ready",
                    "message": f"Readiness check failed: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                },
                status=503
            )
    
    async def liveness_check(self, request: web_request.Request) -> web_response.Response:
        """
        Простая проверка жизнеспособности приложения.
        
        Returns:
            200 - если приложение живо
            503 - если приложение не отвечает
        """
        try:
            health_checker = self._get_health_checker()
            is_alive = await health_checker.is_alive()
            
            if is_alive:
                return web.json_response(
                    {
                        "status": "alive",
                        "timestamp": datetime.utcnow().isoformat() + "Z"
                    },
                    status=200
                )
            else:
                return web.json_response(
                    {
                        "status": "not alive",
                        "timestamp": datetime.utcnow().isoformat() + "Z"
                    },
                    status=503
                )
                
        except Exception as e:
            logger.error(f"Liveness check failed: {e}", exc_info=True)
            return web.json_response(
                {
                    "status": "not alive",
                    "message": f"Liveness check failed: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                },
                status=503
            )
    
    async def metrics_endpoint(self, request: web_request.Request) -> web_response.Response:
        """
        Endpoint для метрик Prometheus.
        
        Returns:
            Метрики в формате Prometheus
        """
        try:
            content, content_type = get_metrics_content()
            
            return web.Response(
                text=content,
                content_type=content_type,
                headers={"Cache-Control": "no-cache"}
            )
            
        except Exception as e:
            logger.error(f"Metrics endpoint failed: {e}", exc_info=True)
            return web.Response(
                text=f"# Error generating metrics: {str(e)}\n",
                content_type="text/plain",
                status=500
            )
    
    async def status_endpoint(self, request: web_request.Request) -> web_response.Response:
        """
        Общий статус системы в JSON формате.
        
        Returns:
            Краткая информация о состоянии системы
        """
        try:
            health_checker = self._get_health_checker()
            overall_status = await health_checker.get_overall_status()
            is_ready = await health_checker.is_ready()
            is_alive = await health_checker.is_alive()
            
            return web.json_response(
                {
                    "service": "vpn-bot",
                    "version": "1.0.0",
                    "status": overall_status.value,
                    "ready": is_ready,
                    "alive": is_alive,
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "uptime_seconds": metrics._start_time if hasattr(metrics, '_start_time') else 0
                },
                status=200
            )
            
        except Exception as e:
            logger.error(f"Status endpoint failed: {e}", exc_info=True)
            return web.json_response(
                {
                    "service": "vpn-bot",
                    "status": "error",
                    "message": str(e),
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                },
                status=500
            )
    
    async def root_endpoint(self, request: web_request.Request) -> web_response.Response:
        """
        Корневой endpoint с информацией о доступных endpoints.
        """
        return web.json_response(
            {
                "service": "VPN Bot Monitoring",
                "version": "1.0.0",
                "endpoints": {
                    "/health": "Full health check of all components",
                    "/health/ready": "Readiness check for critical components",
                    "/health/live": "Liveness check",
                    "/metrics": "Prometheus metrics",
                    "/status": "General system status"
                },
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
        )

    async def alerts_dashboard(self, request: web_request.Request) -> web_response.Response:
        """Dashboard для отображения алертов."""
        if not self.alert_dashboard:
            return web.Response(
                text="Alert system not initialized",
                status=503
            )

        return await self.alert_dashboard.dashboard_endpoint(request)

    async def alerts_status_api(self, request: web_request.Request) -> web_response.Response:
        """API endpoint для статуса алертов."""
        if not self.alert_dashboard:
            return web.json_response(
                {"error": "Alert system not initialized"},
                status=503
            )

        return await self.alert_dashboard.api_status_endpoint(request)

    async def alerts_active_api(self, request: web_request.Request) -> web_response.Response:
        """API endpoint для активных алертов."""
        if not self.alert_dashboard:
            return web.json_response(
                {"error": "Alert system not initialized"},
                status=503
            )

        return await self.alert_dashboard.api_alerts_endpoint(request)

    async def db_status_endpoint(self, request: web_request.Request) -> web_response.Response:
        """API endpoint для статуса БД."""
        try:
            from db.base import get_pool_status, check_db_connection, get_engine_info

            # Получаем статус пула соединений
            pool_status = get_pool_status()

            # Проверяем соединение с БД
            connection_ok = await check_db_connection()

            # Получаем информацию о движке
            engine_info = get_engine_info()

            return web.json_response({
                "database": {
                    "connection": "healthy" if connection_ok else "unhealthy",
                    "pool": pool_status,
                    "engine": engine_info
                },
                "timestamp": datetime.utcnow().isoformat() + "Z"
            })

        except Exception as e:
            logger.error(f"DB status endpoint failed: {e}", exc_info=True)
            return web.json_response(
                {"error": f"Failed to get DB status: {str(e)}"},
                status=500
            )

    async def db_performance_endpoint(self, request: web_request.Request) -> web_response.Response:
        """API endpoint для метрик производительности БД."""
        try:
            from db.performance import performance_analyzer

            # Получаем анализ паттернов запросов
            query_analysis = await performance_analyzer.analyze_query_patterns()

            # Получаем предложения по оптимизации
            suggestions = await performance_analyzer.suggest_optimizations()

            # Получаем отчет о производительности
            performance_report = performance_analyzer.get_performance_report()

            return web.json_response({
                "performance": {
                    "query_analysis": query_analysis,
                    "optimization_suggestions": suggestions,
                    "report": performance_report
                },
                "timestamp": datetime.utcnow().isoformat() + "Z"
            })

        except Exception as e:
            logger.error(f"DB performance endpoint failed: {e}", exc_info=True)
            return web.json_response(
                {"error": f"Failed to get DB performance: {str(e)}"},
                status=500
            )

    async def db_cache_endpoint(self, request: web_request.Request) -> web_response.Response:
        """API endpoint для статистики кэша БД."""
        try:
            from db.cache import db_cache

            # Получаем статистику кэшей
            cache_stats = db_cache.get_cache_stats()

            return web.json_response({
                "cache": cache_stats,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            })

        except Exception as e:
            logger.error(f"DB cache endpoint failed: {e}", exc_info=True)
            return web.json_response(
                {"error": f"Failed to get DB cache stats: {str(e)}"},
                status=500
            )

    async def start(self) -> None:
        """Запускает HTTP сервер"""
        try:
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()

            # Запускаем систему алертов
            if self.alert_manager:
                await self.alert_manager.start(check_interval=30)
                logger.info("Alert manager started")

            logger.info(f"Monitoring server started on {self.host}:{self.port}")

        except Exception as e:
            logger.error(f"Failed to start monitoring server: {e}", exc_info=True)
            raise
    
    async def stop(self) -> None:
        """Останавливает HTTP сервер"""
        try:
            # Останавливаем систему алертов
            if self.alert_manager:
                await self.alert_manager.stop()
                logger.info("Alert manager stopped")

            if self.site:
                await self.site.stop()
                self.site = None

            if self.runner:
                await self.runner.cleanup()
                self.runner = None

            logger.info("Monitoring server stopped")

        except Exception as e:
            logger.error(f"Error stopping monitoring server: {e}", exc_info=True)


# Глобальный экземпляр сервера мониторинга
monitoring_server = MonitoringServer()
