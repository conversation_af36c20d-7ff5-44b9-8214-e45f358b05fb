import asyncio
import logging
import sys
import signal
from pathlib import Path

from aiogram import <PERSON><PERSON>, Di<PERSON>atch<PERSON>, enums
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.utils.i18n import I18n, SimpleI18nMiddleware

from handlers.commands import register_commands
from handlers.messages import register_messages
from handlers.callbacks import register_callbacks
from handlers.payments import register_payments
from middlewares.db_check import DBCheck
from middlewares.error_handler import error_handler
from tasks import setup_schedule, run_scheduler
from utils.logging_config import setup_logging, get_logger, set_correlation_id
from utils.metrics import metrics
from app.monitoring_server import monitoring_server
from services.base import initialize_services, cleanup_services
import glv

# Настраиваем структурированное логирование
setup_logging(
    service_name="vpn-bot",
    log_level=glv.config.get('LOG_LEVEL', 'INFO'),
    enable_json=glv.config.get('ENABLE_JSON_LOGS', True),
    enable_console=True
)

logger = get_logger(__name__)

glv.bot = Bot(glv.config['BOT_TOKEN'], parse_mode=enums.ParseMode.HTML)
glv.storage = MemoryStorage()
glv.dp = Dispatcher(storage=glv.storage)

async def on_startup(bot: Bot):
    """Инициализация при запуске бота"""
    correlation_id = set_correlation_id()

    logger.info(
        "Starting VPN bot...",
        extra={
            "correlation_id": correlation_id,
            "event_type": "bot_startup"
        }
    )

    try:
        # Удаляем webhook для использования polling
        await bot.delete_webhook(drop_pending_updates=True)
        logger.info("Webhook deleted successfully")

        # Инициализируем Service Layer
        await initialize_services()
        logger.info("Service Layer initialized")

        # Запускаем сервер мониторинга
        await monitoring_server.start()
        logger.info("Monitoring server started")

        # Настраиваем планировщик задач
        setup_schedule()
        asyncio.create_task(run_scheduler())
        logger.info("Scheduler started")

        # Записываем метрику запуска
        metrics.record_user_registration("system_start")

        logger.info(
            "VPN bot started successfully",
            extra={
                "correlation_id": correlation_id,
                "event_type": "bot_started"
            }
        )

    except Exception as e:
        logger.error(
            f"Failed to start VPN bot: {e}",
            extra={
                "correlation_id": correlation_id,
                "event_type": "bot_startup_error",
                "error": str(e)
            },
            exc_info=True
        )
        raise


async def on_shutdown(bot: Bot):
    """Очистка при остановке бота"""
    correlation_id = set_correlation_id()

    logger.info(
        "Shutting down VPN bot...",
        extra={
            "correlation_id": correlation_id,
            "event_type": "bot_shutdown"
        }
    )

    try:
        # Очищаем Service Layer
        await cleanup_services()
        logger.info("Service Layer cleaned up")

        # Останавливаем сервер мониторинга
        await monitoring_server.stop()
        logger.info("Monitoring server stopped")

        logger.info(
            "VPN bot shutdown completed",
            extra={
                "correlation_id": correlation_id,
                "event_type": "bot_shutdown_complete"
            }
        )

    except Exception as e:
        logger.error(
            f"Error during shutdown: {e}",
            extra={
                "correlation_id": correlation_id,
                "event_type": "bot_shutdown_error",
                "error": str(e)
            },
            exc_info=True
        )

def setup_routers():
    register_commands(glv.dp)
    register_messages(glv.dp)
    register_callbacks(glv.dp)
    register_payments(glv.dp)

def setup_middlewares():
    """Настройка middleware для обработки сообщений"""
    logger.info("Setting up middlewares...")

    # Настраиваем интернационализацию
    i18n = I18n(path=Path(__file__).parent / 'locales', default_locale='en', domain='bot')
    i18n_middleware = SimpleI18nMiddleware(i18n=i18n)
    i18n_middleware.setup(glv.dp)
    logger.info("I18n middleware configured")

    # Добавляем middleware для обработки ошибок (должен быть первым)
    glv.dp.message.middleware(error_handler)
    glv.dp.callback_query.middleware(error_handler)
    logger.info("Error handler middleware configured")

    # Добавляем middleware для проверки БД
    glv.dp.message.middleware(DBCheck())
    logger.info("Database check middleware configured")

    logger.info("All middlewares configured successfully")

async def main():
    """Основная функция запуска бота"""
    logger.info("Initializing VPN bot...")

    try:
        # Настраиваем роутеры и middleware
        setup_routers()
        setup_middlewares()

        # Регистрируем обработчики событий
        glv.dp.startup.register(on_startup)
        glv.dp.shutdown.register(on_shutdown)

        # Настраиваем graceful shutdown
        def signal_handler():
            logger.info("Received shutdown signal")
            glv.dp.stop_polling()

        # Регистрируем обработчики сигналов
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, lambda s, f: signal_handler())
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, lambda s, f: signal_handler())

        # Запускаем бота в polling режиме
        logger.info("Starting bot in polling mode...")
        await glv.dp.start_polling(glv.bot)

    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error in main: {e}", exc_info=True)
        raise
    finally:
        logger.info("Bot shutdown complete")

if __name__ == "__main__":
    asyncio.run(main())