"""Add encrypted config and user payment data tables

Revision ID: 9ae075a30549
Revises: 36159a9e6985
Create Date: 2025-06-03 16:47:43.720255

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9ae075a30549'
down_revision = '36159a9e6985'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('encrypted_config',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('key_name', sa.String(length=128), nullable=False),
    sa.Column('encrypted_value', sa.Text(), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_index(op.f('ix_encrypted_config_key_name'), 'encrypted_config', ['key_name'], unique=True)
    op.create_table('user_saved_payment_data',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('tg_id', sa.BigInteger(), nullable=False),
    sa.Column('payment_method', sa.String(length=64), nullable=False),
    sa.Column('encrypted_data', sa.Text(), nullable=False),
    sa.Column('data_hash', sa.String(length=128), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_index(op.f('ix_user_saved_payment_data_tg_id'), 'user_saved_payment_data', ['tg_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_saved_payment_data_tg_id'), table_name='user_saved_payment_data')
    op.drop_table('user_saved_payment_data')
    op.drop_index(op.f('ix_encrypted_config_key_name'), table_name='encrypted_config')
    op.drop_table('encrypted_config')
    # ### end Alembic commands ###
