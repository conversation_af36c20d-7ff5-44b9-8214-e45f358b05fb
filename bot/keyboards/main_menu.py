from aiogram.types import ReplyKeyboardMarkup, KeyboardButton
from aiogram.utils.i18n import gettext as _

from utils import get_i18n_string
import glv

def get_main_menu_keyboard(trial_expired: bool = False, lang=None) -> ReplyKeyboardMarkup:
    """
    Создает главное меню бота.

    Args:
        trial_expired: Истек ли тестовый период
        lang: Язык интерфейса

    Returns:
        ReplyKeyboardMarkup с кнопками меню
    """
    kb = []

    # Кнопка тестового периода (если не истек и включен в конфиге)
    if not trial_expired and glv.config.get('TEST_PERIOD', True):
        kb.append([KeyboardButton(text=get_i18n_str("5 days free 🆓", lang))])

    # Основные кнопки
    kb.extend([
        [KeyboardButton(text=get_i18n_str("Join 🏄🏻‍♂️", lang))],
        [
            KeyboardButton(text=get_i18n_str("My subscription 👤", lang)) if trial_expired
            else KeyboardButton(text=get_i18n_str("Frequent questions ℹ️", lang))
        ],
        [KeyboardButton(text=get_i18n_str("Support ❤️", lang))]
    ])

    # Если тестовый период истек, добавляем кнопку FAQ
    if trial_expired:
        kb.append([KeyboardButton(text=get_i18n_str("Frequent questions ℹ️", lang))])

    return ReplyKeyboardMarkup(keyboard=kb, resize_keyboard=True, is_persistent=True)

def get_i18n_str(text: str, lang = None):
    if lang is None:
        return _(text)
    return get_i18n_string(text, lang)
