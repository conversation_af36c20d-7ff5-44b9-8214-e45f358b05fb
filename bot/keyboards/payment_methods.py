"""
Клавиатуры для управления платежными методами.

Этот модуль содержит клавиатуры для работы с сохраненными
платежными методами и настройками автопродления.
"""

from typing import List, Optional
from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.utils.i18n import gettext as _

from schemas.payment_methods import SavedPaymentMethodResponse, AutoRenewalSettingsResponse


class PaymentMethodKeyboards:
    """Клавиатуры для управления платежными методами."""
    
    @staticmethod
    def get_payment_methods_keyboard(
        methods: List[SavedPaymentMethodResponse],
        auto_renewal_settings: Optional[List[AutoRenewalSettingsResponse]] = None
    ) -> InlineKeyboardMarkup:
        """Клавиатура со списком сохраненных методов оплаты."""
        builder = InlineKeyboardBuilder()
        
        # Добавляем кнопки для каждого метода
        for method in methods:
            status_emoji = "✅" if method.is_active else "❌"
            default_mark = "⭐ " if method.is_default else ""
            
            builder.row(
                InlineKeyboardButton(
                    text=f"{default_mark}{status_emoji} {method.display_name}",
                    callback_data=f"payment_method_{method.id}"
                )
            )
        
        # Разделитель
        builder.row(
            InlineKeyboardButton(
                text="➕ Добавить новый способ оплаты",
                callback_data="add_payment_method"
            )
        )
        
        # Настройки автопродления
        if auto_renewal_settings and auto_renewal_settings[0].is_enabled:
            builder.row(
                InlineKeyboardButton(
                    text="🔄 Настройки автопродления",
                    callback_data="auto_renewal_settings"
                )
            )
        else:
            builder.row(
                InlineKeyboardButton(
                    text="🔄 Настроить автопродление",
                    callback_data="setup_auto_renewal"
                )
            )
        
        # Кнопка назад
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="back_to_profile"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_empty_methods_keyboard() -> InlineKeyboardMarkup:
        """Клавиатура для случая, когда нет сохраненных методов."""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="💳 Купить подписку",
                callback_data="buy_subscription"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="back_to_profile"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_payment_method_details_keyboard(
        method_id: str,
        is_default: bool,
        is_active: bool
    ) -> InlineKeyboardMarkup:
        """Клавиатура с действиями для конкретного платежного метода."""
        builder = InlineKeyboardBuilder()
        
        # Тестирование метода
        if is_active:
            builder.row(
                InlineKeyboardButton(
                    text="🧪 Тестировать",
                    callback_data=f"test_method_{method_id}"
                )
            )
        
        # Установить по умолчанию (если еще не установлен)
        if not is_default and is_active:
            builder.row(
                InlineKeyboardButton(
                    text="⭐ Сделать основным",
                    callback_data=f"set_default_{method_id}"
                )
            )
        
        # Активировать/деактивировать
        if is_active:
            builder.row(
                InlineKeyboardButton(
                    text="❌ Деактивировать",
                    callback_data=f"deactivate_method_{method_id}"
                )
            )
        else:
            builder.row(
                InlineKeyboardButton(
                    text="✅ Активировать",
                    callback_data=f"activate_method_{method_id}"
                )
            )
        
        # Удалить метод
        builder.row(
            InlineKeyboardButton(
                text="🗑 Удалить",
                callback_data=f"delete_method_{method_id}"
            )
        )
        
        # Назад к списку
        builder.row(
            InlineKeyboardButton(
                text="⏪ К списку методов",
                callback_data="my_payment_methods"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_delete_confirmation_keyboard(method_id: str) -> InlineKeyboardMarkup:
        """Клавиатура подтверждения удаления платежного метода."""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="✅ Да, удалить",
                callback_data=f"confirm_delete_{method_id}"
            ),
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data=f"payment_method_{method_id}"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_auto_renewal_setup_keyboard() -> InlineKeyboardMarkup:
        """Клавиатура для настройки автопродления."""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="🔄 Включить автопродление",
                callback_data="enable_auto_renewal"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="⚙️ Настройки уведомлений",
                callback_data="auto_renewal_notifications"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="my_payment_methods"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_auto_renewal_settings_keyboard(
        settings: AutoRenewalSettingsResponse
    ) -> InlineKeyboardMarkup:
        """Клавиатура с настройками автопродления."""
        builder = InlineKeyboardBuilder()
        
        # Включить/выключить
        if settings.is_enabled:
            builder.row(
                InlineKeyboardButton(
                    text="❌ Отключить автопродление",
                    callback_data="disable_auto_renewal"
                )
            )
        else:
            builder.row(
                InlineKeyboardButton(
                    text="✅ Включить автопродление",
                    callback_data="enable_auto_renewal"
                )
            )
        
        # Настройки времени
        builder.row(
            InlineKeyboardButton(
                text=f"📅 Продлевать за {settings.renewal_days_before} дней",
                callback_data="change_renewal_days"
            )
        )
        
        # Настройки уведомлений
        builder.row(
            InlineKeyboardButton(
                text="🔔 Настройки уведомлений",
                callback_data="auto_renewal_notifications"
            )
        )
        
        # Изменить платежный метод
        builder.row(
            InlineKeyboardButton(
                text="💳 Изменить способ оплаты",
                callback_data="change_auto_renewal_method"
            )
        )
        
        # Назад
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="my_payment_methods"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_renewal_days_keyboard() -> InlineKeyboardMarkup:
        """Клавиатура для выбора количества дней до продления."""
        builder = InlineKeyboardBuilder()
        
        days_options = [1, 3, 5, 7, 14]
        
        for days in days_options:
            builder.row(
                InlineKeyboardButton(
                    text=f"{days} {'день' if days == 1 else 'дня' if days < 5 else 'дней'}",
                    callback_data=f"set_renewal_days_{days}"
                )
            )
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="auto_renewal_settings"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_notification_settings_keyboard(
        notify_success: bool,
        notify_failure: bool,
        notify_before: bool
    ) -> InlineKeyboardMarkup:
        """Клавиатура настроек уведомлений автопродления."""
        builder = InlineKeyboardBuilder()
        
        # Уведомления об успешном продлении
        success_text = "✅ Успешное продление" if notify_success else "❌ Успешное продление"
        builder.row(
            InlineKeyboardButton(
                text=success_text,
                callback_data="toggle_notify_success"
            )
        )
        
        # Уведомления о неудачном продлении
        failure_text = "✅ Неудачное продление" if notify_failure else "❌ Неудачное продление"
        builder.row(
            InlineKeyboardButton(
                text=failure_text,
                callback_data="toggle_notify_failure"
            )
        )
        
        # Уведомления перед продлением
        before_text = "✅ Напоминание перед продлением" if notify_before else "❌ Напоминание перед продлением"
        builder.row(
            InlineKeyboardButton(
                text=before_text,
                callback_data="toggle_notify_before"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="auto_renewal_settings"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_test_result_keyboard(method_id: str) -> InlineKeyboardMarkup:
        """Клавиатура после тестирования платежного метода."""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="🔄 Тестировать еще раз",
                callback_data=f"test_method_{method_id}"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ К деталям метода",
                callback_data=f"payment_method_{method_id}"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_success_keyboard() -> InlineKeyboardMarkup:
        """Клавиатура для успешных операций."""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ К платежным методам",
                callback_data="my_payment_methods"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def get_back_keyboard() -> InlineKeyboardMarkup:
        """Простая клавиатура с кнопкой назад."""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="my_payment_methods"
            )
        )
        
        return builder.as_markup()
