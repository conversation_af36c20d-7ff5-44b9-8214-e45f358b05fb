"""
Repository для работы с уведомлениями.

Этот модуль содержит абстрактный интерфейс и реализацию для работы
с данными уведомлений в базе данных.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.orm import selectinload

from db.models_notifications import (
    NotificationTemplate, NotificationSchedule, NotificationLog,
    NotificationPreference
)
from schemas.notification_enums import (
    NotificationType, NotificationStatus, DeliveryStatus,
    NotificationPriority, NotificationChannel
)
from utils.logging_config import get_logger

logger = get_logger(__name__)


class NotificationRepositoryInterface(ABC):
    """Абстрактный интерфейс для работы с уведомлениями."""
    
    @abstractmethod
    async def create_template(self, template: NotificationTemplate) -> NotificationTemplate:
        """Создает новый шаблон уведомления."""
        pass
    
    @abstractmethod
    async def get_template_by_id(self, template_id: str) -> Optional[NotificationTemplate]:
        """Получает шаблон по ID."""
        pass
    
    @abstractmethod
    async def get_template_by_type(self, notification_type: NotificationType) -> Optional[NotificationTemplate]:
        """Получает активный шаблон по типу уведомления."""
        pass
    
    @abstractmethod
    async def get_templates_by_type(self, notification_type: NotificationType) -> List[NotificationTemplate]:
        """Получает все активные шаблоны по типу (для A/B тестирования)."""
        pass
    
    @abstractmethod
    async def create_schedule(self, schedule: NotificationSchedule) -> NotificationSchedule:
        """Создает новое запланированное уведомление."""
        pass
    
    @abstractmethod
    async def get_pending_notifications(self, limit: int = 100) -> List[NotificationSchedule]:
        """Получает ожидающие отправки уведомления."""
        pass
    
    @abstractmethod
    async def update_schedule_status(self, schedule_id: str, status: NotificationStatus, 
                                   error_message: Optional[str] = None) -> bool:
        """Обновляет статус запланированного уведомления."""
        pass
    
    @abstractmethod
    async def cancel_scheduled_notifications(self, user_id: int, 
                                           notification_type: NotificationType) -> int:
        """Отменяет запланированные уведомления для пользователя."""
        pass
    
    @abstractmethod
    async def log_notification(self, log: NotificationLog) -> NotificationLog:
        """Записывает лог отправленного уведомления."""
        pass
    
    @abstractmethod
    async def get_user_preferences(self, user_id: int) -> List[NotificationPreference]:
        """Получает настройки уведомлений пользователя."""
        pass
    
    @abstractmethod
    async def update_user_preference(self, user_id: int, notification_type: NotificationType,
                                   is_enabled: bool) -> NotificationPreference:
        """Обновляет настройку уведомления для пользователя."""
        pass


class NotificationRepository(NotificationRepositoryInterface):
    """Реализация repository для работы с уведомлениями."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_template(self, template: NotificationTemplate) -> NotificationTemplate:
        """Создает новый шаблон уведомления."""
        try:
            self.session.add(template)
            await self.session.commit()
            await self.session.refresh(template)
            logger.info(f"Created notification template: {template.id}")
            return template
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create notification template: {e}")
            raise
    
    async def get_template_by_id(self, template_id: str) -> Optional[NotificationTemplate]:
        """Получает шаблон по ID."""
        try:
            result = await self.session.execute(
                select(NotificationTemplate).where(
                    NotificationTemplate.id == template_id
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get template by ID {template_id}: {e}")
            raise
    
    async def get_template_by_type(self, notification_type: NotificationType) -> Optional[NotificationTemplate]:
        """Получает активный шаблон по типу уведомления."""
        try:
            result = await self.session.execute(
                select(NotificationTemplate).where(
                    and_(
                        NotificationTemplate.notification_type == notification_type,
                        NotificationTemplate.is_active == True
                    )
                ).order_by(NotificationTemplate.created_at.desc())
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get template by type {notification_type}: {e}")
            raise
    
    async def get_templates_by_type(self, notification_type: NotificationType) -> List[NotificationTemplate]:
        """Получает все активные шаблоны по типу (для A/B тестирования)."""
        try:
            result = await self.session.execute(
                select(NotificationTemplate).where(
                    and_(
                        NotificationTemplate.notification_type == notification_type,
                        NotificationTemplate.is_active == True
                    )
                ).order_by(NotificationTemplate.ab_test_weight.desc())
            )
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get templates by type {notification_type}: {e}")
            raise
    
    async def create_schedule(self, schedule: NotificationSchedule) -> NotificationSchedule:
        """Создает новое запланированное уведомление."""
        try:
            self.session.add(schedule)
            await self.session.commit()
            await self.session.refresh(schedule)
            logger.info(f"Created notification schedule: {schedule.id}")
            return schedule
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create notification schedule: {e}")
            raise
    
    async def get_pending_notifications(self, limit: int = 100) -> List[NotificationSchedule]:
        """Получает ожидающие отправки уведомления."""
        try:
            result = await self.session.execute(
                select(NotificationSchedule)
                .options(selectinload(NotificationSchedule.template))
                .where(
                    and_(
                        NotificationSchedule.status == NotificationStatus.PENDING,
                        NotificationSchedule.scheduled_at <= datetime.utcnow(),
                        or_(
                            NotificationSchedule.expires_at.is_(None),
                            NotificationSchedule.expires_at > datetime.utcnow()
                        )
                    )
                )
                .order_by(NotificationSchedule.priority.desc(), NotificationSchedule.scheduled_at.asc())
                .limit(limit)
            )
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get pending notifications: {e}")
            raise
    
    async def update_schedule_status(self, schedule_id: str, status: NotificationStatus, 
                                   error_message: Optional[str] = None) -> bool:
        """Обновляет статус запланированного уведомления."""
        try:
            update_data = {
                "status": status,
                "attempts": NotificationSchedule.attempts + 1
            }
            
            if status == NotificationStatus.SENT:
                update_data["sent_at"] = datetime.utcnow()
            elif status == NotificationStatus.FAILED:
                update_data["failed_at"] = datetime.utcnow()
                if error_message:
                    update_data["error_message"] = error_message
            
            result = await self.session.execute(
                update(NotificationSchedule)
                .where(NotificationSchedule.id == schedule_id)
                .values(**update_data)
            )
            
            await self.session.commit()
            return result.rowcount > 0
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update schedule status {schedule_id}: {e}")
            raise
    
    async def cancel_scheduled_notifications(self, user_id: int, 
                                           notification_type: NotificationType) -> int:
        """Отменяет запланированные уведомления для пользователя."""
        try:
            result = await self.session.execute(
                update(NotificationSchedule)
                .where(
                    and_(
                        NotificationSchedule.user_id == user_id,
                        NotificationSchedule.notification_type == notification_type,
                        NotificationSchedule.status == NotificationStatus.PENDING
                    )
                )
                .values(status=NotificationStatus.CANCELLED)
            )
            
            await self.session.commit()
            cancelled_count = result.rowcount
            logger.info(f"Cancelled {cancelled_count} notifications for user {user_id}, type {notification_type}")
            return cancelled_count
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to cancel notifications for user {user_id}: {e}")
            raise
    
    async def log_notification(self, log: NotificationLog) -> NotificationLog:
        """Записывает лог отправленного уведомления."""
        try:
            self.session.add(log)
            await self.session.commit()
            await self.session.refresh(log)
            return log
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to log notification: {e}")
            raise
    
    async def get_user_preferences(self, user_id: int) -> List[NotificationPreference]:
        """Получает настройки уведомлений пользователя."""
        try:
            result = await self.session.execute(
                select(NotificationPreference).where(
                    NotificationPreference.user_id == user_id
                )
            )
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get user preferences for user {user_id}: {e}")
            raise
    
    async def update_user_preference(self, user_id: int, notification_type: NotificationType,
                                   is_enabled: bool) -> NotificationPreference:
        """Обновляет настройку уведомления для пользователя."""
        try:
            # Пытаемся найти существующую настройку
            result = await self.session.execute(
                select(NotificationPreference).where(
                    and_(
                        NotificationPreference.user_id == user_id,
                        NotificationPreference.notification_type == notification_type
                    )
                )
            )
            preference = result.scalar_one_or_none()
            
            if preference:
                # Обновляем существующую
                preference.is_enabled = is_enabled
                preference.updated_at = datetime.utcnow()
            else:
                # Создаем новую
                from utils.id_generator import generate_id
                preference = NotificationPreference(
                    id=generate_id(),
                    user_id=user_id,
                    notification_type=notification_type,
                    is_enabled=is_enabled
                )
                self.session.add(preference)
            
            await self.session.commit()
            await self.session.refresh(preference)
            return preference
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update user preference: {e}")
            raise
