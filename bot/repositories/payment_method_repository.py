"""
Repository для работы с сохраненными платежными методами.

Этот модуль содержит абстрактный интерфейс и реализацию для работы
с сохраненными платежными методами и настройками автопродления.
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from db.models_payment_methods import (
    SavedPaymentMethod, AutoRenewalSettings, PaymentMethodUsageLog,
    AutoRenewalQueue, PaymentMethodSecurityLog,
    PaymentMethodType, PaymentMethodStatus, AutoRenewalStatus
)
from utils.logging_config import get_logger

logger = get_logger(__name__)


class PaymentMethodRepositoryInterface(ABC):
    """Абстрактный интерфейс для работы с платежными методами."""
    
    @abstractmethod
    async def create_payment_method(self, payment_method: SavedPaymentMethod) -> SavedPaymentMethod:
        """Создает новый сохраненный платежный метод."""
        pass
    
    @abstractmethod
    async def get_payment_method_by_id(self, method_id: str) -> Optional[SavedPaymentMethod]:
        """Получает платежный метод по ID."""
        pass
    
    @abstractmethod
    async def get_user_payment_methods(self, user_id: int, 
                                     include_inactive: bool = False) -> List[SavedPaymentMethod]:
        """Получает все платежные методы пользователя."""
        pass
    
    @abstractmethod
    async def update_payment_method(self, method_id: str, 
                                  update_data: Dict[str, Any]) -> Optional[SavedPaymentMethod]:
        """Обновляет платежный метод."""
        pass
    
    @abstractmethod
    async def delete_payment_method(self, method_id: str) -> bool:
        """Удаляет платежный метод."""
        pass
    
    @abstractmethod
    async def set_default_payment_method(self, user_id: int, method_id: str) -> bool:
        """Устанавливает платежный метод по умолчанию."""
        pass
    
    @abstractmethod
    async def get_default_payment_method(self, user_id: int) -> Optional[SavedPaymentMethod]:
        """Получает платежный метод по умолчанию для пользователя."""
        pass
    
    @abstractmethod
    async def create_auto_renewal_settings(self, settings: AutoRenewalSettings) -> AutoRenewalSettings:
        """Создает настройки автопродления."""
        pass
    
    @abstractmethod
    async def get_auto_renewal_settings(self, user_id: int) -> Optional[AutoRenewalSettings]:
        """Получает настройки автопродления пользователя."""
        pass
    
    @abstractmethod
    async def update_auto_renewal_settings(self, settings_id: str,
                                         update_data: Dict[str, Any]) -> Optional[AutoRenewalSettings]:
        """Обновляет настройки автопродления."""
        pass
    
    @abstractmethod
    async def log_payment_method_usage(self, log: PaymentMethodUsageLog) -> PaymentMethodUsageLog:
        """Записывает лог использования платежного метода."""
        pass
    
    @abstractmethod
    async def get_payment_method_usage_history(self, user_id: int,
                                             limit: int = 50) -> List[PaymentMethodUsageLog]:
        """Получает историю использования платежных методов."""
        pass

    @abstractmethod
    async def get_payment_method_by_hash(self, data_hash: str) -> Optional[SavedPaymentMethod]:
        """Получает платежный метод по хешу данных."""
        pass

    @abstractmethod
    async def log_security_event(self, security_log: PaymentMethodSecurityLog) -> PaymentMethodSecurityLog:
        """Записывает событие безопасности."""
        pass


class PaymentMethodRepository(PaymentMethodRepositoryInterface):
    """Реализация repository для работы с платежными методами."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_payment_method(self, payment_method: SavedPaymentMethod) -> SavedPaymentMethod:
        """Создает новый сохраненный платежный метод."""
        try:
            self.session.add(payment_method)
            await self.session.commit()
            await self.session.refresh(payment_method)
            logger.info(f"Created payment method: {payment_method.id} for user {payment_method.user_id}")
            return payment_method
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create payment method: {e}")
            raise
    
    async def get_payment_method_by_id(self, method_id: str) -> Optional[SavedPaymentMethod]:
        """Получает платежный метод по ID."""
        try:
            result = await self.session.execute(
                select(SavedPaymentMethod).where(SavedPaymentMethod.id == method_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get payment method {method_id}: {e}")
            raise
    
    async def get_user_payment_methods(self, user_id: int, 
                                     include_inactive: bool = False) -> List[SavedPaymentMethod]:
        """Получает все платежные методы пользователя."""
        try:
            query = select(SavedPaymentMethod).where(SavedPaymentMethod.user_id == user_id)
            
            if not include_inactive:
                query = query.where(SavedPaymentMethod.is_active == True)
            
            query = query.order_by(
                desc(SavedPaymentMethod.is_default),
                desc(SavedPaymentMethod.last_used_at),
                SavedPaymentMethod.created_at
            )
            
            result = await self.session.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get payment methods for user {user_id}: {e}")
            raise
    
    async def update_payment_method(self, method_id: str, 
                                  update_data: Dict[str, Any]) -> Optional[SavedPaymentMethod]:
        """Обновляет платежный метод."""
        try:
            # Добавляем updated_at
            update_data['updated_at'] = datetime.utcnow()
            
            result = await self.session.execute(
                update(SavedPaymentMethod)
                .where(SavedPaymentMethod.id == method_id)
                .values(**update_data)
                .returning(SavedPaymentMethod)
            )
            
            updated_method = result.scalar_one_or_none()
            if updated_method:
                await self.session.commit()
                logger.info(f"Updated payment method: {method_id}")
            
            return updated_method
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update payment method {method_id}: {e}")
            raise
    
    async def delete_payment_method(self, method_id: str) -> bool:
        """Удаляет платежный метод."""
        try:
            # Сначала удаляем связанные настройки автопродления
            await self.session.execute(
                delete(AutoRenewalSettings)
                .where(AutoRenewalSettings.payment_method_id == method_id)
            )
            
            # Затем удаляем сам платежный метод
            result = await self.session.execute(
                delete(SavedPaymentMethod)
                .where(SavedPaymentMethod.id == method_id)
            )
            
            await self.session.commit()
            deleted = result.rowcount > 0
            
            if deleted:
                logger.info(f"Deleted payment method: {method_id}")
            
            return deleted
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to delete payment method {method_id}: {e}")
            raise
    
    async def set_default_payment_method(self, user_id: int, method_id: str) -> bool:
        """Устанавливает платежный метод по умолчанию."""
        try:
            # Сначала убираем флаг default у всех методов пользователя
            await self.session.execute(
                update(SavedPaymentMethod)
                .where(SavedPaymentMethod.user_id == user_id)
                .values(is_default=False, updated_at=datetime.utcnow())
            )
            
            # Затем устанавливаем новый default
            result = await self.session.execute(
                update(SavedPaymentMethod)
                .where(
                    and_(
                        SavedPaymentMethod.id == method_id,
                        SavedPaymentMethod.user_id == user_id
                    )
                )
                .values(is_default=True, updated_at=datetime.utcnow())
            )
            
            await self.session.commit()
            success = result.rowcount > 0
            
            if success:
                logger.info(f"Set default payment method {method_id} for user {user_id}")
            
            return success
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to set default payment method {method_id} for user {user_id}: {e}")
            raise
    
    async def get_default_payment_method(self, user_id: int) -> Optional[SavedPaymentMethod]:
        """Получает платежный метод по умолчанию для пользователя."""
        try:
            result = await self.session.execute(
                select(SavedPaymentMethod).where(
                    and_(
                        SavedPaymentMethod.user_id == user_id,
                        SavedPaymentMethod.is_default == True,
                        SavedPaymentMethod.is_active == True
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get default payment method for user {user_id}: {e}")
            raise
    
    async def create_auto_renewal_settings(self, settings: AutoRenewalSettings) -> AutoRenewalSettings:
        """Создает настройки автопродления."""
        try:
            self.session.add(settings)
            await self.session.commit()
            await self.session.refresh(settings)
            logger.info(f"Created auto renewal settings: {settings.id} for user {settings.user_id}")
            return settings
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create auto renewal settings: {e}")
            raise
    
    async def get_auto_renewal_settings(self, user_id: int) -> Optional[AutoRenewalSettings]:
        """Получает настройки автопродления пользователя."""
        try:
            result = await self.session.execute(
                select(AutoRenewalSettings).where(AutoRenewalSettings.user_id == user_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get auto renewal settings for user {user_id}: {e}")
            raise
    
    async def update_auto_renewal_settings(self, settings_id: str,
                                         update_data: Dict[str, Any]) -> Optional[AutoRenewalSettings]:
        """Обновляет настройки автопродления."""
        try:
            update_data['updated_at'] = datetime.utcnow()
            
            result = await self.session.execute(
                update(AutoRenewalSettings)
                .where(AutoRenewalSettings.id == settings_id)
                .values(**update_data)
                .returning(AutoRenewalSettings)
            )
            
            updated_settings = result.scalar_one_or_none()
            if updated_settings:
                await self.session.commit()
                logger.info(f"Updated auto renewal settings: {settings_id}")
            
            return updated_settings
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update auto renewal settings {settings_id}: {e}")
            raise
    
    async def log_payment_method_usage(self, log: PaymentMethodUsageLog) -> PaymentMethodUsageLog:
        """Записывает лог использования платежного метода."""
        try:
            self.session.add(log)
            await self.session.commit()
            await self.session.refresh(log)
            return log
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to log payment method usage: {e}")
            raise
    
    async def get_payment_method_usage_history(self, user_id: int,
                                             limit: int = 50) -> List[PaymentMethodUsageLog]:
        """Получает историю использования платежных методов."""
        try:
            result = await self.session.execute(
                select(PaymentMethodUsageLog)
                .where(PaymentMethodUsageLog.user_id == user_id)
                .order_by(desc(PaymentMethodUsageLog.created_at))
                .limit(limit)
            )
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get payment method usage history for user {user_id}: {e}")
            raise

    # Дополнительные методы для автопродления

    async def get_pending_auto_renewals(self, limit: int = 100) -> List[AutoRenewalQueue]:
        """Получает ожидающие автопродления."""
        try:
            current_time = datetime.utcnow()
            result = await self.session.execute(
                select(AutoRenewalQueue)
                .where(
                    and_(
                        AutoRenewalQueue.status == "pending",
                        AutoRenewalQueue.scheduled_at <= current_time
                    )
                )
                .order_by(AutoRenewalQueue.priority.desc(), AutoRenewalQueue.scheduled_at.asc())
                .limit(limit)
            )
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get pending auto renewals: {e}")
            raise

    async def create_auto_renewal_queue_item(self, queue_item: AutoRenewalQueue) -> AutoRenewalQueue:
        """Создает задачу автопродления в очереди."""
        try:
            self.session.add(queue_item)
            await self.session.commit()
            await self.session.refresh(queue_item)
            logger.info(f"Created auto renewal queue item: {queue_item.id}")
            return queue_item
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create auto renewal queue item: {e}")
            raise

    async def update_auto_renewal_queue_status(self, queue_id: str, status: str,
                                             result: Optional[str] = None,
                                             error_message: Optional[str] = None,
                                             new_subscription_id: Optional[str] = None) -> bool:
        """Обновляет статус задачи автопродления."""
        try:
            update_data = {
                'status': status,
                'attempts': AutoRenewalQueue.attempts + 1,
                'updated_at': datetime.utcnow()
            }

            if status in ['completed', 'failed']:
                update_data['processed_at'] = datetime.utcnow()

            if result:
                update_data['result'] = result

            if error_message:
                update_data['error_message'] = error_message

            if new_subscription_id:
                update_data['new_subscription_id'] = new_subscription_id

            update_result = await self.session.execute(
                update(AutoRenewalQueue)
                .where(AutoRenewalQueue.id == queue_id)
                .values(**update_data)
            )

            await self.session.commit()
            return update_result.rowcount > 0
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update auto renewal queue status {queue_id}: {e}")
            raise

    async def log_security_event(self, security_log: PaymentMethodSecurityLog) -> PaymentMethodSecurityLog:
        """Записывает событие безопасности."""
        try:
            self.session.add(security_log)
            await self.session.commit()
            await self.session.refresh(security_log)
            return security_log
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to log security event: {e}")
            raise

    async def get_payment_method_by_hash(self, data_hash: str) -> Optional[SavedPaymentMethod]:
        """Получает платежный метод по хешу данных."""
        try:
            result = await self.session.execute(
                select(SavedPaymentMethod).where(SavedPaymentMethod.data_hash == data_hash)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get payment method by hash: {e}")
            raise

    async def get_expiring_payment_methods(self, days_ahead: int = 30) -> List[SavedPaymentMethod]:
        """Получает платежные методы, которые истекают в ближайшее время."""
        try:
            expiry_threshold = datetime.utcnow() + timedelta(days=days_ahead)
            result = await self.session.execute(
                select(SavedPaymentMethod)
                .where(
                    and_(
                        SavedPaymentMethod.expires_at.isnot(None),
                        SavedPaymentMethod.expires_at <= expiry_threshold,
                        SavedPaymentMethod.is_active == True
                    )
                )
                .order_by(SavedPaymentMethod.expires_at.asc())
            )
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get expiring payment methods: {e}")
            raise
