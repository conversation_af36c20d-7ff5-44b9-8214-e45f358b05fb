"""
Базовые классы и интерфейсы для Repository Pattern.
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Optional, List, Any, Dict
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, insert, update, delete

from db.base import get_session
from utils.logging_config import get_logger

logger = get_logger(__name__)

# Типы для Generic Repository
T = TypeVar('T')
ID = TypeVar('ID')


class BaseRepository(ABC, Generic[T, ID]):
    """
    Базовый абстрактный класс для всех Repository.
    Определяет стандартные операции CRUD.
    """
    
    @abstractmethod
    async def get_by_id(self, id: ID) -> Optional[T]:
        """Получить сущность по ID."""
        pass
    
    @abstractmethod
    async def get_all(self, limit: Optional[int] = None, offset: Optional[int] = None) -> List[T]:
        """Получить все сущности с пагинацией."""
        pass
    
    @abstractmethod
    async def create(self, entity: T) -> T:
        """Создать новую сущность."""
        pass
    
    @abstractmethod
    async def update(self, id: ID, **kwargs) -> Optional[T]:
        """Обновить сущность по ID."""
        pass
    
    @abstractmethod
    async def delete(self, id: ID) -> bool:
        """Удалить сущность по ID."""
        pass
    
    @abstractmethod
    async def exists(self, id: ID) -> bool:
        """Проверить существование сущности по ID."""
        pass


class SQLBaseRepository(BaseRepository[T, ID]):
    """
    Базовая реализация Repository для SQLAlchemy.
    Предоставляет общие методы для работы с БД.
    """
    
    def __init__(self, session: AsyncSession, model_class: type):
        """
        Инициализирует Repository.
        
        Args:
            session: Асинхронная сессия SQLAlchemy
            model_class: Класс модели SQLAlchemy
        """
        self.session = session
        self.model_class = model_class
    
    async def get_by_id(self, id: ID) -> Optional[T]:
        """Получить сущность по ID."""
        try:
            result = await self.session.get(self.model_class, id)
            return result
        except Exception as e:
            logger.error(f"Error getting {self.model_class.__name__} by id {id}: {e}")
            raise
    
    async def get_all(self, limit: Optional[int] = None, offset: Optional[int] = None) -> List[T]:
        """Получить все сущности с пагинацией."""
        try:
            query = select(self.model_class)
            
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all {self.model_class.__name__}: {e}")
            raise
    
    async def create(self, **kwargs) -> T:
        """Создать новую сущность."""
        try:
            entity = self.model_class(**kwargs)
            self.session.add(entity)
            await self.session.flush()  # Получаем ID без коммита
            await self.session.refresh(entity)
            return entity
        except Exception as e:
            logger.error(f"Error creating {self.model_class.__name__}: {e}")
            await self.session.rollback()
            raise
    
    async def update(self, id: ID, **kwargs) -> Optional[T]:
        """Обновить сущность по ID."""
        try:
            entity = await self.get_by_id(id)
            if not entity:
                return None
            
            for key, value in kwargs.items():
                if hasattr(entity, key):
                    setattr(entity, key, value)
            
            await self.session.flush()
            await self.session.refresh(entity)
            return entity
        except Exception as e:
            logger.error(f"Error updating {self.model_class.__name__} {id}: {e}")
            await self.session.rollback()
            raise
    
    async def delete(self, id: ID) -> bool:
        """Удалить сущность по ID."""
        try:
            entity = await self.get_by_id(id)
            if not entity:
                return False
            
            await self.session.delete(entity)
            await self.session.flush()
            return True
        except Exception as e:
            logger.error(f"Error deleting {self.model_class.__name__} {id}: {e}")
            await self.session.rollback()
            raise
    
    async def exists(self, id: ID) -> bool:
        """Проверить существование сущности по ID."""
        try:
            entity = await self.get_by_id(id)
            return entity is not None
        except Exception as e:
            logger.error(f"Error checking existence of {self.model_class.__name__} {id}: {e}")
            raise
    
    async def find_by(self, **kwargs) -> List[T]:
        """Найти сущности по условиям."""
        try:
            query = select(self.model_class)
            
            for key, value in kwargs.items():
                if hasattr(self.model_class, key):
                    query = query.where(getattr(self.model_class, key) == value)
            
            result = await self.session.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error finding {self.model_class.__name__} by {kwargs}: {e}")
            raise
    
    async def find_one_by(self, **kwargs) -> Optional[T]:
        """Найти одну сущность по условиям."""
        try:
            results = await self.find_by(**kwargs)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error finding one {self.model_class.__name__} by {kwargs}: {e}")
            raise
    
    async def count(self, **kwargs) -> int:
        """Подсчитать количество сущностей по условиям."""
        try:
            from sqlalchemy import func
            
            query = select(func.count(self.model_class.id))
            
            for key, value in kwargs.items():
                if hasattr(self.model_class, key):
                    query = query.where(getattr(self.model_class, key) == value)
            
            result = await self.session.execute(query)
            return result.scalar()
        except Exception as e:
            logger.error(f"Error counting {self.model_class.__name__} by {kwargs}: {e}")
            raise


class UnitOfWork:
    """
    Unit of Work pattern для управления транзакциями.
    Обеспечивает атомарность операций с несколькими Repository.
    """
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        self._repositories: Dict[str, Any] = {}
    
    async def __aenter__(self):
        """Начало транзакции."""
        async with get_session() as session:
            self.session = session
            return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Завершение транзакции."""
        if exc_type is not None:
            # Если произошла ошибка, откатываем транзакцию
            await self.session.rollback()
            logger.error(f"Transaction rolled back due to error: {exc_val}")
        else:
            # Если все прошло успешно, коммитим
            await self.session.commit()
            logger.debug("Transaction committed successfully")
        
        await self.session.close()
        self.session = None
        self._repositories.clear()
    
    def get_repository(self, repository_class: type, model_class: type = None):
        """
        Получить Repository в рамках текущей транзакции.

        Args:
            repository_class: Класс Repository
            model_class: Класс модели (опционально)

        Returns:
            Экземпляр Repository
        """
        if not self.session:
            raise RuntimeError("UnitOfWork session is not initialized")

        model_name = model_class.__name__ if model_class else "None"
        repo_key = f"{repository_class.__name__}_{model_name}"

        if repo_key not in self._repositories:
            # Для некоторых Repository не нужен model_class
            if model_class:
                self._repositories[repo_key] = repository_class(self.session, model_class)
            else:
                self._repositories[repo_key] = repository_class(self.session)

        return self._repositories[repo_key]
    
    async def commit(self):
        """Принудительный коммит транзакции."""
        if self.session:
            await self.session.commit()
            logger.debug("Manual commit executed")
    
    async def rollback(self):
        """Принудительный откат транзакции."""
        if self.session:
            await self.session.rollback()
            logger.debug("Manual rollback executed")


# Фабрика для создания Repository
class RepositoryFactory:
    """Фабрика для создания Repository с автоматическим управлением сессиями."""
    
    @staticmethod
    @asynccontextmanager
    async def create_repository(repository_class: type, model_class: type = None):
        """
        Создает Repository с автоматическим управлением сессией.

        Args:
            repository_class: Класс Repository
            model_class: Класс модели (опционально)

        Yields:
            Экземпляр Repository
        """
        async with get_session() as session:
            if model_class:
                repository = repository_class(session, model_class)
            else:
                repository = repository_class(session)
            try:
                yield repository
                await session.commit()
            except Exception:
                await session.rollback()
                raise
