"""
Адаптеры для обеспечения обратной совместимости с существующими handlers.
Постепенный переход от db.methods к Repository Pattern.
"""

from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

from .user_repository import SQLUserRepository
from .payment_repository import SQLPaymentRepository
from .subscription_repository import MarzbanSubscriptionService
from .base import UnitOfWork, RepositoryFactory
from db.models import VPNUsers, YPayments, CPayments
from db.base import get_session
from utils.logging_config import get_logger

logger = get_logger(__name__)


class RepositoryAdapter:
    """
    Адаптер для обеспечения обратной совместимости.
    Предоставляет те же функции, что и db.methods, но использует Repository Pattern.
    """
    
    @staticmethod
    @asynccontextmanager
    async def get_user_repository():
        """Получить UserRepository с автоматическим управлением сессией."""
        async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as repo:
            yield repo
    
    @staticmethod
    @asynccontextmanager
    async def get_payment_repository():
        """Получить PaymentRepository с автоматическим управлением сессией."""
        async with get_session() as session:
            repo = SQLPaymentRepository(session)
            try:
                yield repo
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    @staticmethod
    @asynccontextmanager
    async def get_subscription_service():
        """Получить SubscriptionService с автоматическим управлением сессией."""
        async with RepositoryFactory.create_repository(SQLUserRepository, VPNUsers) as user_repo:
            service = MarzbanSubscriptionService(user_repo)
            yield service


# Функции-адаптеры для обратной совместимости с db.methods
async def create_vpn_profile(tg_id: int) -> None:
    """
    Создать VPN профиль пользователя.
    Адаптер для db.methods.create_vpn_profile.
    """
    try:
        async with RepositoryAdapter.get_user_repository() as user_repo:
            await user_repo.create_user(tg_id)
            logger.debug(f"VPN profile created for user {tg_id}")
    except Exception as e:
        logger.error(f"Error creating VPN profile for user {tg_id}: {e}")
        raise


async def get_marzban_profile_db(tg_id: int) -> Optional[VPNUsers]:
    """
    Получить профиль пользователя из БД.
    Адаптер для db.methods.get_marzban_profile_db.
    """
    try:
        async with RepositoryAdapter.get_user_repository() as user_repo:
            user = await user_repo.get_by_telegram_id(tg_id)
            logger.debug(f"Retrieved profile for user {tg_id}: {'found' if user else 'not found'}")
            return user
    except Exception as e:
        logger.error(f"Error getting profile for user {tg_id}: {e}")
        raise


async def get_marzban_profile_by_vpn_id(vpn_id: str) -> Optional[VPNUsers]:
    """
    Получить профиль пользователя по VPN ID.
    Адаптер для db.methods.get_marzban_profile_by_vpn_id.
    """
    try:
        async with RepositoryAdapter.get_user_repository() as user_repo:
            user = await user_repo.get_by_vpn_id(vpn_id)
            logger.debug(f"Retrieved profile by VPN ID {vpn_id}: {'found' if user else 'not found'}")
            return user
    except Exception as e:
        logger.error(f"Error getting profile by VPN ID {vpn_id}: {e}")
        raise


async def had_test_sub(tg_id: int) -> bool:
    """
    Проверить, использовал ли пользователь тестовую подписку.
    Адаптер для db.methods.had_test_sub.
    """
    try:
        async with RepositoryAdapter.get_user_repository() as user_repo:
            # Получаем пользователя и проверяем по expire_date
            user = await user_repo.get_by_telegram_id(tg_id)
            if not user:
                logger.debug(f"User {tg_id} not found")
                return False

            # Считаем, что тестовая подписка использована, если есть expire_date
            result = user.expire_date is not None
            logger.debug(f"Test subscription check for user {tg_id}: {result}")
            return result
    except Exception as e:
        logger.error(f"Error checking test subscription for user {tg_id}: {e}")
        raise


async def update_test_subscription_state(tg_id: int) -> None:
    """
    Обновить статус тестовой подписки.
    Адаптер для db.methods.update_test_subscription_state.
    """
    try:
        async with RepositoryAdapter.get_user_repository() as user_repo:
            await user_repo.update_test_subscription_state(tg_id, True)
            logger.debug(f"Updated test subscription state for user {tg_id}")
    except Exception as e:
        logger.error(f"Error updating test subscription state for user {tg_id}: {e}")
        raise


async def add_yookassa_payment(tg_id: int, callback: str, chat_id: int, lang_code: str, payment_id: str) -> Dict[str, Any]:
    """
    Добавить платеж YooKassa.
    Адаптер для db.methods.add_yookassa_payment.
    """
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            payment = await payment_repo.create_yookassa_payment(
                tg_id, payment_id, callback, chat_id, lang_code
            )
            logger.debug(f"Added YooKassa payment {payment_id} for user {tg_id}")
            return {"id": payment.id, "payment_id": payment.payment_id}
    except Exception as e:
        logger.error(f"Error adding YooKassa payment {payment_id}: {e}")
        raise


async def add_cryptomus_payment(tg_id: int, callback: str, chat_id: int, lang_code: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Добавить платеж Cryptomus.
    Адаптер для db.methods.add_cryptomus_payment.
    """
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            payment = await payment_repo.create_cryptomus_payment(
                tg_id, data['order_id'], callback, chat_id, lang_code
            )
            logger.debug(f"Added Cryptomus payment {data['order_id']} for user {tg_id}")
            return {"id": payment.id, "order_id": payment.order_id}
    except Exception as e:
        logger.error(f"Error adding Cryptomus payment {data.get('order_id')}: {e}")
        raise


async def get_yookassa_payment(payment_id: str) -> Optional[YPayments]:
    """
    Получить платеж YooKassa.
    Адаптер для db.methods.get_yookassa_payment.
    """
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            payment = await payment_repo.get_yookassa_payment(payment_id)
            logger.debug(f"Retrieved YooKassa payment {payment_id}: {'found' if payment else 'not found'}")
            return payment
    except Exception as e:
        logger.error(f"Error getting YooKassa payment {payment_id}: {e}")
        raise


async def get_cryptomus_payment(order_id: str) -> Optional[CPayments]:
    """
    Получить платеж Cryptomus.
    Адаптер для db.methods.get_cryptomus_payment.
    """
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            payment = await payment_repo.get_cryptomus_payment(order_id)
            logger.debug(f"Retrieved Cryptomus payment {order_id}: {'found' if payment else 'not found'}")
            return payment
    except Exception as e:
        logger.error(f"Error getting Cryptomus payment {order_id}: {e}")
        raise


async def delete_payment(payment_id: str, payment_type: str = "yookassa") -> bool:
    """
    Удалить платеж.
    Адаптер для db.methods.delete_payment.
    """
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            result = await payment_repo.delete_payment(payment_id, payment_type)
            logger.debug(f"Deleted {payment_type} payment {payment_id}: {result}")
            return result
    except Exception as e:
        logger.error(f"Error deleting {payment_type} payment {payment_id}: {e}")
        raise


# Новые функции для расширенной функциональности
async def get_user_stats() -> Dict[str, Any]:
    """Получить статистику пользователей."""
    try:
        async with RepositoryAdapter.get_user_repository() as user_repo:
            stats = await user_repo.get_user_stats()
            logger.debug(f"Retrieved user stats: {stats}")
            return stats
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        raise


async def get_payment_stats() -> Dict[str, Any]:
    """Получить статистику платежей."""
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            stats = await payment_repo.get_payment_stats()
            logger.debug(f"Retrieved payment stats: {stats}")
            return stats
    except Exception as e:
        logger.error(f"Error getting payment stats: {e}")
        raise


async def get_user_payments(tg_id: int, payment_type: Optional[str] = None) -> list:
    """Получить все платежи пользователя."""
    try:
        async with RepositoryAdapter.get_payment_repository() as payment_repo:
            payments = await payment_repo.get_user_payments(tg_id, payment_type)
            logger.debug(f"Retrieved {len(payments)} payments for user {tg_id}")
            return payments
    except Exception as e:
        logger.error(f"Error getting payments for user {tg_id}: {e}")
        raise


# Функции для работы с подписками через Marzban API
async def get_user_subscription_info(tg_id: int) -> Optional[Dict[str, Any]]:
    """Получить информацию о подписке пользователя."""
    try:
        async with RepositoryAdapter.get_subscription_service() as subscription_service:
            info = await subscription_service.get_user_subscription_info(tg_id)
            logger.debug(f"Retrieved subscription info for user {tg_id}: {'found' if info else 'not found'}")
            return info
    except Exception as e:
        logger.error(f"Error getting subscription info for user {tg_id}: {e}")
        raise


async def create_test_subscription(tg_id: int) -> bool:
    """Создать тестовую подписку."""
    try:
        async with RepositoryAdapter.get_subscription_service() as subscription_service:
            result = await subscription_service.create_test_subscription(tg_id)
            logger.debug(f"Created test subscription for user {tg_id}: {result}")
            return result
    except Exception as e:
        logger.error(f"Error creating test subscription for user {tg_id}: {e}")
        raise


async def create_paid_subscription(tg_id: int, product_data: Dict[str, Any]) -> bool:
    """Создать платную подписку."""
    try:
        async with RepositoryAdapter.get_subscription_service() as subscription_service:
            result = await subscription_service.create_paid_subscription(tg_id, product_data)
            logger.debug(f"Created paid subscription for user {tg_id}: {result}")
            return result
    except Exception as e:
        logger.error(f"Error creating paid subscription for user {tg_id}: {e}")
        raise
