"""
Repository Pattern для VPN бота.
Абстракции для работы с данными, отделяющие бизнес-логику от деталей БД.
"""

from .base import BaseRepository, UnitOfWork
from .user_repository import UserRepository, SQLUserRepository
from .payment_repository import PaymentRepository, SQLPaymentRepository
from .subscription_repository import SubscriptionRepository, SQLSubscriptionRepository

__all__ = [
    'BaseRepository',
    'UnitOfWork',
    'UserRepository',
    'SQLUserRepository',
    'PaymentRepository',
    'SQLPaymentRepository',
    'SubscriptionRepository',
    'SQLSubscriptionRepository'
]
