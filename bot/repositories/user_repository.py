"""
Repository для работы с пользователями VPN.
"""

import hashlib
from abc import ABC, abstractmethod
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from db.models import VPNUsers
from db.cache import db_cache
from db.performance import performance_analyzer
from .base import SQLBaseRepository
from utils.logging_config import get_logger

logger = get_logger(__name__)


class UserRepository(ABC):
    """Абстрактный интерфейс для работы с пользователями."""
    
    @abstractmethod
    async def get_by_telegram_id(self, tg_id: int) -> Optional[VPNUsers]:
        """Получить пользователя по Telegram ID."""
        pass
    
    @abstractmethod
    async def get_by_vpn_id(self, vpn_id: str) -> Optional[VPNUsers]:
        """Получить пользователя по VPN ID."""
        pass
    
    @abstractmethod
    async def create_user(self, tg_id: int, vpn_id: Optional[str] = None) -> VPNUsers:
        """Создать нового пользователя."""
        pass
    
    @abstractmethod
    async def update_test_subscription_state(self, tg_id: int, test_used: bool = True) -> bool:
        """Обновить статус тестовой подписки."""
        pass
    
    @abstractmethod
    async def has_test_subscription(self, tg_id: int) -> bool:
        """Проверить, использовал ли пользователь тестовую подписку."""
        pass
    
    @abstractmethod
    async def user_exists(self, tg_id: int) -> bool:
        """Проверить существование пользователя."""
        pass


class SQLUserRepository(SQLBaseRepository[VPNUsers, int], UserRepository):
    """SQLAlchemy реализация Repository для пользователей."""

    def __init__(self, session: AsyncSession, model_class: type = VPNUsers):
        super().__init__(session, model_class)
    
    async def get_by_telegram_id(self, tg_id: int) -> Optional[VPNUsers]:
        """
        Получить пользователя по Telegram ID с кэшированием.

        Args:
            tg_id: Telegram ID пользователя

        Returns:
            Объект пользователя или None
        """
        try:
            # Проверяем кэш
            cached_user = await db_cache.get_user(tg_id)
            if cached_user is not None:
                logger.debug(f"Retrieved user {tg_id} from cache")
                return cached_user

            # Измеряем производительность запроса
            async with performance_analyzer.measure_query(f"get_user_by_telegram_id:{tg_id}"):
                query = select(VPNUsers).where(VPNUsers.tg_id == tg_id)
                result = await self.session.execute(query)
                user = result.scalar_one_or_none()

            # Кэшируем результат
            if user:
                await db_cache.set_user(tg_id, user, ttl=300)  # 5 минут
                logger.debug(f"Retrieved and cached user by telegram_id {tg_id}")
            else:
                logger.debug(f"User not found by telegram_id {tg_id}")

            return user

        except Exception as e:
            logger.error(f"Error getting user by telegram_id {tg_id}: {e}")
            raise
    
    async def get_by_vpn_id(self, vpn_id: str) -> Optional[VPNUsers]:
        """
        Получить пользователя по VPN ID.
        
        Args:
            vpn_id: VPN ID пользователя
            
        Returns:
            Объект пользователя или None
        """
        try:
            query = select(VPNUsers).where(VPNUsers.vpn_id == vpn_id)
            result = await self.session.execute(query)
            user = result.scalar_one_or_none()
            
            logger.debug(f"Retrieved user by vpn_id {vpn_id}: {'found' if user else 'not found'}")
            return user
            
        except Exception as e:
            logger.error(f"Error getting user by vpn_id {vpn_id}: {e}")
            raise
    
    async def create_user(self, tg_id: int, vpn_id: Optional[str] = None) -> VPNUsers:
        """
        Создать нового пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            vpn_id: VPN ID (если не указан, генерируется автоматически)
            
        Returns:
            Созданный объект пользователя
        """
        try:
            # Проверяем, не существует ли уже пользователь
            existing_user = await self.get_by_telegram_id(tg_id)
            if existing_user:
                logger.info(f"User with telegram_id {tg_id} already exists")
                return existing_user
            
            # Генерируем VPN ID если не указан
            if not vpn_id:
                vpn_id = hashlib.md5(str(tg_id).encode()).hexdigest()
            
            # Создаем пользователя с измерением производительности
            async with performance_analyzer.measure_query(f"create_user:{tg_id}"):
                user = await self.create(
                    tg_id=tg_id,
                    vpn_id=vpn_id,
                    test=False
                )

            # Кэшируем нового пользователя
            await db_cache.set_user(tg_id, user, ttl=300)
            await db_cache.set_user_by_vpn_id(vpn_id, user, ttl=300)

            logger.info(f"Created new user: telegram_id={tg_id}, vpn_id={vpn_id}")
            return user
            
        except Exception as e:
            logger.error(f"Error creating user with telegram_id {tg_id}: {e}")
            raise
    
    async def update_test_subscription_state(self, tg_id: int, test_used: bool = True) -> bool:
        """
        Обновить статус тестовой подписки.
        
        Args:
            tg_id: Telegram ID пользователя
            test_used: Статус использования тестовой подписки
            
        Returns:
            True если обновление прошло успешно
        """
        try:
            query = (
                update(VPNUsers)
                .where(VPNUsers.tg_id == tg_id)
                .values(test=test_used)
            )
            
            result = await self.session.execute(query)
            
            if result.rowcount > 0:
                logger.info(f"Updated test subscription state for user {tg_id}: test={test_used}")
                return True
            else:
                logger.warning(f"No user found with telegram_id {tg_id} for test subscription update")
                return False
                
        except Exception as e:
            logger.error(f"Error updating test subscription state for user {tg_id}: {e}")
            raise
    
    async def has_test_subscription(self, tg_id: int) -> bool:
        """
        Проверить, использовал ли пользователь тестовую подписку.
        
        Args:
            tg_id: Telegram ID пользователя
            
        Returns:
            True если тестовая подписка была использована
        """
        try:
            user = await self.get_by_telegram_id(tg_id)
            
            if not user:
                logger.warning(f"User with telegram_id {tg_id} not found for test subscription check")
                return False
            
            result = bool(user.test)
            logger.debug(f"Test subscription check for user {tg_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error checking test subscription for user {tg_id}: {e}")
            raise
    
    async def user_exists(self, tg_id: int) -> bool:
        """
        Проверить существование пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            
        Returns:
            True если пользователь существует
        """
        try:
            user = await self.get_by_telegram_id(tg_id)
            result = user is not None
            logger.debug(f"User existence check for telegram_id {tg_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error checking user existence for telegram_id {tg_id}: {e}")
            raise
    
    async def get_users_by_test_status(self, test_used: bool) -> List[VPNUsers]:
        """
        Получить пользователей по статусу тестовой подписки.
        
        Args:
            test_used: Статус использования тестовой подписки
            
        Returns:
            Список пользователей
        """
        try:
            query = select(VPNUsers).where(VPNUsers.test == test_used)
            result = await self.session.execute(query)
            users = result.scalars().all()
            
            logger.debug(f"Retrieved {len(users)} users with test status {test_used}")
            return users
            
        except Exception as e:
            logger.error(f"Error getting users by test status {test_used}: {e}")
            raise
    
    async def get_user_stats(self) -> dict:
        """
        Получить статистику пользователей.
        
        Returns:
            Словарь со статистикой
        """
        try:
            from sqlalchemy import func
            
            # Общее количество пользователей
            total_query = select(func.count(VPNUsers.id))
            total_result = await self.session.execute(total_query)
            total_users = total_result.scalar()
            
            # Пользователи с тестовой подпиской
            test_query = select(func.count(VPNUsers.id)).where(VPNUsers.test == True)
            test_result = await self.session.execute(test_query)
            test_users = test_result.scalar()
            
            stats = {
                'total_users': total_users,
                'test_users': test_users,
                'non_test_users': total_users - test_users
            }
            
            logger.debug(f"User stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            raise
