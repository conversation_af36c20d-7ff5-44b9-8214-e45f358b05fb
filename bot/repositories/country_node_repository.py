"""
Repository для работы со странами, нодами и inbounds.
Реализует Repository Pattern для моделей Country, MarzbanNode, MarzbanInbound.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from db.models_countries_nodes import (
    Country, MarzbanNode, MarzbanInbound, 
    UserNodePreference, NodeStatistics,
    NodeStatus, InboundProtocol
)
from utils.logging_config import get_logger

logger = get_logger(__name__)


class CountryRepository:
    """Repository для работы со странами."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_id(self, id: str) -> Optional[Country]:
        """Получить страну по ID."""
        return await self.get_by_iso_code(id)
    
    async def get_active_countries(self) -> List[Country]:
        """Получить все активные страны, отсортированные по приоритету."""
        query = (
            select(Country)
            .where(Country.is_active == True)
            .order_by(Country.priority.asc(), Country.name.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_by_iso_code(self, iso_code: str) -> Optional[Country]:
        """Получить страну по ISO коду."""
        query = select(Country).where(Country.id == iso_code.upper())
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def get_countries_with_active_nodes(self) -> List[Country]:
        """Получить страны, у которых есть активные ноды."""
        query = (
            select(Country)
            .join(MarzbanNode)
            .where(
                and_(
                    Country.is_active == True,
                    MarzbanNode.is_active == True,
                    MarzbanNode.status == NodeStatus.CONNECTED
                )
            )
            .options(selectinload(Country.nodes))
            .distinct()
            .order_by(Country.priority.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())


class MarzbanNodeRepository:
    """Repository для работы с нодами Marzban."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_id(self, id: str) -> Optional[MarzbanNode]:
        """Получить ноду по ID."""
        query = select(MarzbanNode).where(MarzbanNode.id == id)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def get_inbounds_by_node(self, node_id: str) -> List['MarzbanInbound']:
        """Получить inbounds для конкретной ноды."""
        from db.models_countries_nodes import MarzbanInbound
        query = (
            select(MarzbanInbound)
            .where(
                and_(
                    MarzbanInbound.primary_node_id == node_id,
                    MarzbanInbound.is_active == True
                )
            )
            .order_by(MarzbanInbound.protocol.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())

    async def get_all(self, limit: int = 100, offset: int = 0) -> List[MarzbanNode]:
        """Получить все ноды."""
        query = (
            select(MarzbanNode)
            .order_by(MarzbanNode.country_id.asc(), MarzbanNode.name.asc())
            .limit(limit)
            .offset(offset)
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())

    async def get_active_nodes(self) -> List[MarzbanNode]:
        """Получить все активные ноды."""
        query = (
            select(MarzbanNode)
            .where(
                and_(
                    MarzbanNode.is_active == True,
                    MarzbanNode.status == NodeStatus.CONNECTED
                )
            )
            .options(selectinload(MarzbanNode.inbounds))
            .order_by(MarzbanNode.current_users.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_nodes_by_country(self, country_id: str, include_inactive: bool = False) -> List[MarzbanNode]:
        """Получить ноды по стране."""
        conditions = [MarzbanNode.country_id == country_id.upper()]
        
        if not include_inactive:
            conditions.extend([
                MarzbanNode.is_active == True,
                MarzbanNode.status == NodeStatus.CONNECTED
            ])
        
        query = (
            select(MarzbanNode)
            .where(and_(*conditions))
            .options(selectinload(MarzbanNode.inbounds))
            .order_by(
                MarzbanNode.current_users.asc(),
                MarzbanNode.cpu_usage.asc()
            )
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_optimal_nodes(self,
                               country_ids: Optional[List[str]] = None,
                               protocols: Optional[List[str]] = None,
                               max_load_percent: float = 80.0,
                               limit: int = 10) -> List[MarzbanNode]:
        """
        Получить оптимальные ноды по критериям.
        
        Args:
            country_ids: Список ISO кодов стран
            protocols: Список протоколов
            max_load_percent: Максимальная загрузка ноды в процентах
            limit: Максимальное количество нод
        """
        conditions = [
            MarzbanNode.is_active == True,
            MarzbanNode.status == NodeStatus.CONNECTED,
            MarzbanNode.current_users < (MarzbanNode.max_users * max_load_percent / 100)
        ]
        
        if country_ids:
            conditions.append(MarzbanNode.country_id.in_([c.upper() for c in country_ids]))
        
        query = (
            select(MarzbanNode)
            .where(and_(*conditions))
            .options(selectinload(MarzbanNode.inbounds))
        )
        
        # Если указаны протоколы, фильтруем по inbounds
        if protocols:
            query = query.join(MarzbanInbound).where(
                and_(
                    MarzbanInbound.protocol.in_(protocols),
                    MarzbanInbound.is_active == True
                )
            ).distinct()
        
        # Сортируем по загрузке и производительности
        query = query.order_by(
            (MarzbanNode.current_users / MarzbanNode.max_users).asc(),
            MarzbanNode.cpu_usage.asc(),
            MarzbanNode.memory_usage.asc()
        ).limit(limit)
        
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def update_node_stats(self, node_id: str, stats: Dict[str, Any]) -> bool:
        """Обновить статистику ноды."""
        try:
            node = await self.get_by_id(node_id)
            if not node:
                return False
            
            # Обновляем статистику
            for key, value in stats.items():
                if hasattr(node, key):
                    setattr(node, key, value)
            
            node.last_check = datetime.utcnow()
            await self.session.commit()
            
            logger.info(f"Updated stats for node {node_id}: {stats}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating node stats for {node_id}: {e}")
            await self.session.rollback()
            return False


class MarzbanInboundRepository:
    """Repository для работы с inbounds."""

    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def get_active_inbounds(self) -> List[MarzbanInbound]:
        """Получить все активные inbounds."""
        query = (
            select(MarzbanInbound)
            .where(MarzbanInbound.is_active == True)
            .options(selectinload(MarzbanInbound.primary_node))
            .order_by(MarzbanInbound.protocol.asc(), MarzbanInbound.tag.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_inbounds_by_protocol(self, protocol: InboundProtocol) -> List[MarzbanInbound]:
        """Получить inbounds по протоколу."""
        query = (
            select(MarzbanInbound)
            .where(
                and_(
                    MarzbanInbound.protocol == protocol,
                    MarzbanInbound.is_active == True
                )
            )
            .options(selectinload(MarzbanInbound.primary_node))
            .order_by(MarzbanInbound.tag.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_inbounds_by_node(self, node_id: str) -> List[MarzbanInbound]:
        """Получить inbounds для конкретной ноды."""
        query = (
            select(MarzbanInbound)
            .where(
                and_(
                    MarzbanInbound.primary_node_id == node_id,
                    MarzbanInbound.is_active == True
                )
            )
            .order_by(MarzbanInbound.protocol.asc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())


class UserNodePreferenceRepository:
    """Repository для работы с предпочтениями пользователей."""

    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def get_by_telegram_id(self, tg_id: int) -> Optional[UserNodePreference]:
        """Получить предпочтения пользователя по Telegram ID."""
        query = select(UserNodePreference).where(UserNodePreference.tg_id == tg_id)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def create_or_update_preferences(self, tg_id: int, preferences: Dict[str, Any]) -> UserNodePreference:
        """Создать или обновить предпочтения пользователя."""
        existing = await self.get_by_telegram_id(tg_id)
        
        if existing:
            # Обновляем существующие предпочтения
            for key, value in preferences.items():
                if hasattr(existing, key):
                    setattr(existing, key, value)
            existing.updated_at = datetime.utcnow()
            await self.session.commit()
            return existing
        else:
            # Создаем новые предпочтения
            new_preference = UserNodePreference(
                tg_id=tg_id,
                **preferences
            )
            self.session.add(new_preference)
            await self.session.commit()
            return new_preference
    
    async def update_last_selection(self, tg_id: int, country_id: Optional[str] = None, node_id: Optional[str] = None) -> bool:
        """Обновить последний выбор пользователя."""
        try:
            preference = await self.get_by_telegram_id(tg_id)
            if not preference:
                # Создаем базовые предпочтения если их нет
                preference = UserNodePreference(tg_id=tg_id)
                self.session.add(preference)
            
            if country_id:
                preference.last_selected_country = country_id.upper()
            if node_id:
                preference.last_selected_node = node_id
            
            preference.updated_at = datetime.utcnow()
            await self.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error updating last selection for user {tg_id}: {e}")
            await self.session.rollback()
            return False


class NodeStatisticsRepository:
    """Repository для работы со статистикой нод."""

    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def add_statistics(self, node_id: str, stats: Dict[str, Any]) -> NodeStatistics:
        """Добавить запись статистики для ноды."""
        stat_record = NodeStatistics(
            node_id=node_id,
            **stats
        )
        self.session.add(stat_record)
        await self.session.commit()
        return stat_record
    
    async def get_recent_stats(self, node_id: str, hours: int = 24) -> List[NodeStatistics]:
        """Получить недавнюю статистику ноды."""
        since = datetime.utcnow() - timedelta(hours=hours)
        
        query = (
            select(NodeStatistics)
            .where(
                and_(
                    NodeStatistics.node_id == node_id,
                    NodeStatistics.timestamp >= since
                )
            )
            .order_by(NodeStatistics.timestamp.desc())
        )
        result = await self.session.execute(query)
        return list(result.scalars().all())
    
    async def get_average_performance(self, node_id: str, hours: int = 24) -> Dict[str, float]:
        """Получить среднюю производительность ноды за период."""
        since = datetime.utcnow() - timedelta(hours=hours)
        
        query = (
            select(
                func.avg(NodeStatistics.cpu_usage).label('avg_cpu'),
                func.avg(NodeStatistics.memory_usage).label('avg_memory'),
                func.avg(NodeStatistics.response_time_ms).label('avg_response_time'),
                func.avg(NodeStatistics.users_count).label('avg_users')
            )
            .where(
                and_(
                    NodeStatistics.node_id == node_id,
                    NodeStatistics.timestamp >= since,
                    NodeStatistics.is_online == True
                )
            )
        )
        
        result = await self.session.execute(query)
        row = result.first()
        
        if row:
            return {
                'avg_cpu_usage': float(row.avg_cpu or 0),
                'avg_memory_usage': float(row.avg_memory or 0),
                'avg_response_time_ms': float(row.avg_response_time or 0),
                'avg_users_count': float(row.avg_users or 0)
            }
        
        return {
            'avg_cpu_usage': 0.0,
            'avg_memory_usage': 0.0,
            'avg_response_time_ms': 0.0,
            'avg_users_count': 0.0
        }
