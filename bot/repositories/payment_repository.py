"""
Repository для работы с платежами.
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete

from db.models import YPayments, CPayments
from .base import SQLBaseRepository
from utils.logging_config import get_logger

logger = get_logger(__name__)


class PaymentRepository(ABC):
    """Абстрактный интерфейс для работы с платежами."""
    
    @abstractmethod
    async def create_yookassa_payment(self, tg_id: int, payment_id: str, callback: str, 
                                    chat_id: int, lang_code: str) -> YPayments:
        """Создать платеж YooKassa."""
        pass
    
    @abstractmethod
    async def create_cryptomus_payment(self, tg_id: int, order_id: str, callback: str,
                                     chat_id: int, lang_code: str) -> CPayments:
        """Создать платеж Cryptomus."""
        pass
    
    @abstractmethod
    async def get_yookassa_payment(self, payment_id: str) -> Optional[YPayments]:
        """Получить платеж YooKassa по ID."""
        pass
    
    @abstractmethod
    async def get_cryptomus_payment(self, order_id: str) -> Optional[CPayments]:
        """Получить платеж Cryptomus по order_id."""
        pass
    
    @abstractmethod
    async def delete_payment(self, payment_id: str, payment_type: str) -> bool:
        """Удалить платеж."""
        pass
    
    @abstractmethod
    async def get_user_payments(self, tg_id: int, payment_type: Optional[str] = None) -> List[Union[YPayments, CPayments]]:
        """Получить все платежи пользователя."""
        pass


class SQLPaymentRepository(PaymentRepository):
    """SQLAlchemy реализация Repository для платежей."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.yookassa_repo = SQLBaseRepository(session, YPayments)
        self.cryptomus_repo = SQLBaseRepository(session, CPayments)
    
    async def create_yookassa_payment(self, tg_id: int, payment_id: str, callback: str,
                                    chat_id: int, lang_code: str) -> YPayments:
        """
        Создать платеж YooKassa.
        
        Args:
            tg_id: Telegram ID пользователя
            payment_id: ID платежа в YooKassa
            callback: Callback данные
            chat_id: ID чата
            lang_code: Код языка
            
        Returns:
            Созданный объект платежа
        """
        try:
            payment = await self.yookassa_repo.create(
                tg_id=tg_id,
                payment_id=payment_id,
                callback=callback,
                chat_id=chat_id,
                lang=lang_code
            )
            
            logger.info(f"Created YooKassa payment: payment_id={payment_id}, user={tg_id}")
            return payment
            
        except Exception as e:
            logger.error(f"Error creating YooKassa payment {payment_id}: {e}")
            raise
    
    async def create_cryptomus_payment(self, tg_id: int, order_id: str, callback: str,
                                     chat_id: int, lang_code: str) -> CPayments:
        """
        Создать платеж Cryptomus.
        
        Args:
            tg_id: Telegram ID пользователя
            order_id: ID заказа в Cryptomus
            callback: Callback данные
            chat_id: ID чата
            lang_code: Код языка
            
        Returns:
            Созданный объект платежа
        """
        try:
            payment = await self.cryptomus_repo.create(
                tg_id=tg_id,
                payment_uuid=order_id,
                order_id=order_id,
                callback=callback,
                chat_id=chat_id,
                lang=lang_code
            )
            
            logger.info(f"Created Cryptomus payment: order_id={order_id}, user={tg_id}")
            return payment
            
        except Exception as e:
            logger.error(f"Error creating Cryptomus payment {order_id}: {e}")
            raise
    
    async def get_yookassa_payment(self, payment_id: str) -> Optional[YPayments]:
        """
        Получить платеж YooKassa по ID.
        
        Args:
            payment_id: ID платежа
            
        Returns:
            Объект платежа или None
        """
        try:
            query = select(YPayments).where(YPayments.payment_id == payment_id)
            result = await self.session.execute(query)
            payment = result.scalar_one_or_none()
            
            logger.debug(f"Retrieved YooKassa payment {payment_id}: {'found' if payment else 'not found'}")
            return payment
            
        except Exception as e:
            logger.error(f"Error getting YooKassa payment {payment_id}: {e}")
            raise
    
    async def get_cryptomus_payment(self, order_id: str) -> Optional[CPayments]:
        """
        Получить платеж Cryptomus по order_id.
        
        Args:
            order_id: ID заказа
            
        Returns:
            Объект платежа или None
        """
        try:
            query = select(CPayments).where(CPayments.order_id == order_id)
            result = await self.session.execute(query)
            payment = result.scalar_one_or_none()
            
            logger.debug(f"Retrieved Cryptomus payment {order_id}: {'found' if payment else 'not found'}")
            return payment
            
        except Exception as e:
            logger.error(f"Error getting Cryptomus payment {order_id}: {e}")
            raise
    
    async def delete_payment(self, payment_id: str, payment_type: str) -> bool:
        """
        Удалить платеж.
        
        Args:
            payment_id: ID платежа
            payment_type: Тип платежа ('yookassa' или 'cryptomus')
            
        Returns:
            True если платеж был удален
        """
        try:
            if payment_type.lower() == 'yookassa':
                query = delete(YPayments).where(YPayments.payment_id == payment_id)
            elif payment_type.lower() == 'cryptomus':
                query = delete(CPayments).where(CPayments.order_id == payment_id)
            else:
                logger.error(f"Unknown payment type: {payment_type}")
                return False
            
            result = await self.session.execute(query)
            
            if result.rowcount > 0:
                logger.info(f"Deleted {payment_type} payment: {payment_id}")
                return True
            else:
                logger.warning(f"Payment not found for deletion: {payment_type} {payment_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting {payment_type} payment {payment_id}: {e}")
            raise
    
    async def get_user_payments(self, tg_id: int, payment_type: Optional[str] = None) -> List[Union[YPayments, CPayments]]:
        """
        Получить все платежи пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            payment_type: Тип платежей ('yookassa', 'cryptomus' или None для всех)
            
        Returns:
            Список платежей пользователя
        """
        try:
            payments = []
            
            if payment_type is None or payment_type.lower() == 'yookassa':
                yookassa_query = select(YPayments).where(YPayments.tg_id == tg_id)
                yookassa_result = await self.session.execute(yookassa_query)
                payments.extend(yookassa_result.scalars().all())
            
            if payment_type is None or payment_type.lower() == 'cryptomus':
                cryptomus_query = select(CPayments).where(CPayments.tg_id == tg_id)
                cryptomus_result = await self.session.execute(cryptomus_query)
                payments.extend(cryptomus_result.scalars().all())
            
            logger.debug(f"Retrieved {len(payments)} payments for user {tg_id}")
            return payments
            
        except Exception as e:
            logger.error(f"Error getting payments for user {tg_id}: {e}")
            raise
    
    async def get_payment_stats(self) -> dict:
        """
        Получить статистику платежей.
        
        Returns:
            Словарь со статистикой
        """
        try:
            from sqlalchemy import func
            
            # YooKassa статистика
            yookassa_query = select(func.count(YPayments.id))
            yookassa_result = await self.session.execute(yookassa_query)
            yookassa_count = yookassa_result.scalar()
            
            # Cryptomus статистика
            cryptomus_query = select(func.count(CPayments.id))
            cryptomus_result = await self.session.execute(cryptomus_query)
            cryptomus_count = cryptomus_result.scalar()
            
            stats = {
                'total_payments': yookassa_count + cryptomus_count,
                'yookassa_payments': yookassa_count,
                'cryptomus_payments': cryptomus_count
            }
            
            logger.debug(f"Payment stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting payment stats: {e}")
            raise
    
    async def get_payments_by_callback(self, callback: str, payment_type: Optional[str] = None) -> List[Union[YPayments, CPayments]]:
        """
        Получить платежи по callback данным.
        
        Args:
            callback: Callback данные
            payment_type: Тип платежей
            
        Returns:
            Список платежей
        """
        try:
            payments = []
            
            if payment_type is None or payment_type.lower() == 'yookassa':
                yookassa_query = select(YPayments).where(YPayments.callback == callback)
                yookassa_result = await self.session.execute(yookassa_query)
                payments.extend(yookassa_result.scalars().all())
            
            if payment_type is None or payment_type.lower() == 'cryptomus':
                cryptomus_query = select(CPayments).where(CPayments.callback == callback)
                cryptomus_result = await self.session.execute(cryptomus_query)
                payments.extend(cryptomus_result.scalars().all())
            
            logger.debug(f"Retrieved {len(payments)} payments for callback {callback}")
            return payments
            
        except Exception as e:
            logger.error(f"Error getting payments by callback {callback}: {e}")
            raise
