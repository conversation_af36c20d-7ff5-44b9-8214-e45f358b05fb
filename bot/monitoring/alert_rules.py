"""
Предустановленные правила алертов для критических событий VPN бота.
"""

import psutil
import time
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any, Optional

from .alerts import AlertRule, AlertSeverity
from utils.health_checks import <PERSON>Checker, HealthStatus
from utils.logging_config import get_logger

logger = get_logger(__name__)


class SystemMetrics:
    """Класс для получения системных метрик."""
    
    @staticmethod
    def get_memory_usage_percent() -> float:
        """Возвращает процент использования памяти."""
        try:
            return psutil.virtual_memory().percent
        except Exception as e:
            logger.error(f"Failed to get memory usage: {e}")
            return 0.0
    
    @staticmethod
    def get_cpu_usage_percent() -> float:
        """Возвращает процент использования CPU."""
        try:
            return psutil.cpu_percent(interval=1)
        except Exception as e:
            logger.error(f"Failed to get CPU usage: {e}")
            return 0.0
    
    @staticmethod
    def get_disk_usage_percent() -> float:
        """Возвращает процент использования диска."""
        try:
            return psutil.disk_usage('/').percent
        except Exception as e:
            logger.error(f"Failed to get disk usage: {e}")
            return 0.0


class ErrorCounter:
    """Счетчик ошибок для алертов."""
    
    def __init__(self):
        self.error_counts: Dict[str, list] = {}
        self.window_size = 300  # 5 минут
    
    def add_error(self, error_type: str) -> None:
        """Добавляет ошибку в счетчик."""
        current_time = time.time()
        
        if error_type not in self.error_counts:
            self.error_counts[error_type] = []
        
        self.error_counts[error_type].append(current_time)
        
        # Очищаем старые записи
        self._cleanup_old_errors(error_type, current_time)
    
    def get_error_rate(self, error_type: str, window_seconds: int = 60) -> float:
        """
        Возвращает количество ошибок за указанный период.
        
        Args:
            error_type: Тип ошибки
            window_seconds: Период в секундах
            
        Returns:
            Количество ошибок за период
        """
        if error_type not in self.error_counts:
            return 0.0
        
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        return len([
            timestamp for timestamp in self.error_counts[error_type]
            if timestamp >= cutoff_time
        ])
    
    def _cleanup_old_errors(self, error_type: str, current_time: float) -> None:
        """Удаляет старые записи об ошибках."""
        cutoff_time = current_time - self.window_size
        self.error_counts[error_type] = [
            timestamp for timestamp in self.error_counts[error_type]
            if timestamp >= cutoff_time
        ]


# Глобальные экземпляры
health_checker = HealthChecker()
error_counter = ErrorCounter()
system_metrics = SystemMetrics()


def create_default_alert_rules() -> Dict[str, AlertRule]:
    """
    Создает набор предустановленных правил алертов.
    
    Returns:
        Словарь с правилами алертов
    """
    rules = {}
    
    # 1. Алерт для проблем с базой данных
    rules["database_unhealthy"] = AlertRule(
        name="database_unhealthy",
        description="База данных недоступна или работает некорректно",
        severity=AlertSeverity.CRITICAL,
        condition=lambda: _check_database_health(),
        for_duration=timedelta(seconds=30),
        labels={"component": "database", "severity": "critical"},
        annotations={
            "summary": "Проблемы с подключением к базе данных",
            "runbook": "Проверьте статус MariaDB и сетевое подключение"
        }
    )
    
    # 2. Алерт для проблем с Marzban API
    rules["marzban_api_unhealthy"] = AlertRule(
        name="marzban_api_unhealthy", 
        description="Marzban API недоступен или работает медленно",
        severity=AlertSeverity.CRITICAL,
        condition=lambda: _check_marzban_health(),
        for_duration=timedelta(minutes=1),
        labels={"component": "marzban", "severity": "critical"},
        annotations={
            "summary": "Проблемы с Marzban API",
            "runbook": "Проверьте статус Marzban панели и сетевое подключение"
        }
    )
    
    # 3. Алерт для высокого использования памяти
    rules["high_memory_usage"] = AlertRule(
        name="high_memory_usage",
        description="Высокое использование оперативной памяти",
        severity=AlertSeverity.HIGH,
        condition=lambda: system_metrics.get_memory_usage_percent() > 80,
        threshold=80.0,
        for_duration=timedelta(minutes=2),
        labels={"component": "system", "resource": "memory"},
        annotations={
            "summary": "Использование памяти превышает 80%",
            "runbook": "Проверьте процессы, потребляющие память"
        }
    )
    
    # 4. Алерт для высокого использования CPU
    rules["high_cpu_usage"] = AlertRule(
        name="high_cpu_usage",
        description="Высокое использование процессора",
        severity=AlertSeverity.MEDIUM,
        condition=lambda: system_metrics.get_cpu_usage_percent() > 85,
        threshold=85.0,
        for_duration=timedelta(minutes=3),
        labels={"component": "system", "resource": "cpu"},
        annotations={
            "summary": "Использование CPU превышает 85%",
            "runbook": "Проверьте процессы, потребляющие CPU"
        }
    )
    
    # 5. Алерт для высокого использования диска
    rules["high_disk_usage"] = AlertRule(
        name="high_disk_usage",
        description="Высокое использование дискового пространства",
        severity=AlertSeverity.HIGH,
        condition=lambda: system_metrics.get_disk_usage_percent() > 90,
        threshold=90.0,
        for_duration=timedelta(minutes=5),
        labels={"component": "system", "resource": "disk"},
        annotations={
            "summary": "Использование диска превышает 90%",
            "runbook": "Очистите логи и временные файлы"
        }
    )
    
    # 6. Алерт для высокой частоты ошибок
    rules["high_error_rate"] = AlertRule(
        name="high_error_rate",
        description="Высокая частота ошибок в приложении",
        severity=AlertSeverity.HIGH,
        condition=lambda: error_counter.get_error_rate("general", 60) > 10,
        threshold=10.0,
        for_duration=timedelta(minutes=1),
        labels={"component": "application", "type": "errors"},
        annotations={
            "summary": "Более 10 ошибок в минуту",
            "runbook": "Проверьте логи приложения на наличие ошибок"
        }
    )
    
    # 7. Алерт для ошибок платежных систем
    rules["payment_system_errors"] = AlertRule(
        name="payment_system_errors",
        description="Ошибки в платежных системах",
        severity=AlertSeverity.CRITICAL,
        condition=lambda: (
            error_counter.get_error_rate("yookassa_error", 300) > 3 or
            error_counter.get_error_rate("cryptomus_error", 300) > 3
        ),
        threshold=3.0,
        for_duration=timedelta(minutes=1),
        labels={"component": "payments", "severity": "critical"},
        annotations={
            "summary": "Ошибки в платежных системах",
            "runbook": "Проверьте статус YooKassa и Cryptomus API"
        }
    )
    
    # 8. Алерт для критических исключений
    rules["critical_exceptions"] = AlertRule(
        name="critical_exceptions",
        description="Критические исключения в приложении",
        severity=AlertSeverity.CRITICAL,
        condition=lambda: error_counter.get_error_rate("critical_exception", 60) > 0,
        threshold=0.0,
        for_duration=timedelta(seconds=10),
        labels={"component": "application", "type": "exceptions"},
        annotations={
            "summary": "Критические исключения в приложении",
            "runbook": "Немедленно проверьте логи и состояние приложения"
        }
    )
    
    return rules


def _check_database_health() -> bool:
    """Проверяет здоровье базы данных."""
    try:
        import asyncio
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Если цикл уже запущен, создаем задачу
            task = asyncio.create_task(health_checker.check_database())
            # Не можем ждать в синхронном контексте, возвращаем False для безопасности
            return False
        else:
            # Если цикла нет, запускаем синхронно
            result = loop.run_until_complete(health_checker.check_database())
            return result.status == HealthStatus.HEALTHY
    except Exception as e:
        logger.error(f"Error checking database health: {e}")
        return False


def _check_marzban_health() -> bool:
    """Проверяет здоровье Marzban API."""
    try:
        import asyncio
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Если цикл уже запущен, создаем задачу
            task = asyncio.create_task(health_checker.check_marzban_api())
            # Не можем ждать в синхронном контексте, возвращаем False для безопасности
            return False
        else:
            # Если цикла нет, запускаем синхронно
            result = loop.run_until_complete(health_checker.check_marzban_api())
            return result.status == HealthStatus.HEALTHY
    except Exception as e:
        logger.error(f"Error checking Marzban health: {e}")
        return False


# Функции для добавления ошибок в счетчик
def record_payment_error(payment_system: str) -> None:
    """Записывает ошибку платежной системы."""
    error_counter.add_error(f"{payment_system}_error")


def record_critical_exception() -> None:
    """Записывает критическое исключение."""
    error_counter.add_error("critical_exception")


def record_general_error() -> None:
    """Записывает общую ошибку."""
    error_counter.add_error("general")
