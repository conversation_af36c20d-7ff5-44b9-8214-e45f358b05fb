"""
Мониторинг логов для автоматического создания алертов на основе ошибок.
"""

import logging
import re
from typing import Dict, Set, Optional
from datetime import datetime, timedelta

from .alert_rules import error_counter, record_payment_error, record_critical_exception, record_general_error
from utils.logging_config import get_logger

logger = get_logger(__name__)


class AlertLogHandler(logging.Handler):
    """
    Обработчик логов для автоматического создания алертов.
    Анализирует логи и создает алерты на основе критических событий.
    """
    
    def __init__(self):
        super().__init__()
        self.setLevel(logging.WARNING)
        
        # Паттерны для определения типов ошибок
        self.error_patterns = {
            'payment_yookassa': [
                r'yookassa.*error',
                r'yookassa.*failed',
                r'yookassa.*timeout',
                r'yookassa.*invalid'
            ],
            'payment_cryptomus': [
                r'cryptomus.*error',
                r'cryptomus.*failed', 
                r'cryptomus.*timeout',
                r'cryptomus.*invalid'
            ],
            'database': [
                r'database.*error',
                r'database.*connection.*failed',
                r'database.*timeout',
                r'sqlalchemy.*error',
                r'mariadb.*error'
            ],
            'marzban': [
                r'marzban.*error',
                r'marzban.*failed',
                r'marzban.*timeout',
                r'marzban.*connection.*failed'
            ],
            'critical_exception': [
                r'critical.*exception',
                r'fatal.*error',
                r'system.*crash',
                r'memory.*error',
                r'segmentation.*fault'
            ],
            'validation_error': [
                r'validation.*error',
                r'invalid.*input',
                r'malformed.*data',
                r'security.*violation'
            ]
        }
        
        # Компилируем регулярные выражения
        self.compiled_patterns = {}
        for category, patterns in self.error_patterns.items():
            self.compiled_patterns[category] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
        
        # Счетчики для предотвращения спама алертов
        self.alert_cooldowns: Dict[str, datetime] = {}
        self.cooldown_duration = timedelta(minutes=5)
    
    def emit(self, record: logging.LogRecord) -> None:
        """
        Обрабатывает запись лога и создает алерты при необходимости.
        
        Args:
            record: Запись лога
        """
        try:
            # Анализируем только WARNING и выше
            if record.levelno < logging.WARNING:
                return
            
            message = record.getMessage().lower()
            
            # Проверяем каждую категорию ошибок
            for category, patterns in self.compiled_patterns.items():
                if self._matches_patterns(message, patterns):
                    self._handle_error_category(category, record)
                    break
            
            # Общий счетчик ошибок для WARNING и выше
            if record.levelno >= logging.WARNING:
                record_general_error()
            
        except Exception as e:
            # Избегаем рекурсии в логировании
            print(f"Error in AlertLogHandler: {e}")
    
    def _matches_patterns(self, message: str, patterns: list) -> bool:
        """Проверяет, соответствует ли сообщение одному из паттернов."""
        return any(pattern.search(message) for pattern in patterns)
    
    def _handle_error_category(self, category: str, record: logging.LogRecord) -> None:
        """
        Обрабатывает ошибку определенной категории.
        
        Args:
            category: Категория ошибки
            record: Запись лога
        """
        # Проверяем cooldown для предотвращения спама
        if self._is_in_cooldown(category):
            return
        
        # Записываем ошибку в соответствующий счетчик
        if category == 'payment_yookassa':
            record_payment_error('yookassa')
        elif category == 'payment_cryptomus':
            record_payment_error('cryptomus')
        elif category == 'critical_exception':
            record_critical_exception()
            self._set_cooldown(category)  # Критические ошибки имеют cooldown
        elif category in ['database', 'marzban']:
            record_general_error()
            self._set_cooldown(category)
        
        # Логируем обработку ошибки
        logger.info(
            f"Alert triggered by log pattern: {category}",
            extra={
                "category": category,
                "original_message": record.getMessage(),
                "level": record.levelname,
                "module": record.module,
                "event_type": "log_alert_triggered"
            }
        )
    
    def _is_in_cooldown(self, category: str) -> bool:
        """Проверяет, находится ли категория в cooldown."""
        if category not in self.alert_cooldowns:
            return False
        
        return datetime.now() - self.alert_cooldowns[category] < self.cooldown_duration
    
    def _set_cooldown(self, category: str) -> None:
        """Устанавливает cooldown для категории."""
        self.alert_cooldowns[category] = datetime.now()


class LogMetricsCollector:
    """
    Сборщик метрик из логов для мониторинга.
    """
    
    def __init__(self):
        self.metrics = {
            'total_logs': 0,
            'error_logs': 0,
            'warning_logs': 0,
            'critical_logs': 0,
            'by_module': {},
            'by_category': {}
        }
        self.start_time = datetime.now()
    
    def record_log(self, record: logging.LogRecord, category: Optional[str] = None) -> None:
        """
        Записывает метрики лога.
        
        Args:
            record: Запись лога
            category: Категория ошибки (если определена)
        """
        self.metrics['total_logs'] += 1
        
        # Счетчики по уровням
        if record.levelno >= logging.CRITICAL:
            self.metrics['critical_logs'] += 1
        elif record.levelno >= logging.ERROR:
            self.metrics['error_logs'] += 1
        elif record.levelno >= logging.WARNING:
            self.metrics['warning_logs'] += 1
        
        # Счетчики по модулям
        module = record.module or 'unknown'
        if module not in self.metrics['by_module']:
            self.metrics['by_module'][module] = 0
        self.metrics['by_module'][module] += 1
        
        # Счетчики по категориям
        if category:
            if category not in self.metrics['by_category']:
                self.metrics['by_category'][category] = 0
            self.metrics['by_category'][category] += 1
    
    def get_metrics(self) -> Dict:
        """Возвращает собранные метрики."""
        uptime = datetime.now() - self.start_time
        
        return {
            **self.metrics,
            'uptime_seconds': uptime.total_seconds(),
            'error_rate': self.metrics['error_logs'] / max(self.metrics['total_logs'], 1),
            'warning_rate': self.metrics['warning_logs'] / max(self.metrics['total_logs'], 1)
        }
    
    def reset_metrics(self) -> None:
        """Сбрасывает метрики."""
        self.metrics = {
            'total_logs': 0,
            'error_logs': 0,
            'warning_logs': 0,
            'critical_logs': 0,
            'by_module': {},
            'by_category': {}
        }
        self.start_time = datetime.now()


# Глобальные экземпляры
alert_log_handler = AlertLogHandler()
log_metrics_collector = LogMetricsCollector()


def setup_log_monitoring() -> None:
    """
    Настраивает мониторинг логов для алертов.
    Добавляет обработчик к корневому логгеру.
    """
    # Получаем корневой логгер
    root_logger = logging.getLogger()
    
    # Проверяем, не добавлен ли уже наш обработчик
    for handler in root_logger.handlers:
        if isinstance(handler, AlertLogHandler):
            logger.info("Alert log handler already configured")
            return
    
    # Добавляем наш обработчик
    root_logger.addHandler(alert_log_handler)
    
    logger.info("Log monitoring for alerts configured successfully")


def get_log_metrics() -> Dict:
    """Возвращает метрики логирования."""
    return log_metrics_collector.get_metrics()


def test_alert_patterns() -> None:
    """Тестирует паттерны алертов с примерами сообщений."""
    test_messages = [
        "YooKassa payment failed with error 500",
        "Cryptomus API timeout occurred",
        "Database connection failed",
        "Marzban API returned error",
        "Critical exception in payment processing",
        "Validation error: invalid user input",
        "Normal info message"
    ]
    
    handler = AlertLogHandler()
    
    for message in test_messages:
        # Создаем тестовую запись лога
        record = logging.LogRecord(
            name="test",
            level=logging.ERROR,
            pathname="",
            lineno=0,
            msg=message,
            args=(),
            exc_info=None
        )
        
        print(f"Testing: {message}")
        
        # Проверяем каждую категорию
        message_lower = message.lower()
        for category, patterns in handler.compiled_patterns.items():
            if handler._matches_patterns(message_lower, patterns):
                print(f"  -> Matches category: {category}")
                break
        else:
            print("  -> No category match")
        
        print()


if __name__ == "__main__":
    # Запуск тестирования паттернов
    test_alert_patterns()
