"""
Конфигурация системы алертов.
"""

import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class AlertConfig:
    """Конфигурация алертов."""
    
    # Общие настройки
    enabled: bool = True
    check_interval: int = 30  # секунды
    
    # Telegram уведомления
    telegram_enabled: bool = True
    admin_chat_ids: List[int] = None
    
    # Пороги для алертов
    memory_threshold: float = 80.0  # процент
    cpu_threshold: float = 85.0     # процент
    disk_threshold: float = 90.0    # процент
    error_rate_threshold: float = 10.0  # ошибок в минуту
    payment_error_threshold: float = 3.0  # ошибок за 5 минут
    
    # Настройки cooldown
    critical_alert_cooldown: int = 300  # 5 минут
    general_alert_cooldown: int = 600   # 10 минут
    
    # Включение/отключение отдельных правил
    rules_enabled: Dict[str, bool] = None
    
    def __post_init__(self):
        if self.admin_chat_ids is None:
            self.admin_chat_ids = []
        
        if self.rules_enabled is None:
            self.rules_enabled = {
                "database_unhealthy": True,
                "marzban_api_unhealthy": True,
                "high_memory_usage": True,
                "high_cpu_usage": True,
                "high_disk_usage": True,
                "high_error_rate": True,
                "payment_system_errors": True,
                "critical_exceptions": True
            }


def load_alert_config() -> AlertConfig:
    """
    Загружает конфигурацию алертов из переменных окружения.
    
    Returns:
        Конфигурация алертов
    """
    config = AlertConfig()
    
    try:
        # Общие настройки
        config.enabled = os.getenv("ALERTS_ENABLED", "true").lower() == "true"
        config.check_interval = int(os.getenv("ALERTS_CHECK_INTERVAL", "30"))
        
        # Telegram настройки
        config.telegram_enabled = os.getenv("ALERTS_TELEGRAM_ENABLED", "true").lower() == "true"
        
        # Парсим список admin chat IDs
        admin_chats = os.getenv("ALERTS_ADMIN_CHAT_IDS", "")
        if admin_chats:
            try:
                config.admin_chat_ids = [int(chat_id.strip()) for chat_id in admin_chats.split(",") if chat_id.strip()]
            except ValueError as e:
                logger.error(f"Invalid admin chat IDs format: {admin_chats}, error: {e}")
                config.admin_chat_ids = []
        
        # Пороги алертов
        config.memory_threshold = float(os.getenv("ALERTS_MEMORY_THRESHOLD", "80.0"))
        config.cpu_threshold = float(os.getenv("ALERTS_CPU_THRESHOLD", "85.0"))
        config.disk_threshold = float(os.getenv("ALERTS_DISK_THRESHOLD", "90.0"))
        config.error_rate_threshold = float(os.getenv("ALERTS_ERROR_RATE_THRESHOLD", "10.0"))
        config.payment_error_threshold = float(os.getenv("ALERTS_PAYMENT_ERROR_THRESHOLD", "3.0"))
        
        # Cooldown настройки
        config.critical_alert_cooldown = int(os.getenv("ALERTS_CRITICAL_COOLDOWN", "300"))
        config.general_alert_cooldown = int(os.getenv("ALERTS_GENERAL_COOLDOWN", "600"))
        
        # Включение/отключение правил
        for rule_name in config.rules_enabled.keys():
            env_var = f"ALERTS_RULE_{rule_name.upper()}_ENABLED"
            if os.getenv(env_var):
                config.rules_enabled[rule_name] = os.getenv(env_var, "true").lower() == "true"
        
        logger.info(f"Alert configuration loaded: {len(config.admin_chat_ids)} admin chats configured")
        
    except Exception as e:
        logger.error(f"Error loading alert configuration: {e}", exc_info=True)
        # Возвращаем конфигурацию по умолчанию
        config = AlertConfig()
    
    return config


def get_alert_config_summary() -> Dict[str, Any]:
    """
    Возвращает краткую сводку конфигурации алертов.
    
    Returns:
        Словарь с основными настройками
    """
    config = load_alert_config()
    
    return {
        "enabled": config.enabled,
        "check_interval": config.check_interval,
        "telegram_enabled": config.telegram_enabled,
        "admin_chats_count": len(config.admin_chat_ids),
        "thresholds": {
            "memory": config.memory_threshold,
            "cpu": config.cpu_threshold,
            "disk": config.disk_threshold,
            "error_rate": config.error_rate_threshold,
            "payment_errors": config.payment_error_threshold
        },
        "enabled_rules": sum(1 for enabled in config.rules_enabled.values() if enabled),
        "total_rules": len(config.rules_enabled)
    }


def validate_alert_config(config: AlertConfig) -> List[str]:
    """
    Валидирует конфигурацию алертов.
    
    Args:
        config: Конфигурация для валидации
        
    Returns:
        Список ошибок валидации (пустой если все ОК)
    """
    errors = []
    
    # Проверяем интервал проверки
    if config.check_interval < 10:
        errors.append("Check interval must be at least 10 seconds")
    
    if config.check_interval > 3600:
        errors.append("Check interval must be less than 1 hour")
    
    # Проверяем пороги
    if not (0 <= config.memory_threshold <= 100):
        errors.append("Memory threshold must be between 0 and 100")
    
    if not (0 <= config.cpu_threshold <= 100):
        errors.append("CPU threshold must be between 0 and 100")
    
    if not (0 <= config.disk_threshold <= 100):
        errors.append("Disk threshold must be between 0 and 100")
    
    if config.error_rate_threshold < 0:
        errors.append("Error rate threshold must be non-negative")
    
    if config.payment_error_threshold < 0:
        errors.append("Payment error threshold must be non-negative")
    
    # Проверяем cooldown
    if config.critical_alert_cooldown < 60:
        errors.append("Critical alert cooldown must be at least 60 seconds")
    
    if config.general_alert_cooldown < 60:
        errors.append("General alert cooldown must be at least 60 seconds")
    
    # Проверяем Telegram настройки
    if config.telegram_enabled and not config.admin_chat_ids:
        errors.append("Telegram alerts enabled but no admin chat IDs configured")
    
    return errors


def create_example_env_file() -> str:
    """
    Создает пример файла с переменными окружения для алертов.
    
    Returns:
        Содержимое примера .env файла
    """
    return """
# Настройки системы алертов

# Включить/отключить алерты
ALERTS_ENABLED=true

# Интервал проверки алертов (секунды)
ALERTS_CHECK_INTERVAL=30

# Telegram уведомления
ALERTS_TELEGRAM_ENABLED=true
ALERTS_ADMIN_CHAT_IDS=123456789,987654321

# Пороги для алертов (проценты)
ALERTS_MEMORY_THRESHOLD=80.0
ALERTS_CPU_THRESHOLD=85.0
ALERTS_DISK_THRESHOLD=90.0

# Пороги ошибок
ALERTS_ERROR_RATE_THRESHOLD=10.0
ALERTS_PAYMENT_ERROR_THRESHOLD=3.0

# Cooldown для алертов (секунды)
ALERTS_CRITICAL_COOLDOWN=300
ALERTS_GENERAL_COOLDOWN=600

# Включение/отключение отдельных правил
ALERTS_RULE_DATABASE_UNHEALTHY_ENABLED=true
ALERTS_RULE_MARZBAN_API_UNHEALTHY_ENABLED=true
ALERTS_RULE_HIGH_MEMORY_USAGE_ENABLED=true
ALERTS_RULE_HIGH_CPU_USAGE_ENABLED=true
ALERTS_RULE_HIGH_DISK_USAGE_ENABLED=true
ALERTS_RULE_HIGH_ERROR_RATE_ENABLED=true
ALERTS_RULE_PAYMENT_SYSTEM_ERRORS_ENABLED=true
ALERTS_RULE_CRITICAL_EXCEPTIONS_ENABLED=true
"""


# Глобальная конфигурация
alert_config = load_alert_config()


def reload_alert_config() -> AlertConfig:
    """
    Перезагружает конфигурацию алертов.
    
    Returns:
        Обновленная конфигурация
    """
    global alert_config
    alert_config = load_alert_config()
    logger.info("Alert configuration reloaded")
    return alert_config


if __name__ == "__main__":
    # Тестирование конфигурации
    config = load_alert_config()
    print("Alert Configuration:")
    print(f"  Enabled: {config.enabled}")
    print(f"  Check interval: {config.check_interval}s")
    print(f"  Telegram enabled: {config.telegram_enabled}")
    print(f"  Admin chats: {len(config.admin_chat_ids)}")
    print(f"  Memory threshold: {config.memory_threshold}%")
    print(f"  CPU threshold: {config.cpu_threshold}%")
    print(f"  Disk threshold: {config.disk_threshold}%")
    print(f"  Enabled rules: {sum(1 for enabled in config.rules_enabled.values() if enabled)}/{len(config.rules_enabled)}")
    
    # Валидация
    errors = validate_alert_config(config)
    if errors:
        print("\nValidation errors:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\nConfiguration is valid!")
    
    # Пример .env файла
    print("\nExample .env configuration:")
    print(create_example_env_file())
