"""
Dashboard для отображения статуса алертов и мониторинга системы.
"""

import json
from datetime import datetime
from typing import Dict, Any, List
from aiohttp import web

from .alerts import <PERSON>ertManager, AlertSeverity, AlertStatus
from utils.logging_config import get_logger

logger = get_logger(__name__)


class AlertDashboard:
    """Dashboard для мониторинга алертов."""
    
    def __init__(self, alert_manager: AlertManager):
        self.alert_manager = alert_manager
    
    def get_dashboard_html(self) -> str:
        """Генерирует HTML страницу dashboard."""
        status = self.alert_manager.get_status()
        
        html = f"""
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN Bot - Alert Dashboard</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .status-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }}
        .status-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .status-card h3 {{
            margin: 0 0 10px 0;
            color: #333;
        }}
        .status-value {{
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }}
        .status-running {{
            color: #28a745;
        }}
        .status-stopped {{
            color: #dc3545;
        }}
        .alerts-section {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        .alert-item {{
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }}
        .alert-critical {{
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }}
        .alert-high {{
            border-left: 4px solid #fd7e14;
            background-color: #fff3cd;
        }}
        .alert-medium {{
            border-left: 4px solid #ffc107;
            background-color: #fff3cd;
        }}
        .alert-low {{
            border-left: 4px solid #17a2b8;
            background-color: #d1ecf1;
        }}
        .alert-info {{
            border-left: 4px solid #6c757d;
            background-color: #e2e3e5;
        }}
        .alert-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }}
        .alert-title {{
            font-weight: bold;
            font-size: 16px;
        }}
        .alert-severity {{
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }}
        .severity-critical {{
            background-color: #dc3545;
            color: white;
        }}
        .severity-high {{
            background-color: #fd7e14;
            color: white;
        }}
        .severity-medium {{
            background-color: #ffc107;
            color: black;
        }}
        .severity-low {{
            background-color: #17a2b8;
            color: white;
        }}
        .severity-info {{
            background-color: #6c757d;
            color: white;
        }}
        .alert-description {{
            color: #666;
            margin-bottom: 10px;
        }}
        .alert-meta {{
            font-size: 12px;
            color: #999;
        }}
        .rules-section {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .rule-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }}
        .rule-enabled {{
            color: #28a745;
        }}
        .rule-disabled {{
            color: #dc3545;
        }}
        .refresh-btn {{
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }}
        .refresh-btn:hover {{
            background-color: #0056b3;
        }}
    </style>
    <script>
        function refreshPage() {{
            location.reload();
        }}
        
        // Автообновление каждые 30 секунд
        setInterval(refreshPage, 30000);
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>VPN Bot - Alert Dashboard</h1>
            <p>Система мониторинга и алертов</p>
            <button class="refresh-btn" onclick="refreshPage()">Обновить</button>
            <span style="color: #666; font-size: 14px;">
                Последнее обновление: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </span>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>Статус системы алертов</h3>
                <div class="status-value {'status-running' if status['running'] else 'status-stopped'}">
                    {'🟢 Работает' if status['running'] else '🔴 Остановлена'}
                </div>
            </div>
            
            <div class="status-card">
                <h3>Активные алерты</h3>
                <div class="status-value" style="color: {'#dc3545' if status['metrics']['active_alerts_count'] > 0 else '#28a745'};">
                    {status['metrics']['active_alerts_count']}
                </div>
            </div>
            
            <div class="status-card">
                <h3>Всего правил</h3>
                <div class="status-value" style="color: #007bff;">
                    {status['metrics']['enabled_rules_count']} / {status['metrics']['rules_count']}
                </div>
                <small>включено / всего</small>
            </div>
            
            <div class="status-card">
                <h3>Уведомления отправлено</h3>
                <div class="status-value" style="color: #28a745;">
                    {status['metrics']['notifications_sent']}
                </div>
                <small>Ошибок: {status['metrics']['notification_failures']}</small>
            </div>
        </div>
        
        {self._generate_alerts_section(status['active_alerts'])}
        
        {self._generate_rules_section(status['rules'])}
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_alerts_section(self, alerts: List[Dict[str, Any]]) -> str:
        """Генерирует секцию с активными алертами."""
        if not alerts:
            return """
            <div class="alerts-section">
                <h2>Активные алерты</h2>
                <p style="color: #28a745; font-weight: bold;">✅ Активных алертов нет</p>
            </div>
            """
        
        alerts_html = '<div class="alerts-section"><h2>Активные алерты</h2>'
        
        for alert in alerts:
            severity = alert['severity']
            created_at = datetime.fromisoformat(alert['created_at']).strftime('%Y-%m-%d %H:%M:%S')
            
            value_info = ""
            if alert.get('value') is not None:
                value_info = f"Значение: {alert['value']}"
                if alert.get('threshold') is not None:
                    value_info += f" (порог: {alert['threshold']})"
            
            alerts_html += f"""
            <div class="alert-item alert-{severity}">
                <div class="alert-header">
                    <div class="alert-title">{alert['name']}</div>
                    <div class="alert-severity severity-{severity}">{severity}</div>
                </div>
                <div class="alert-description">{alert['description']}</div>
                {f'<div style="margin-bottom: 5px;"><strong>{value_info}</strong></div>' if value_info else ''}
                <div class="alert-meta">
                    Создан: {created_at} | ID: {alert['id']}
                </div>
            </div>
            """
        
        alerts_html += '</div>'
        return alerts_html
    
    def _generate_rules_section(self, rules: List[Dict[str, Any]]) -> str:
        """Генерирует секцию с правилами алертов."""
        rules_html = '<div class="rules-section"><h2>Правила алертов</h2>'
        
        for rule in rules:
            status_class = "rule-enabled" if rule['enabled'] else "rule-disabled"
            status_text = "Включено" if rule['enabled'] else "Отключено"
            
            last_check = "Никогда"
            if rule['last_check']:
                last_check = datetime.fromisoformat(rule['last_check']).strftime('%H:%M:%S')
            
            firing_status = ""
            if rule['firing_since']:
                firing_since = datetime.fromisoformat(rule['firing_since']).strftime('%H:%M:%S')
                firing_status = f" | 🔥 Срабатывает с {firing_since}"
            
            rules_html += f"""
            <div class="rule-item">
                <div>
                    <strong>{rule['name']}</strong>
                    <br>
                    <small>Критичность: {rule['severity']} | Последняя проверка: {last_check}{firing_status}</small>
                </div>
                <div class="{status_class}">
                    {status_text}
                </div>
            </div>
            """
        
        rules_html += '</div>'
        return rules_html
    
    async def dashboard_endpoint(self, request: web.Request) -> web.Response:
        """Endpoint для отображения dashboard."""
        try:
            html = self.get_dashboard_html()
            return web.Response(
                text=html,
                content_type='text/html',
                headers={'Cache-Control': 'no-cache'}
            )
        except Exception as e:
            logger.error(f"Error generating dashboard: {e}", exc_info=True)
            return web.Response(
                text=f"Error generating dashboard: {str(e)}",
                status=500
            )
    
    async def api_status_endpoint(self, request: web.Request) -> web.Response:
        """API endpoint для получения статуса алертов в JSON."""
        try:
            status = self.alert_manager.get_status()
            return web.json_response(status)
        except Exception as e:
            logger.error(f"Error getting alert status: {e}", exc_info=True)
            return web.json_response(
                {"error": str(e)},
                status=500
            )
    
    async def api_alerts_endpoint(self, request: web.Request) -> web.Response:
        """API endpoint для получения активных алертов."""
        try:
            alerts = self.alert_manager.get_active_alerts()
            return web.json_response([alert.to_dict() for alert in alerts])
        except Exception as e:
            logger.error(f"Error getting active alerts: {e}", exc_info=True)
            return web.json_response(
                {"error": str(e)},
                status=500
            )
