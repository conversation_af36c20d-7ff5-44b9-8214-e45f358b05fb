"""
Система алертов для мониторинга критических событий VPN бота.
Поддерживает уведомления через Telegram и интеграцию с метриками.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json

from utils.logging_config import get_logger
from utils.health_checks import HealthChecker, HealthStatus

logger = get_logger(__name__)


class AlertSeverity(Enum):
    """Уровни критичности алертов."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AlertStatus(Enum):
    """Статусы алертов."""
    FIRING = "firing"
    RESOLVED = "resolved"
    SILENCED = "silenced"


@dataclass
class Alert:
    """Модель алерта."""
    id: str
    name: str
    description: str
    severity: AlertSeverity
    status: AlertStatus = AlertStatus.FIRING
    created_at: datetime = field(default_factory=datetime.now)
    resolved_at: Optional[datetime] = None
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    value: Optional[float] = None
    threshold: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Конвертирует алерт в словарь."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "severity": self.severity.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "labels": self.labels,
            "annotations": self.annotations,
            "value": self.value,
            "threshold": self.threshold
        }
    
    def resolve(self) -> None:
        """Помечает алерт как решенный."""
        self.status = AlertStatus.RESOLVED
        self.resolved_at = datetime.now()
    
    def silence(self) -> None:
        """Заглушает алерт."""
        self.status = AlertStatus.SILENCED


@dataclass
class AlertRule:
    """Правило для создания алертов."""
    name: str
    description: str
    severity: AlertSeverity
    condition: Callable[[], bool]
    threshold: Optional[float] = None
    for_duration: timedelta = field(default_factory=lambda: timedelta(minutes=1))
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    enabled: bool = True
    last_check: Optional[datetime] = None
    firing_since: Optional[datetime] = None
    
    def should_fire(self) -> bool:
        """Проверяет, должен ли сработать алерт."""
        if not self.enabled:
            return False
        
        try:
            condition_met = self.condition()
            now = datetime.now()
            
            if condition_met:
                if self.firing_since is None:
                    self.firing_since = now
                
                # Проверяем, прошло ли достаточно времени
                return (now - self.firing_since) >= self.for_duration
            else:
                self.firing_since = None
                return False
                
        except Exception as e:
            logger.error(f"Error checking alert rule {self.name}: {e}", exc_info=True)
            return False


class AlertChannel:
    """Базовый класс для каналов уведомлений."""
    
    async def send_alert(self, alert: Alert) -> bool:
        """
        Отправляет алерт через канал.
        
        Args:
            alert: Алерт для отправки
            
        Returns:
            True если успешно отправлено
        """
        raise NotImplementedError
    
    async def send_resolution(self, alert: Alert) -> bool:
        """
        Отправляет уведомление о решении алерта.
        
        Args:
            alert: Решенный алерт
            
        Returns:
            True если успешно отправлено
        """
        raise NotImplementedError


class TelegramAlertChannel(AlertChannel):
    """Канал уведомлений через Telegram."""
    
    def __init__(self, bot, admin_chat_ids: List[int]):
        """
        Инициализирует Telegram канал.
        
        Args:
            bot: Экземпляр Telegram бота
            admin_chat_ids: Список ID чатов администраторов
        """
        self.bot = bot
        self.admin_chat_ids = admin_chat_ids
    
    def _format_alert_message(self, alert: Alert) -> str:
        """Форматирует сообщение алерта для Telegram."""
        severity_emoji = {
            AlertSeverity.CRITICAL: "🔴",
            AlertSeverity.HIGH: "🟠", 
            AlertSeverity.MEDIUM: "🟡",
            AlertSeverity.LOW: "🔵",
            AlertSeverity.INFO: "ℹ️"
        }
        
        emoji = severity_emoji.get(alert.severity, "⚠️")
        
        message = f"{emoji} <b>АЛЕРТ: {alert.name}</b>\n\n"
        message += f"📝 <b>Описание:</b> {alert.description}\n"
        message += f"🔥 <b>Критичность:</b> {alert.severity.value.upper()}\n"
        message += f"⏰ <b>Время:</b> {alert.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if alert.value is not None:
            message += f"📊 <b>Значение:</b> {alert.value}"
            if alert.threshold is not None:
                message += f" (порог: {alert.threshold})"
            message += "\n"
        
        if alert.labels:
            message += f"\n🏷️ <b>Метки:</b>\n"
            for key, value in alert.labels.items():
                message += f"  • {key}: {value}\n"
        
        if alert.annotations:
            message += f"\n📋 <b>Дополнительно:</b>\n"
            for key, value in alert.annotations.items():
                message += f"  • {key}: {value}\n"
        
        message += f"\n🆔 <code>{alert.id}</code>"
        
        return message
    
    def _format_resolution_message(self, alert: Alert) -> str:
        """Форматирует сообщение о решении алерта."""
        duration = ""
        if alert.resolved_at and alert.created_at:
            delta = alert.resolved_at - alert.created_at
            duration = f" (длительность: {delta})"
        
        message = f"✅ <b>РЕШЕНО: {alert.name}</b>\n\n"
        message += f"📝 {alert.description}\n"
        message += f"⏰ Решено: {alert.resolved_at.strftime('%Y-%m-%d %H:%M:%S')}{duration}\n"
        message += f"🆔 <code>{alert.id}</code>"
        
        return message
    
    async def send_alert(self, alert: Alert) -> bool:
        """Отправляет алерт администраторам."""
        message = self._format_alert_message(alert)
        
        success_count = 0
        for chat_id in self.admin_chat_ids:
            try:
                await self.bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode="HTML",
                    disable_web_page_preview=True
                )
                success_count += 1
                
            except Exception as e:
                logger.error(f"Failed to send alert to chat {chat_id}: {e}")
        
        return success_count > 0
    
    async def send_resolution(self, alert: Alert) -> bool:
        """Отправляет уведомление о решении алерта."""
        message = self._format_resolution_message(alert)
        
        success_count = 0
        for chat_id in self.admin_chat_ids:
            try:
                await self.bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode="HTML",
                    disable_web_page_preview=True
                )
                success_count += 1
                
            except Exception as e:
                logger.error(f"Failed to send resolution to chat {chat_id}: {e}")
        
        return success_count > 0


class AlertManager:
    """Менеджер алертов для управления правилами и уведомлениями."""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.channels: List[AlertChannel] = []
        self.health_checker = HealthChecker()
        self._running = False
        self._check_task: Optional[asyncio.Task] = None
        
        # Счетчики для метрик
        self.metrics = {
            "alerts_fired": 0,
            "alerts_resolved": 0,
            "notifications_sent": 0,
            "notification_failures": 0
        }
    
    def add_channel(self, channel: AlertChannel) -> None:
        """Добавляет канал уведомлений."""
        self.channels.append(channel)
        logger.info(f"Added alert channel: {type(channel).__name__}")
    
    def add_rule(self, rule: AlertRule) -> None:
        """Добавляет правило алерта."""
        self.rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """Удаляет правило алерта."""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
            return True
        return False
    
    def get_rule(self, rule_name: str) -> Optional[AlertRule]:
        """Получает правило по имени."""
        return self.rules.get(rule_name)
    
    def enable_rule(self, rule_name: str) -> bool:
        """Включает правило алерта."""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            logger.info(f"Enabled alert rule: {rule_name}")
            return True
        return False
    
    def disable_rule(self, rule_name: str) -> bool:
        """Отключает правило алерта."""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            logger.info(f"Disabled alert rule: {rule_name}")
            return True
        return False

    async def fire_alert(self, rule: AlertRule, value: Optional[float] = None) -> Alert:
        """
        Создает и отправляет алерт.

        Args:
            rule: Правило алерта
            value: Текущее значение метрики

        Returns:
            Созданный алерт
        """
        alert_id = f"{rule.name}_{int(time.time())}"

        alert = Alert(
            id=alert_id,
            name=rule.name,
            description=rule.description,
            severity=rule.severity,
            labels=rule.labels.copy(),
            annotations=rule.annotations.copy(),
            value=value,
            threshold=rule.threshold
        )

        self.active_alerts[alert_id] = alert
        self.metrics["alerts_fired"] += 1

        logger.warning(
            f"Alert fired: {rule.name}",
            extra={
                "alert_id": alert_id,
                "severity": rule.severity.value,
                "value": value,
                "threshold": rule.threshold,
                "event_type": "alert_fired"
            }
        )

        # Отправляем уведомления
        await self._send_notifications(alert)

        return alert

    async def resolve_alert(self, alert_id: str) -> bool:
        """
        Решает алерт.

        Args:
            alert_id: ID алерта

        Returns:
            True если алерт был решен
        """
        if alert_id not in self.active_alerts:
            return False

        alert = self.active_alerts[alert_id]
        alert.resolve()

        self.metrics["alerts_resolved"] += 1

        duration_seconds = None
        if alert.resolved_at and alert.created_at:
            duration_seconds = (alert.resolved_at - alert.created_at).total_seconds()

        logger.info(
            f"Alert resolved: {alert.name}",
            extra={
                "alert_id": alert_id,
                "duration_seconds": duration_seconds,
                "event_type": "alert_resolved"
            }
        )

        # Отправляем уведомления о решении
        await self._send_resolution_notifications(alert)

        # Удаляем из активных алертов
        del self.active_alerts[alert_id]

        return True

    async def _send_notifications(self, alert: Alert) -> None:
        """Отправляет уведомления через все каналы."""
        for channel in self.channels:
            try:
                success = await channel.send_alert(alert)
                if success:
                    self.metrics["notifications_sent"] += 1
                else:
                    self.metrics["notification_failures"] += 1

            except Exception as e:
                logger.error(f"Failed to send alert via {type(channel).__name__}: {e}")
                self.metrics["notification_failures"] += 1

    async def _send_resolution_notifications(self, alert: Alert) -> None:
        """Отправляет уведомления о решении алерта."""
        for channel in self.channels:
            try:
                success = await channel.send_resolution(alert)
                if success:
                    self.metrics["notifications_sent"] += 1
                else:
                    self.metrics["notification_failures"] += 1

            except Exception as e:
                logger.error(f"Failed to send resolution via {type(channel).__name__}: {e}")
                self.metrics["notification_failures"] += 1

    async def check_rules(self) -> None:
        """Проверяет все правила алертов."""
        for rule_name, rule in self.rules.items():
            try:
                rule.last_check = datetime.now()

                if rule.should_fire():
                    # Проверяем, нет ли уже активного алерта для этого правила
                    existing_alert = None
                    for alert in self.active_alerts.values():
                        if alert.name == rule.name and alert.status == AlertStatus.FIRING:
                            existing_alert = alert
                            break

                    if not existing_alert:
                        # Получаем текущее значение метрики если возможно
                        value = None
                        try:
                            if hasattr(rule.condition, '__call__'):
                                # Если condition возвращает число, используем его как значение
                                result = rule.condition()
                                if isinstance(result, (int, float)):
                                    value = float(result)
                        except:
                            pass

                        await self.fire_alert(rule, value)

                else:
                    # Проверяем, есть ли активные алерты для решения
                    alerts_to_resolve = []
                    for alert_id, alert in self.active_alerts.items():
                        if alert.name == rule.name and alert.status == AlertStatus.FIRING:
                            alerts_to_resolve.append(alert_id)

                    for alert_id in alerts_to_resolve:
                        await self.resolve_alert(alert_id)

            except Exception as e:
                logger.error(f"Error checking rule {rule_name}: {e}", exc_info=True)

    async def start(self, check_interval: int = 30) -> None:
        """
        Запускает мониторинг алертов.

        Args:
            check_interval: Интервал проверки в секундах
        """
        if self._running:
            logger.warning("Alert manager is already running")
            return

        self._running = True
        logger.info(f"Starting alert manager with {check_interval}s interval")

        async def check_loop():
            while self._running:
                try:
                    await self.check_rules()
                    await asyncio.sleep(check_interval)
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"Error in alert check loop: {e}", exc_info=True)
                    await asyncio.sleep(check_interval)

        self._check_task = asyncio.create_task(check_loop())

    async def stop(self) -> None:
        """Останавливает мониторинг алертов."""
        if not self._running:
            return

        self._running = False

        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass

        logger.info("Alert manager stopped")

    def get_active_alerts(self) -> List[Alert]:
        """Возвращает список активных алертов."""
        return list(self.active_alerts.values())

    def get_metrics(self) -> Dict[str, Any]:
        """Возвращает метрики алертов."""
        return {
            **self.metrics,
            "active_alerts_count": len(self.active_alerts),
            "rules_count": len(self.rules),
            "enabled_rules_count": sum(1 for rule in self.rules.values() if rule.enabled),
            "channels_count": len(self.channels)
        }

    def get_status(self) -> Dict[str, Any]:
        """Возвращает статус системы алертов."""
        return {
            "running": self._running,
            "metrics": self.get_metrics(),
            "active_alerts": [alert.to_dict() for alert in self.active_alerts.values()],
            "rules": [
                {
                    "name": rule.name,
                    "enabled": rule.enabled,
                    "severity": rule.severity.value,
                    "last_check": rule.last_check.isoformat() if rule.last_check else None,
                    "firing_since": rule.firing_since.isoformat() if rule.firing_since else None
                }
                for rule in self.rules.values()
            ]
        }
