"""
Обработчики callback запросов для выбора стран и нод.
Интегрируется с MarzbanCountryNodeService и NodeMonitoringService.
"""

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.utils.i18n import gettext as _

from keyboards.country_selection import CountrySelectionKeyboards
from services.base import get_service_container
from services.marzban_country_node_service import MarzbanCountryNodeService
from services.node_monitoring_service import NodeMonitoringService
from services.node_selection_service import SelectionStrategy
from schemas.country_callbacks import (
    parse_country_callback, is_country_callback, ValidationError,
    CountryCallbackData, NodeCallbackData, ProtocolCallbackData, ConfirmationCallbackData
)
from utils.logging_config import get_logger

logger = get_logger(__name__)

router = Router(name="country-callbacks-router")


class CountryCallbackHandlers:
    """Обработчики callback запросов для стран и нод."""
    
    def __init__(self):
        self.keyboards = CountrySelectionKeyboards()
    
    async def get_services(self):
        """Получить сервисы из контейнера."""
        container = get_service_container()
        marzban_service = container.get_service(MarzbanCountryNodeService)
        monitoring_service = container.get_service(NodeMonitoringService)
        return marzban_service, monitoring_service
    
    async def handle_countries_menu(self, callback: CallbackQuery):
        """Показать главное меню выбора стран."""
        try:
            await callback.message.edit_text(
                text="🌍 <b>Выбор страны и ноды</b>\n\n"
                     "Выберите страну для подключения к VPN серверу.\n"
                     "Вы можете настроить предпочтения или позволить системе автоматически выбрать лучший сервер.",
                reply_markup=self.keyboards.get_countries_menu_keyboard()
            )
            
            logger.info(f"Showed countries menu", extra={
                "user_id": callback.from_user.id,
                "event_type": "countries_menu_shown"
            })
            
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error showing countries menu: {e}", extra={
                "user_id": callback.from_user.id,
                "error": str(e),
                "event_type": "countries_menu_error"
            })
            await callback.answer("❌ Ошибка при загрузке меню", show_alert=True)
    
    async def handle_select_countries(self, callback: CallbackQuery, page: int = 0):
        """Показать список стран для выбора."""
        try:
            marzban_service, _ = await self.get_services()
            
            # Получаем доступные страны с информацией о нодах
            countries_info = await marzban_service.get_available_countries_with_nodes(
                include_stats=True,
                use_cache=True
            )
            
            if not countries_info:
                await callback.message.edit_text(
                    text="❌ <b>Страны недоступны</b>\n\n"
                         "В данный момент нет доступных стран для подключения.\n"
                         "Попробуйте позже.",
                    reply_markup=self.keyboards.get_countries_menu_keyboard()
                )
                await callback.answer()
                return
            
            # Получаем предпочтения пользователя
            user_preferences = await marzban_service.get_user_country_preferences(
                callback.from_user.id
            )
            
            preferred_countries = [pref.country_id for pref in user_preferences] if user_preferences else []
            
            # Создаем клавиатуру
            keyboard = self.keyboards.get_country_selection_keyboard(
                countries=countries_info,
                user_preferences=preferred_countries,
                page=page
            )
            
            # Формируем текст сообщения
            total_countries = len(countries_info)
            total_nodes = sum(country.total_nodes for country in countries_info)
            available_nodes = sum(country.available_nodes for country in countries_info)
            
            text = f"🌍 <b>Выбор страны ({total_countries} стран)</b>\n\n"
            text += f"📊 Всего нод: {total_nodes}\n"
            text += f"🟢 Доступно: {available_nodes}\n\n"
            
            if preferred_countries:
                text += f"⭐ Ваши предпочтения: {', '.join(preferred_countries)}\n\n"
            
            text += "Выберите страны для добавления в предпочтения:\n"
            text += "🟢 - низкая нагрузка, 🟡 - средняя, 🔴 - высокая"
            
            await callback.message.edit_text(
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Showed countries selection", extra={
                "user_id": callback.from_user.id,
                "page": page,
                "total_countries": total_countries,
                "available_nodes": available_nodes,
                "event_type": "countries_selection_shown"
            })
            
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error showing countries selection: {e}", extra={
                "user_id": callback.from_user.id,
                "page": page,
                "error": str(e),
                "event_type": "countries_selection_error"
            })
            await callback.answer("❌ Ошибка при загрузке стран", show_alert=True)
    
    async def handle_toggle_country(self, callback: CallbackQuery, country_id: str):
        """Переключить выбор страны в предпочтениях."""
        try:
            marzban_service, _ = await self.get_services()
            
            # Получаем текущие предпочтения
            user_preferences = await marzban_service.get_user_country_preferences(
                callback.from_user.id
            )
            
            preferred_countries = [pref.country_id for pref in user_preferences] if user_preferences else []
            
            # Переключаем выбор
            if country_id in preferred_countries:
                # Удаляем из предпочтений
                success = await marzban_service.update_user_country_preference(
                    callback.from_user.id,
                    country_id,
                    "remove"
                )
                action_text = "удалена из"
            else:
                # Добавляем в предпочтения
                success = await marzban_service.update_user_country_preference(
                    callback.from_user.id,
                    country_id,
                    "add"
                )
                action_text = "добавлена в"
            
            if success:
                # Обновляем отображение
                await self.handle_select_countries(callback, page=0)
                
                logger.info(f"Country preference toggled", extra={
                    "user_id": callback.from_user.id,
                    "country_id": country_id,
                    "action": "remove" if country_id in preferred_countries else "add",
                    "event_type": "country_preference_toggled"
                })
            else:
                await callback.answer(f"❌ Не удалось обновить предпочтения", show_alert=True)
            
        except Exception as e:
            logger.error(f"Error toggling country preference: {e}", extra={
                "user_id": callback.from_user.id,
                "country_id": country_id,
                "error": str(e),
                "event_type": "country_toggle_error"
            })
            await callback.answer("❌ Ошибка при обновлении предпочтений", show_alert=True)
    
    async def handle_auto_select_country(self, callback: CallbackQuery):
        """Автоматический выбор лучшей страны."""
        try:
            marzban_service, _ = await self.get_services()
            
            # Получаем оптимальную ноду для пользователя
            result = await marzban_service.get_optimal_node_for_user(
                callback.from_user.id,
                strategy=SelectionStrategy.HYBRID,
                use_cache=False
            )
            
            if result.success and result.selected_node:
                node = result.selected_node
                country_name = node.country.name_ru if hasattr(node, 'country') else "Неизвестно"
                
                text = f"🎯 <b>Автовыбор завершен!</b>\n\n"
                text += f"🌍 Страна: {node.country.flag} {country_name}\n"
                text += f"🖥 Нода: {node.name}\n"
                text += f"📊 Загрузка: {node.current_users}/{node.max_users} ({node.load_percentage:.1f}%)\n"
                text += f"⚡ Статус: {'🟢 Онлайн' if node.is_available else '🔴 Офлайн'}\n\n"
                text += f"📋 Причины выбора:\n"
                
                for reason in result.selection_reasons:
                    text += f"• {reason}\n"
                
                # Добавляем страну в предпочтения
                await marzban_service.update_user_country_preference(
                    callback.from_user.id,
                    node.country_id,
                    "add"
                )
                
                await callback.message.edit_text(
                    text=text,
                    reply_markup=self.keyboards.get_countries_menu_keyboard()
                )
                
                logger.info(f"Auto-selected country", extra={
                    "user_id": callback.from_user.id,
                    "country_id": node.country_id,
                    "node_id": node.id,
                    "strategy": result.selection_strategy,
                    "event_type": "country_auto_selected"
                })
                
            else:
                await callback.message.edit_text(
                    text="❌ <b>Автовыбор не удался</b>\n\n"
                         "Не удалось найти подходящую ноду.\n"
                         "Попробуйте выбрать страну вручную.",
                    reply_markup=self.keyboards.get_countries_menu_keyboard()
                )
            
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error in auto country selection: {e}", extra={
                "user_id": callback.from_user.id,
                "error": str(e),
                "event_type": "auto_select_country_error"
            })
            await callback.answer("❌ Ошибка при автовыборе", show_alert=True)
    
    async def handle_my_preferences(self, callback: CallbackQuery):
        """Показать управление предпочтениями пользователя."""
        try:
            marzban_service, _ = await self.get_services()
            
            # Получаем предпочтения пользователя
            user_preferences = await marzban_service.get_user_country_preferences(
                callback.from_user.id
            )
            
            preferred_countries = [pref.country_id for pref in user_preferences] if user_preferences else []
            
            # Получаем доступные страны
            countries_info = await marzban_service.get_available_countries_with_nodes(
                include_stats=False,
                use_cache=True
            )
            
            available_countries = [country.country.id for country in countries_info]
            
            # Создаем клавиатуру
            keyboard = self.keyboards.get_preferences_keyboard(
                user_preferences=preferred_countries,
                available_countries=available_countries
            )
            
            # Формируем текст
            text = "⚙️ <b>Мои предпочтения</b>\n\n"
            
            if preferred_countries:
                text += "📋 <b>Предпочитаемые страны:</b>\n"
                for country_id in preferred_countries:
                    # Находим информацию о стране
                    country_info = next((c for c in countries_info if c.country.id == country_id), None)
                    if country_info:
                        text += f"• {country_info.country.flag} {country_info.country.name_ru} "
                        text += f"({country_info.available_nodes} нод)\n"
                    else:
                        text += f"• {country_id} (недоступна)\n"
                text += "\n"
            else:
                text += "📋 У вас пока нет предпочитаемых стран.\n\n"
            
            text += "Нажмите ❌ рядом со страной, чтобы удалить её из предпочтений.\n"
            text += "Используйте кнопку \"➕ Добавить страну\" для добавления новых предпочтений."
            
            await callback.message.edit_text(
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Showed user preferences", extra={
                "user_id": callback.from_user.id,
                "preferences_count": len(preferred_countries),
                "event_type": "preferences_shown"
            })
            
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error showing preferences: {e}", extra={
                "user_id": callback.from_user.id,
                "error": str(e),
                "event_type": "preferences_error"
            })
            await callback.answer("❌ Ошибка при загрузке предпочтений", show_alert=True)
    
    async def handle_nodes_statistics(self, callback: CallbackQuery):
        """Показать статистику нод."""
        try:
            marzban_service, monitoring_service = await self.get_services()
            
            # Получаем статистику мониторинга
            monitoring_stats = await monitoring_service.get_monitoring_stats()
            
            # Получаем информацию о странах
            countries_info = await marzban_service.get_available_countries_with_nodes(
                include_stats=True,
                use_cache=True
            )
            
            # Формируем статистику
            text = "📊 <b>Статистика нод</b>\n\n"
            
            text += f"🌐 <b>Общая статистика:</b>\n"
            text += f"• Всего нод: {monitoring_stats.total_nodes}\n"
            text += f"• Онлайн: {monitoring_stats.online_nodes} 🟢\n"
            text += f"• Офлайн: {monitoring_stats.offline_nodes} 🔴\n"
            text += f"• Среднее время отклика: {monitoring_stats.average_response_time:.1f}мс\n"
            text += f"• Последняя проверка: {monitoring_stats.last_check_time.strftime('%H:%M:%S')}\n\n"
            
            if countries_info:
                text += f"🌍 <b>По странам:</b>\n"
                for country_info in countries_info[:5]:  # Показываем топ-5
                    country = country_info.country
                    text += f"• {country.flag} {country.name_ru}: "
                    text += f"{country_info.available_nodes}/{country_info.total_nodes} нод "
                    
                    if country_info.average_load_percentage < 50:
                        text += "🟢\n"
                    elif country_info.average_load_percentage < 80:
                        text += "🟡\n"
                    else:
                        text += "🔴\n"
                
                if len(countries_info) > 5:
                    text += f"... и еще {len(countries_info) - 5} стран\n"
            
            await callback.message.edit_text(
                text=text,
                reply_markup=self.keyboards.get_countries_menu_keyboard()
            )
            
            logger.info(f"Showed nodes statistics", extra={
                "user_id": callback.from_user.id,
                "total_nodes": monitoring_stats.total_nodes,
                "online_nodes": monitoring_stats.online_nodes,
                "event_type": "nodes_statistics_shown"
            })
            
            await callback.answer()
            
        except Exception as e:
            logger.error(f"Error showing nodes statistics: {e}", extra={
                "user_id": callback.from_user.id,
                "error": str(e),
                "event_type": "nodes_statistics_error"
            })
            await callback.answer("❌ Ошибка при загрузке статистики", show_alert=True)


# Создаем экземпляр обработчика
handlers = CountryCallbackHandlers()


# Регистрируем обработчики callback запросов

@router.callback_query(F.data == "countries_menu")
async def callback_countries_menu(callback: CallbackQuery):
    """Главное меню выбора стран."""
    await handlers.handle_countries_menu(callback)


@router.callback_query(F.data == "select_countries")
async def callback_select_countries(callback: CallbackQuery):
    """Показать список стран."""
    await handlers.handle_select_countries(callback)


@router.callback_query(F.data.startswith("countries_page_"))
async def callback_countries_page(callback: CallbackQuery):
    """Пагинация стран."""
    try:
        page = int(callback.data.split("_")[-1])
        await handlers.handle_select_countries(callback, page=page)
    except (ValueError, IndexError):
        await callback.answer("❌ Неверный номер страницы", show_alert=True)


@router.callback_query(F.data.startswith("toggle_country_"))
async def callback_toggle_country(callback: CallbackQuery):
    """Переключить выбор страны."""
    try:
        country_id = callback.data.split("_")[-1]
        await handlers.handle_toggle_country(callback, country_id)
    except IndexError:
        await callback.answer("❌ Неверный ID страны", show_alert=True)


@router.callback_query(F.data == "auto_select_country")
async def callback_auto_select_country(callback: CallbackQuery):
    """Автовыбор страны."""
    await handlers.handle_auto_select_country(callback)


@router.callback_query(F.data == "my_preferences")
async def callback_my_preferences(callback: CallbackQuery):
    """Управление предпочтениями."""
    await handlers.handle_my_preferences(callback)


@router.callback_query(F.data == "nodes_statistics")
async def callback_nodes_statistics(callback: CallbackQuery):
    """Статистика нод."""
    await handlers.handle_nodes_statistics(callback)


@router.callback_query(F.data == "back_to_countries_menu")
async def callback_back_to_countries_menu(callback: CallbackQuery):
    """Назад к меню стран."""
    await handlers.handle_countries_menu(callback)


# Обработчик для всех остальных callback связанных со странами
@router.callback_query(lambda c: is_country_callback(c.data))
async def callback_country_handler(callback: CallbackQuery):
    """Универсальный обработчик callback стран."""
    try:
        # Валидируем callback данные
        parsed_callback = parse_country_callback(callback.data)
        
        logger.info(f"Processing country callback", extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "parsed_type": type(parsed_callback).__name__,
            "event_type": "country_callback_processed"
        })
        
        # Пока что просто отвечаем, что функция в разработке
        await callback.answer("🚧 Функция в разработке", show_alert=True)
        
    except ValidationError as e:
        logger.warning(f"Invalid country callback: {e}", extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "error": str(e),
            "event_type": "country_callback_validation_error"
        })
        await callback.answer("❌ Неверный запрос", show_alert=True)
    
    except Exception as e:
        logger.error(f"Error processing country callback: {e}", extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "error": str(e),
            "event_type": "country_callback_error"
        })
        await callback.answer("❌ Ошибка обработки запроса", show_alert=True)
