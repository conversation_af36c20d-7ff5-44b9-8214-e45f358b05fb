"""
Административные команды для VPN бота.
"""

from aiogram import Router, F
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.filters import Command
from aiogram.utils.i18n import gettext as _

from services.base import get_service_container
from services.user_service import UserService
from services.subscription_service import SubscriptionService
from utils.logging_config import get_logger
import glv

logger = get_logger(__name__)
router = Router(name="admin-commands-router")


def is_admin(user_id: int) -> bool:
    """Проверяет, является ли пользователь администратором."""
    admin_id = glv.config.get('ADMIN_USER_ID')
    return admin_id is not None and user_id == admin_id


def get_admin_menu_keyboard() -> InlineKeyboardMarkup:
    """Создает клавиатуру админ-меню."""
    keyboard = [
        [
            InlineKeyboardButton(text="📊 Статистика", callback_data="admin_stats"),
            InlineKeyboardButton(text="👥 Пользователи", callback_data="admin_users")
        ],
        [
            InlineKeyboardButton(text="🔧 Настройки", callback_data="admin_settings"),
            InlineKeyboardButton(text="📝 Логи", callback_data="admin_logs")
        ],
        [
            InlineKeyboardButton(text="🔄 Перезагрузка", callback_data="admin_reload"),
            InlineKeyboardButton(text="❌ Закрыть", callback_data="admin_close")
        ]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)


@router.message(Command("admin"))
async def admin_panel(message: Message):
    """
    Главная команда админ-панели.
    """
    if not is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав доступа к админ-панели.")
        return
    
    try:
        text = "🔧 <b>Админ-панель VPN бота</b>\n\n"
        text += "Добро пожаловать в панель администратора!\n"
        text += "Выберите нужное действие:"
        
        await message.answer(
            text=text,
            reply_markup=get_admin_menu_keyboard()
        )
        
    except Exception as e:
        logger.error(f"Error in admin panel: {e}")
        await message.answer("❌ Ошибка при загрузке админ-панели.")


@router.message(Command("stats"))
async def admin_stats_command(message: Message):
    """
    Команда для получения статистики.
    """
    if not is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав доступа к статистике.")
        return
    
    try:
        # Получаем сервисы
        container = get_service_container()
        user_service = container.get_service(UserService)
        
        # Получаем статистику пользователей
        user_stats = await user_service.get_user_stats()
        
        text = "📊 <b>Статистика бота</b>\n\n"
        text += f"👥 Всего пользователей: {user_stats.get('total_users', 0)}\n"
        text += f"✅ Активных пользователей: {user_stats.get('active_users', 0)}\n"
        
        # Добавляем информацию о системе
        import psutil
        import datetime
        
        # Информация о памяти
        memory = psutil.virtual_memory()
        text += f"\n💾 <b>Система</b>\n"
        text += f"RAM: {memory.percent}% ({memory.used // 1024 // 1024} MB / {memory.total // 1024 // 1024} MB)\n"
        
        # Время работы
        text += f"⏰ Время: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        await message.answer(text)
        
    except Exception as e:
        logger.error(f"Error getting admin stats: {e}")
        await message.answer("❌ Ошибка при получении статистики.")


@router.message(Command("users"))
async def admin_users_command(message: Message):
    """
    Команда для управления пользователями.
    """
    if not is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав доступа к управлению пользователями.")
        return
    
    try:
        # Получаем сервисы
        container = get_service_container()
        user_service = container.get_service(UserService)
        
        # Получаем статистику
        stats = await user_service.get_user_stats()
        
        text = "👥 <b>Управление пользователями</b>\n\n"
        text += f"Всего пользователей: {stats.get('total_users', 0)}\n"
        text += f"Активных: {stats.get('active_users', 0)}\n\n"
        text += "Для получения подробной информации о пользователе используйте:\n"
        text += "<code>/user_info [telegram_id]</code>"
        
        await message.answer(text)
        
    except Exception as e:
        logger.error(f"Error in admin users command: {e}")
        await message.answer("❌ Ошибка при получении информации о пользователях.")


@router.message(Command("user_info"))
async def admin_user_info_command(message: Message):
    """
    Команда для получения информации о конкретном пользователе.
    """
    if not is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав доступа.")
        return
    
    try:
        # Извлекаем telegram_id из команды
        command_parts = message.text.split()
        if len(command_parts) < 2:
            await message.answer("❌ Укажите Telegram ID пользователя.\nПример: <code>/user_info 123456789</code>")
            return
        
        try:
            telegram_id = int(command_parts[1])
        except ValueError:
            await message.answer("❌ Неверный формат Telegram ID.")
            return
        
        # Получаем сервисы
        container = get_service_container()
        user_service = container.get_service(UserService)
        subscription_service = container.get_service(SubscriptionService)
        
        # Получаем информацию о пользователе
        user_info = await user_service.get_user_creation_info(telegram_id)
        
        if not user_info:
            await message.answer(f"❌ Пользователь с ID {telegram_id} не найден.")
            return
        
        # Получаем информацию о подписке
        subscription_status = await subscription_service.check_subscription_status(telegram_id)
        
        text = f"👤 <b>Информация о пользователе {telegram_id}</b>\n\n"
        text += f"🆔 Telegram ID: {user_info['telegram_id']}\n"
        text += f"🔑 VPN ID: {user_info['vpn_id']}\n"
        text += f"📅 Дата создания: {user_info.get('created_at', 'Неизвестно')}\n"
        text += f"🧪 Тестовая подписка: {'Использована' if user_info['test_subscription_used'] else 'Не использована'}\n"
        
        if user_info.get('expire_date'):
            text += f"⏰ Дата истечения: {user_info['expire_date']}\n"
        
        text += f"\n📊 <b>Статус подписки</b>\n"
        text += f"Статус: {subscription_status.get('status', 'Неизвестно')}\n"
        text += f"Сообщение: {subscription_status.get('message', 'Нет данных')}\n"
        
        await message.answer(text)
        
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        await message.answer("❌ Ошибка при получении информации о пользователе.")


@router.message(Command("test_config"))
async def admin_test_config_command(message: Message):
    """
    Команда для проверки конфигурации.
    """
    if not is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав доступа.")
        return
    
    try:
        text = "⚙️ <b>Конфигурация бота</b>\n\n"
        
        # Проверяем основные настройки
        text += f"🤖 Бот токен: {'✅ Установлен' if glv.config.get('BOT_TOKEN') else '❌ Не установлен'}\n"
        text += f"🏪 Название магазина: {glv.config.get('SHOP_NAME', 'Не установлено')}\n"
        text += f"🧪 Тестовый период: {'✅ Включен' if glv.config.get('TEST_PERIOD') else '❌ Отключен'}\n"
        text += f"⏰ Лимит тестового периода: {glv.config.get('PERIOD_LIMIT', 'Не установлен')} часов\n"
        text += f"🔧 Панель хост: {glv.config.get('PANEL_HOST', 'Не установлен')}\n"
        text += f"👤 Админ ID: {glv.config.get('ADMIN_USER_ID', 'Не установлен')}\n"
        
        await message.answer(text)
        
    except Exception as e:
        logger.error(f"Error checking config: {e}")
        await message.answer("❌ Ошибка при проверке конфигурации.")


def register_admin_commands(dp):
    """Регистрирует административные команды."""
    dp.include_router(router)
