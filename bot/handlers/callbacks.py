from datetime import datetime, timedelta

from aiogram import Router, F
from aiogram import Dispatcher
from aiogram.types import CallbackQuery, LabeledPrice
from aiogram.utils.i18n import gettext as _

from keyboards import get_payment_keyboard, get_pay_keyboard, get_xtr_pay_keyboard
from services.base import get_service_container
from services.payment_service import PaymentService
from schemas.user_input_simple import parse_callback_data, ValidationError, PaymentCallbackData
from utils.logging_config import get_logger

logger = get_logger(__name__)

router = Router(name="callbacks-router") 

@router.callback_query(F.data.startswith("pay_kassa_"))
async def callback_yookassa_payment(callback: CallbackQuery):
    try:
        await callback.message.delete()

        # Валидируем callback данные
        try:
            validated_callback = parse_callback_data(callback.data)
            if not isinstance(validated_callback, PaymentCallbackData):
                raise ValidationError("Invalid payment callback format")
        except ValidationError as e:
            logger.warning(f"Invalid YooKassa callback data: {e}", extra={
                "user_id": callback.from_user.id,
                "callback_data": callback.data,
                "event_type": "yookassa_callback_validation_error"
            })
            await callback.answer("❌ Некорректный запрос", show_alert=True)
            return

        product_callback = validated_callback.product_callback

        # Получаем PaymentService
        container = get_service_container()
        payment_service = container.get_service(PaymentService)

        # Проверяем существование товара
        is_valid = await payment_service.validate_product(product_callback)
        if not is_valid:
            logger.warning(f"Product not found: {product_callback}", extra={
                "user_id": callback.from_user.id,
                "product_callback": product_callback,
                "event_type": "product_not_found"
            })
            await callback.answer("❌ Товар не найден", show_alert=True)
            return

        # Создаем платеж через PaymentService
        result = await payment_service.create_yookassa_payment(
            callback.from_user.id,
            product_callback,
            callback.message.chat.id,
            callback.from_user.language_code
        )

        if result:
            await callback.message.answer(
                _("To be paid - {amount}₽ ⬇️").format(
                    amount=int(result['amount'])
                ),
                reply_markup=get_pay_keyboard(result['url'])
            )
        else:
            await callback.answer("❌ Ошибка создания платежа", show_alert=True)
            return

        logger.info(f"YooKassa payment created", extra={
            "user_id": callback.from_user.id,
            "product_callback": product_callback,
            "amount": result['amount'],
            "event_type": "yookassa_payment_created"
        })

        await callback.answer()

    except Exception as e:
        logger.error(f"Error in YooKassa payment handler: {e}", exc_info=True, extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "event_type": "yookassa_payment_error"
        })
        await callback.answer("❌ Произошла ошибка. Попробуйте позже.", show_alert=True)


@router.callback_query(F.data.startswith("pay_stars_"))
async def callback_telegram_stars_payment(callback: CallbackQuery):
    try:
        await callback.message.delete()

        # Валидируем callback данные
        try:
            validated_callback = parse_callback_data(callback.data)
            if not isinstance(validated_callback, PaymentCallbackData):
                raise ValidationError("Invalid payment callback format")
        except ValidationError as e:
            logger.warning(f"Invalid Telegram Stars callback data: {e}", extra={
                "user_id": callback.from_user.id,
                "callback_data": callback.data,
                "event_type": "telegram_stars_callback_validation_error"
            })
            await callback.answer("❌ Некорректный запрос", show_alert=True)
            return

        product_callback = validated_callback.product_callback

        # Проверяем существование товара
        if product_callback not in goods.get_callbacks():
            logger.warning(f"Product not found: {product_callback}", extra={
                "user_id": callback.from_user.id,
                "product_callback": product_callback,
                "event_type": "product_not_found"
            })
            await callback.answer("❌ Товар не найден", show_alert=True)
            return

        good = goods.get(product_callback)
        if not good or 'price' not in good or 'stars' not in good['price']:
            logger.error(f"Invalid product data: {good}", extra={
                "user_id": callback.from_user.id,
                "product_callback": product_callback,
                "event_type": "invalid_product_data"
            })
            await callback.answer("❌ Ошибка в данных товара", show_alert=True)
            return

        price = good['price']['stars']
        months = good['months']

        # Валидируем цену
        if not isinstance(price, int) or price <= 0 or price > 2500:
            logger.error(f"Invalid stars price: {price}", extra={
                "user_id": callback.from_user.id,
                "product_callback": product_callback,
                "price": price,
                "event_type": "invalid_stars_price"
            })
            await callback.answer("❌ Некорректная цена", show_alert=True)
            return

        prices = [LabeledPrice(label="XTR", amount=price)]

        await callback.message.answer_invoice(
            title=_("Subscription for {amount} month").format(amount=months),
            currency="XTR",
            description=_("To be paid - {amount}⭐️ ⬇️").format(amount=int(price)),
            prices=prices,
            provider_token="",
            payload=product_callback,
            reply_markup=get_xtr_pay_keyboard(price)
        )

        logger.info(f"Telegram Stars payment created", extra={
            "user_id": callback.from_user.id,
            "product_callback": product_callback,
            "amount": price,
            "event_type": "telegram_stars_payment_created"
        })

        await callback.answer()

    except Exception as e:
        logger.error(f"Error in Telegram Stars payment handler: {e}", exc_info=True, extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "event_type": "telegram_stars_payment_error"
        })
        await callback.answer("❌ Произошла ошибка. Попробуйте позже.", show_alert=True)

@router.callback_query(F.data.startswith("pay_crypto_"))
async def callback_cryptomus_payment(callback: CallbackQuery):
    try:
        await callback.message.delete()

        # Валидируем callback данные
        try:
            validated_callback = parse_callback_data(callback.data)
            if not isinstance(validated_callback, PaymentCallbackData):
                raise ValidationError("Invalid payment callback format")
        except ValidationError as e:
            logger.warning(f"Invalid Cryptomus callback data: {e}", extra={
                "user_id": callback.from_user.id,
                "callback_data": callback.data,
                "event_type": "cryptomus_callback_validation_error"
            })
            await callback.answer("❌ Некорректный запрос", show_alert=True)
            return

        product_callback = validated_callback.product_callback

        # Проверяем существование товара
        if product_callback not in goods.get_callbacks():
            logger.warning(f"Product not found: {product_callback}", extra={
                "user_id": callback.from_user.id,
                "product_callback": product_callback,
                "event_type": "product_not_found"
            })
            await callback.answer("❌ Товар не найден", show_alert=True)
            return

        # Создаем платеж
        result = await cryptomus.create_payment(
            callback.from_user.id,
            product_callback,
            callback.message.chat.id,
            callback.from_user.language_code
        )

        now = datetime.now()
        expire_date = (now + timedelta(minutes=60)).strftime("%d/%m/%Y, %H:%M")

        await callback.message.answer(
            _("To be paid - {amount}$ ⬇️").format(
                amount=result['amount'],
                date=expire_date
            ),
            reply_markup=get_pay_keyboard(result['url'])
        )

        logger.info(f"Cryptomus payment created", extra={
            "user_id": callback.from_user.id,
            "product_callback": product_callback,
            "amount": result['amount'],
            "event_type": "cryptomus_payment_created"
        })

        await callback.answer()

    except Exception as e:
        logger.error(f"Error in Cryptomus payment handler: {e}", exc_info=True, extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "event_type": "cryptomus_payment_error"
        })
        await callback.answer("❌ Произошла ошибка. Попробуйте позже.", show_alert=True)

@router.callback_query(lambda c: c.data in goods.get_callbacks())
async def callback_product_select(callback: CallbackQuery):
    try:
        await callback.message.delete()

        # Валидируем callback данные
        if not callback.data or callback.data not in goods.get_callbacks():
            logger.warning(f"Invalid product callback: {callback.data}", extra={
                "user_id": callback.from_user.id,
                "callback_data": callback.data,
                "event_type": "invalid_product_callback"
            })
            await callback.answer("❌ Товар не найден", show_alert=True)
            return

        good = goods.get(callback.data)
        if not good:
            logger.error(f"Product data not found: {callback.data}", extra={
                "user_id": callback.from_user.id,
                "callback_data": callback.data,
                "event_type": "product_data_not_found"
            })
            await callback.answer("❌ Ошибка в данных товара", show_alert=True)
            return

        await callback.message.answer(
            text=_("Select payment method ⬇️"),
            reply_markup=get_payment_keyboard(good)
        )

        logger.info(f"Product selected", extra={
            "user_id": callback.from_user.id,
            "product_callback": callback.data,
            "event_type": "product_selected"
        })

        await callback.answer()

    except Exception as e:
        logger.error(f"Error in product selection handler: {e}", exc_info=True, extra={
            "user_id": callback.from_user.id,
            "callback_data": callback.data,
            "event_type": "product_selection_error"
        })
        await callback.answer("❌ Произошла ошибка. Попробуйте позже.", show_alert=True)

def register_callbacks(dp: Dispatcher):
    dp.include_router(router)
