from aiogram import Router, F
from aiogram import Dispatcher
from aiogram.types import Message
from aiogram.utils.i18n import gettext as _
from aiogram.utils.i18n import lazy_gettext as __

from .commands import start
from keyboards import get_buy_menu_keyboard, get_back_keyboard, get_main_menu_keyboard, get_subscription_keyboard
from services.base import get_service_container
from services.user_service import UserService
from services.subscription_service import SubscriptionService
from services.notification_service import NotificationService
import glv

router = Router(name="messages-router") 

@router.message(F.text == __("Join 🏄🏻‍♂️"))
async def buy(message: Message):
    await message.answer(_("Choose the appropriate tariff ⬇️"), reply_markup=get_buy_menu_keyboard())

@router.message(F.text == __("My subscription 👤"))
async def profile(message: Message):
    """
    Обработчик просмотра профиля пользователя.
    Использует SubscriptionService для получения информации о подписке.
    """
    try:
        # Получаем сервисы
        container = get_service_container()
        subscription_service = container.get_service(SubscriptionService)

        # Получаем информацию о подписке
        subscription_info = await subscription_service.get_user_subscription_info(message.from_user.id)

        if not subscription_info:
            await message.answer(
                _("Your profile is not active at the moment.\n️\nYou can choose \"5 days free 🆓\" or \"Join 🏄🏻‍♂️\"."),
                reply_markup=get_main_menu_keyboard(False)
            )
            return

        # Получаем URL подписки
        subscription_url = await subscription_service.get_subscription_url(message.from_user.id)

        if subscription_url:
            await message.answer(
                _("Subscription page ⬇️"),
                reply_markup=get_subscription_keyboard(subscription_url)
            )
        else:
            await message.answer(
                _("Your subscription is active but URL is not available."),
                reply_markup=get_main_menu_keyboard(True)
            )

    except Exception as e:
        # Fallback на сообщение об ошибке
        await message.answer(
            _("Error loading subscription information. Please try again later."),
            reply_markup=get_main_menu_keyboard(False)
        )

@router.message(F.text == __("Frequent questions ℹ️"))
async def information(message: Message):
    await message.answer(
        _("Follow the <a href=\"{link}\">link</a> 🔗").format(
            link=glv.config['ABOUT']),
        reply_markup=get_back_keyboard())

@router.message(F.text == __("Support ❤️"))
async def support(message: Message):
    await message.answer(
        _("Follow the <a href=\"{link}\">link</a> and ask us a question. We are always happy to help 🤗").format(
            link=glv.config['SUPPORT_LINK']),
        reply_markup=get_back_keyboard())

@router.message(F.text == __("5 days free 🆓"))
async def test_subscription(message: Message):
    """
    Обработчик создания тестовой подписки.
    Использует UserService и SubscriptionService для бизнес-логики.
    """
    try:
        # Получаем сервисы
        container = get_service_container()
        user_service = container.get_service(UserService)
        subscription_service = container.get_service(SubscriptionService)
        notification_service = container.get_service(NotificationService)

        # Устанавливаем бот для уведомлений
        notification_service.set_bot(message.bot)

        # Проверяем, использовал ли пользователь уже тестовую подписку
        has_test = await user_service.check_test_subscription_used(message.from_user.id)
        if has_test:
            await message.answer(
                _("Your subscription is available in the \"My subscription 👤\" section."),
                reply_markup=get_main_menu_keyboard(True)
            )
            return

        # Создаем тестовую подписку
        result = await subscription_service.create_test_subscription(message.from_user.id)

        if result:
            # Отправляем уведомление об успешном создании
            await notification_service.send_test_subscription_created(message.from_user.id)
        else:
            await message.answer(
                _("Error creating test subscription. Please try again later."),
                reply_markup=get_main_menu_keyboard(False)
            )

    except Exception as e:
        # Fallback на сообщение об ошибке
        await message.answer(
            _("Error creating test subscription. Please try again later."),
            reply_markup=get_main_menu_keyboard(False)
        )
    
@router.message(F.text == __("⏪ Back"))
async def start_text(message: Message):
    await start(message)

def register_messages(dp: Dispatcher):
    dp.include_router(router)
