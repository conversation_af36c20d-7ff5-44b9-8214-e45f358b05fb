"""
Обработчики для управления сохраненными платежными методами.

Этот модуль содержит обработчики для работы с сохраненными
платежными методами и настройками автопродления.
"""

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.utils.i18n import gettext as _

from services.base import get_service_container
from services.payment_method_service import PaymentMethodService
from keyboards.payment_methods import PaymentMethodKeyboards
from utils.logging_config import get_logger

logger = get_logger(__name__)

router = Router(name="payment-methods-router")


@router.callback_query(F.data == "my_payment_methods")
async def show_payment_methods(callback: CallbackQuery):
    """
    Показывает список сохраненных платежных методов пользователя.
    """
    try:
        await callback.answer()
        
        # Получаем сервис
        container = get_service_container()
        payment_method_service = container.get_service(PaymentMethodService)
        
        # Получаем платежные методы пользователя
        user_methods = await payment_method_service.get_user_payment_methods(callback.from_user.id)
        
        if not user_methods.payment_methods:
            await callback.message.edit_text(
                _("У вас пока нет сохраненных платежных методов.\n\n"
                  "Сохраненные методы оплаты позволяют:\n"
                  "• Быстро оплачивать подписки\n"
                  "• Настроить автопродление\n"
                  "• Управлять способами оплаты\n\n"
                  "Добавьте первый платежный метод при следующей покупке."),
                reply_markup=PaymentMethodKeyboards.get_empty_methods_keyboard()
            )
            return
        
        # Формируем текст с информацией о методах
        text = _("💳 Ваши сохраненные платежные методы:\n\n")
        
        for i, method in enumerate(user_methods.payment_methods, 1):
            status_emoji = "✅" if method.is_active else "❌"
            default_mark = "⭐ " if method.is_default else ""
            
            text += f"{i}. {default_mark}{status_emoji} {method.display_name}\n"
            text += f"   {method.masked_data}\n"
            if method.last_used_at:
                text += f"   Последнее использование: {method.last_used_at.strftime('%d.%m.%Y')}\n"
            text += "\n"
        
        # Добавляем информацию об автопродлении
        if user_methods.auto_renewal_settings:
            auto_renewal = user_methods.auto_renewal_settings[0]
            if auto_renewal.is_enabled:
                text += f"🔄 Автопродление: включено\n"
                text += f"📅 Продлевать за {auto_renewal.renewal_days_before} дней до истечения\n\n"
            else:
                text += "🔄 Автопродление: отключено\n\n"
        
        text += _("Выберите действие:")
        
        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_payment_methods_keyboard(
                user_methods.payment_methods,
                user_methods.auto_renewal_settings
            )
        )
        
    except Exception as e:
        logger.error(f"Error showing payment methods for user {callback.from_user.id}: {e}")
        await callback.message.edit_text(
            _("Произошла ошибка при загрузке платежных методов. Попробуйте позже."),
            reply_markup=PaymentMethodKeyboards.get_back_keyboard()
        )


@router.callback_query(F.data.startswith("payment_method_"))
async def show_payment_method_details(callback: CallbackQuery):
    """
    Показывает детали конкретного платежного метода.
    """
    try:
        await callback.answer()
        
        method_id = callback.data.split("_", 2)[2]
        
        # Получаем сервис
        container = get_service_container()
        payment_method_service = container.get_service(PaymentMethodService)
        
        # Получаем информацию о методе
        user_methods = await payment_method_service.get_user_payment_methods(callback.from_user.id)
        
        # Находим нужный метод
        selected_method = None
        for method in user_methods.payment_methods:
            if method.id == method_id:
                selected_method = method
                break
        
        if not selected_method:
            await callback.message.edit_text(
                _("Платежный метод не найден."),
                reply_markup=PaymentMethodKeyboards.get_back_keyboard()
            )
            return
        
        # Формируем детальную информацию
        text = f"💳 {selected_method.display_name}\n\n"
        
        text += f"🔢 Номер: {selected_method.masked_data}\n"
        text += f"📊 Статус: {'Активен' if selected_method.is_active else 'Неактивен'}\n"
        text += f"⭐ По умолчанию: {'Да' if selected_method.is_default else 'Нет'}\n"
        text += f"🏦 Тип: {selected_method.payment_type.value.title()}\n"
        
        if selected_method.last_used_at:
            text += f"📅 Последнее использование: {selected_method.last_used_at.strftime('%d.%m.%Y %H:%M')}\n"
        
        if selected_method.expires_at:
            text += f"⏰ Истекает: {selected_method.expires_at.strftime('%d.%m.%Y')}\n"
        
        text += f"📝 Создан: {selected_method.created_at.strftime('%d.%m.%Y %H:%M')}\n\n"
        
        text += _("Выберите действие:")
        
        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_payment_method_details_keyboard(
                selected_method.id,
                selected_method.is_default,
                selected_method.is_active
            )
        )
        
    except Exception as e:
        logger.error(f"Error showing payment method details for user {callback.from_user.id}: {e}")
        await callback.message.edit_text(
            _("Произошла ошибка при загрузке информации о платежном методе."),
            reply_markup=PaymentMethodKeyboards.get_back_keyboard()
        )


@router.callback_query(F.data.startswith("set_default_"))
async def set_default_payment_method(callback: CallbackQuery):
    """
    Устанавливает платежный метод по умолчанию.
    """
    try:
        await callback.answer()
        
        method_id = callback.data.split("_", 2)[2]
        
        # Получаем сервис
        container = get_service_container()
        payment_method_service = container.get_service(PaymentMethodService)
        
        # Обновляем метод
        from schemas.payment_methods import SavedPaymentMethodUpdate
        update_data = SavedPaymentMethodUpdate(is_default=True)
        
        result = await payment_method_service.update_payment_method(
            callback.from_user.id, method_id, update_data
        )
        
        if result.success:
            await callback.message.edit_text(
                _("✅ Платежный метод установлен по умолчанию!"),
                reply_markup=PaymentMethodKeyboards.get_success_keyboard()
            )
        else:
            await callback.message.edit_text(
                _("❌ Не удалось установить платежный метод по умолчанию: {error}").format(
                    error=result.error_message
                ),
                reply_markup=PaymentMethodKeyboards.get_back_keyboard()
            )
        
    except Exception as e:
        logger.error(f"Error setting default payment method for user {callback.from_user.id}: {e}")
        await callback.message.edit_text(
            _("Произошла ошибка при установке платежного метода по умолчанию."),
            reply_markup=PaymentMethodKeyboards.get_back_keyboard()
        )


@router.callback_query(F.data.startswith("delete_method_"))
async def confirm_delete_payment_method(callback: CallbackQuery):
    """
    Подтверждение удаления платежного метода.
    """
    try:
        await callback.answer()
        
        method_id = callback.data.split("_", 2)[2]
        
        text = _("⚠️ Вы уверены, что хотите удалить этот платежный метод?\n\n"
                "Это действие нельзя отменить.\n"
                "Если у вас настроено автопродление с этим методом, "
                "оно будет отключено.")
        
        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_delete_confirmation_keyboard(method_id)
        )
        
    except Exception as e:
        logger.error(f"Error showing delete confirmation for user {callback.from_user.id}: {e}")
        await callback.message.edit_text(
            _("Произошла ошибка."),
            reply_markup=PaymentMethodKeyboards.get_back_keyboard()
        )


@router.callback_query(F.data.startswith("confirm_delete_"))
async def delete_payment_method(callback: CallbackQuery):
    """
    Удаляет платежный метод.
    """
    try:
        await callback.answer()
        
        method_id = callback.data.split("_", 2)[2]
        
        # Получаем сервис
        container = get_service_container()
        payment_method_service = container.get_service(PaymentMethodService)
        
        # Удаляем метод
        result = await payment_method_service.delete_payment_method(
            callback.from_user.id, method_id
        )
        
        if result.success:
            await callback.message.edit_text(
                _("✅ Платежный метод успешно удален!"),
                reply_markup=PaymentMethodKeyboards.get_success_keyboard()
            )
        else:
            await callback.message.edit_text(
                _("❌ Не удалось удалить платежный метод: {error}").format(
                    error=result.error_message
                ),
                reply_markup=PaymentMethodKeyboards.get_back_keyboard()
            )
        
    except Exception as e:
        logger.error(f"Error deleting payment method for user {callback.from_user.id}: {e}")
        await callback.message.edit_text(
            _("Произошла ошибка при удалении платежного метода."),
            reply_markup=PaymentMethodKeyboards.get_back_keyboard()
        )


@router.callback_query(F.data.startswith("test_method_"))
async def test_payment_method(callback: CallbackQuery):
    """
    Тестирует платежный метод.
    """
    try:
        await callback.answer("🔄 Тестируем платежный метод...")
        
        method_id = callback.data.split("_", 2)[2]
        
        # Получаем сервис
        container = get_service_container()
        payment_method_service = container.get_service(PaymentMethodService)
        
        # Тестируем метод
        test_result = await payment_method_service.test_payment_method(
            callback.from_user.id, method_id
        )
        
        if test_result.success:
            text = f"✅ Тест прошел успешно!\n\n"
            text += f"📊 Время ответа: {test_result.response_time_ms}мс\n"
            text += f"💬 {test_result.message}"
            
            if test_result.test_transaction_id:
                text += f"\n🔢 ID теста: {test_result.test_transaction_id}"
        else:
            text = f"❌ Тест не прошел\n\n"
            text += f"💬 {test_result.message}\n"
            text += f"📊 Время ответа: {test_result.response_time_ms}мс"
        
        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_test_result_keyboard(method_id)
        )
        
    except Exception as e:
        logger.error(f"Error testing payment method for user {callback.from_user.id}: {e}")
        await callback.message.edit_text(
            _("Произошла ошибка при тестировании платежного метода."),
            reply_markup=PaymentMethodKeyboards.get_back_keyboard()
        )
