"""
Обработчики callback-кнопок для админ-панели.
"""

from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.utils.i18n import gettext as _

from services.base import get_service_container
from services.user_service import UserService
from services.subscription_service import SubscriptionService
from utils.logging_config import get_logger
import glv

logger = get_logger(__name__)
router = Router(name="admin-callbacks-router")


def is_admin(user_id: int) -> bool:
    """Проверяет, является ли пользователь администратором."""
    admin_id = glv.config.get('ADMIN_USER_ID')
    return admin_id is not None and user_id == admin_id


@router.callback_query(F.data == "admin_stats")
async def admin_stats_callback(callback: CallbackQuery):
    """Обработчик кнопки статистики."""
    if not is_admin(callback.from_user.id):
        await callback.answer("❌ Нет доступа", show_alert=True)
        return
    
    try:
        # Получаем сервисы
        container = get_service_container()
        user_service = container.get_service(UserService)
        
        # Получаем статистику
        user_stats = await user_service.get_user_stats()
        
        text = "📊 <b>Детальная статистика</b>\n\n"
        text += f"👥 Всего пользователей: {user_stats.get('total_users', 0)}\n"
        text += f"✅ Активных пользователей: {user_stats.get('active_users', 0)}\n"
        
        # Добавляем системную информацию
        import psutil
        import datetime
        
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        text += f"\n💻 <b>Система</b>\n"
        text += f"🧠 CPU: {cpu_percent}%\n"
        text += f"💾 RAM: {memory.percent}% ({memory.used // 1024 // 1024} MB)\n"
        text += f"💿 Доступно RAM: {memory.available // 1024 // 1024} MB\n"
        
        # Информация о боте
        text += f"\n🤖 <b>Бот</b>\n"
        text += f"⏰ Время: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        text += f"🔧 Тестовый период: {'Включен' if glv.config.get('TEST_PERIOD') else 'Отключен'}\n"
        
        await callback.message.edit_text(text)
        await callback.answer()
        
    except Exception as e:
        logger.error(f"Error in admin stats callback: {e}")
        await callback.answer("❌ Ошибка получения статистики", show_alert=True)


@router.callback_query(F.data == "admin_users")
async def admin_users_callback(callback: CallbackQuery):
    """Обработчик кнопки управления пользователями."""
    if not is_admin(callback.from_user.id):
        await callback.answer("❌ Нет доступа", show_alert=True)
        return
    
    try:
        # Получаем сервисы
        container = get_service_container()
        user_service = container.get_service(UserService)
        
        # Получаем статистику пользователей
        stats = await user_service.get_user_stats()
        
        text = "👥 <b>Управление пользователями</b>\n\n"
        text += f"📊 Всего пользователей: {stats.get('total_users', 0)}\n"
        text += f"✅ Активных: {stats.get('active_users', 0)}\n\n"
        
        text += "📝 <b>Доступные команды:</b>\n"
        text += "• <code>/user_info [telegram_id]</code> - информация о пользователе\n"
        text += "• <code>/stats</code> - общая статистика\n"
        text += "• <code>/users</code> - управление пользователями\n\n"
        
        text += "💡 <i>Для получения подробной информации о пользователе укажите его Telegram ID</i>"
        
        await callback.message.edit_text(text)
        await callback.answer()
        
    except Exception as e:
        logger.error(f"Error in admin users callback: {e}")
        await callback.answer("❌ Ошибка получения данных пользователей", show_alert=True)


@router.callback_query(F.data == "admin_settings")
async def admin_settings_callback(callback: CallbackQuery):
    """Обработчик кнопки настроек."""
    if not is_admin(callback.from_user.id):
        await callback.answer("❌ Нет доступа", show_alert=True)
        return
    
    try:
        text = "⚙️ <b>Настройки бота</b>\n\n"
        
        # Основные настройки
        text += "🔧 <b>Основные параметры:</b>\n"
        text += f"🏪 Название: {glv.config.get('SHOP_NAME', 'Не установлено')}\n"
        text += f"🧪 Тестовый период: {'✅ Включен' if glv.config.get('TEST_PERIOD') else '❌ Отключен'}\n"
        text += f"⏰ Лимит тестового периода: {glv.config.get('PERIOD_LIMIT', 'Не установлен')} ч.\n"
        
        # Панель управления
        text += f"\n🖥️ <b>Панель Marzban:</b>\n"
        text += f"🌐 Хост: {glv.config.get('PANEL_HOST', 'Не установлен')}\n"
        text += f"👤 Пользователь: {glv.config.get('PANEL_USER', 'Не установлен')}\n"
        text += f"🔑 Пароль: {'✅ Установлен' if glv.config.get('PANEL_PASS') else '❌ Не установлен'}\n"
        
        # Платежи
        text += f"\n💳 <b>Платежные системы:</b>\n"
        text += f"💰 YooKassa: {'✅ Настроена' if glv.config.get('YOOKASSA_TOKEN') else '❌ Не настроена'}\n"
        text += f"⭐ Telegram Stars: {'✅ Включены' if glv.config.get('STARS_PAYMENT_ENABLED') else '❌ Отключены'}\n"
        
        # Администрирование
        text += f"\n👑 <b>Администрирование:</b>\n"
        text += f"👤 Админ ID: {glv.config.get('ADMIN_USER_ID', 'Не установлен')}\n"
        
        await callback.message.edit_text(text)
        await callback.answer()
        
    except Exception as e:
        logger.error(f"Error in admin settings callback: {e}")
        await callback.answer("❌ Ошибка получения настроек", show_alert=True)


@router.callback_query(F.data == "admin_logs")
async def admin_logs_callback(callback: CallbackQuery):
    """Обработчик кнопки логов."""
    if not is_admin(callback.from_user.id):
        await callback.answer("❌ Нет доступа", show_alert=True)
        return
    
    try:
        text = "📝 <b>Системные логи</b>\n\n"
        text += "🔍 <b>Доступные команды для просмотра логов:</b>\n\n"
        text += "• <code>/logs error</code> - ошибки за последний час\n"
        text += "• <code>/logs warning</code> - предупреждения за последний час\n"
        text += "• <code>/logs info</code> - информационные сообщения\n"
        text += "• <code>/health</code> - проверка состояния системы\n\n"
        
        text += "💡 <i>Логи помогают отслеживать работу бота и выявлять проблемы</i>\n\n"
        
        # Добавляем краткую информацию о состоянии
        text += "📊 <b>Текущее состояние:</b>\n"
        
        # Проверяем доступность основных компонентов
        try:
            container = get_service_container()
            user_service = container.get_service(UserService)
            text += "✅ UserService: Работает\n"
        except:
            text += "❌ UserService: Ошибка\n"
        
        try:
            from utils import marzban_api
            text += "✅ Marzban API: Доступен\n"
        except:
            text += "❌ Marzban API: Недоступен\n"
        
        await callback.message.edit_text(text)
        await callback.answer()
        
    except Exception as e:
        logger.error(f"Error in admin logs callback: {e}")
        await callback.answer("❌ Ошибка получения логов", show_alert=True)


@router.callback_query(F.data == "admin_reload")
async def admin_reload_callback(callback: CallbackQuery):
    """Обработчик кнопки перезагрузки."""
    if not is_admin(callback.from_user.id):
        await callback.answer("❌ Нет доступа", show_alert=True)
        return
    
    try:
        text = "🔄 <b>Перезагрузка системы</b>\n\n"
        text += "⚠️ <b>Внимание!</b> Перезагрузка может временно прервать работу бота.\n\n"
        text += "🔧 <b>Доступные операции:</b>\n"
        text += "• <code>/reload_services</code> - перезагрузка сервисов\n"
        text += "• <code>/reload_config</code> - перезагрузка конфигурации\n"
        text += "• <code>/restart_bot</code> - полная перезагрузка бота\n\n"
        
        text += "💡 <i>Используйте эти команды только при необходимости</i>"
        
        await callback.message.edit_text(text)
        await callback.answer()
        
    except Exception as e:
        logger.error(f"Error in admin reload callback: {e}")
        await callback.answer("❌ Ошибка", show_alert=True)


@router.callback_query(F.data == "admin_close")
async def admin_close_callback(callback: CallbackQuery):
    """Обработчик кнопки закрытия админ-панели."""
    if not is_admin(callback.from_user.id):
        await callback.answer("❌ Нет доступа", show_alert=True)
        return
    
    try:
        await callback.message.delete()
        await callback.answer("Админ-панель закрыта")
        
    except Exception as e:
        logger.error(f"Error closing admin panel: {e}")
        await callback.answer("❌ Ошибка закрытия панели", show_alert=True)


def register_admin_callbacks(dp):
    """Регистрирует обработчики админ-панели."""
    dp.include_router(router)
