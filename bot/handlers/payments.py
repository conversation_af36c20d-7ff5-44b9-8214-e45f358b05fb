from aiogram import Router, Dispatcher, F
from aiogram.types import Message, PreCheckoutQuery
from aiogram.utils.i18n import gettext as _

from services.base import get_service_container
from services.payment_service import PaymentService
from services.subscription_service import SubscriptionService
from services.notification_service import NotificationService
from keyboards import get_main_menu_keyboard

import glv

router = Router(name="payment-router")

@router.pre_checkout_query()
async def pre_checkout_handler(query: PreCheckoutQuery):
    """
    Обработчик предварительной проверки платежа.
    Использует PaymentService для валидации товара.
    """
    try:
        # Получаем PaymentService
        container = get_service_container()
        payment_service = container.get_service(PaymentService)

        # Валидируем товар
        is_valid = await payment_service.validate_product(query.invoice_payload)

        if not is_valid:
            return await query.answer(
                _("Error: Invalid product type.\nPlease contact the support team."),
                ok=False
            )

        await query.answer(ok=True)

    except Exception as e:
        # Fallback на ошибку
        return await query.answer(
            _("Error: Invalid product type.\nPlease contact the support team."),
            ok=False
        )

@router.message(F.successful_payment)
async def success_payment(message: Message):
    """
    Обработчик успешного платежа.
    Использует PaymentService, SubscriptionService и NotificationService.
    """
    try:
        # Получаем сервисы
        container = get_service_container()
        payment_service = container.get_service(PaymentService)
        subscription_service = container.get_service(SubscriptionService)
        notification_service = container.get_service(NotificationService)

        # Устанавливаем бот для уведомлений
        notification_service.set_bot(message.bot)

        # Получаем информацию о товаре
        product_callback = message.successful_payment.invoice_payload
        product = await payment_service.get_product_info(product_callback)

        if not product:
            await message.answer(
                _("Error processing payment. Please contact support."),
                reply_markup=get_main_menu_keyboard(True)
            )
            return

        # Создаем платную подписку
        result = await subscription_service.create_paid_subscription(
            message.from_user.id,
            product
        )

        if result:
            # Отправляем уведомление об успешном платеже
            await notification_service.send_paid_subscription_created(message.from_user.id)
        else:
            await message.answer(
                _("Error creating subscription. Please contact support."),
                reply_markup=get_main_menu_keyboard(True)
            )

    except Exception as e:
        # Fallback на сообщение об ошибке
        await message.answer(
            _("Error processing payment. Please contact support."),
            reply_markup=get_main_menu_keyboard(True)
        )
    
def register_payments(dp: Dispatcher):
    dp.include_router(router)