from aiogram import Router
from aiogram import Dispatcher
from aiogram.filters import Command
from aiogram.types import Message
from aiogram.utils.i18n import gettext as _
from aiogram.utils.i18n import lazy_gettext as __

from keyboards import get_main_menu_keyboard
from keyboards.country_selection import CountrySelectionKeyboards
from services.base import get_service_container
from services.user_service import UserService
from services.notification_service import NotificationService
import glv

router = Router(name="commands-router")

@router.message(
    Command("start")
)
async def start(message: Message):
    """
    Обработчик команды /start.
    Использует UserService и NotificationService для бизнес-логики.
    """
    try:
        # Получаем сервисы из контейнера
        container = get_service_container()
        user_service = container.get_service(UserService)
        notification_service = container.get_service(NotificationService)

        # Устанавливаем бот для уведомлений
        notification_service.set_bot(message.bot)

        # Обеспечиваем существование пользователя
        await user_service.ensure_user_exists(message.from_user.id)

        # Проверяем тестовую подписку
        has_test_subscription = await user_service.check_test_subscription_used(message.from_user.id)

        # Отправляем приветственное сообщение через NotificationService
        await notification_service.send_welcome_message(
            message.from_user.id,
            message.from_user.first_name or "User",
            has_test_subscription
        )

    except Exception as e:
        # Fallback на старую логику в случае ошибки
        text = _("Hello, {name} 👋🏻\n\nSelect an action ⬇️").format(
            name=message.from_user.first_name,
            title=glv.config.get('SHOP_NAME', 'VPN Shop')
        )
        await message.answer(text, reply_markup=get_main_menu_keyboard(False))


@router.message(Command("countries"))
async def countries_command(message: Message):
    """
    Обработчик команды /countries.
    Показывает меню выбора стран и нод.
    """
    try:
        keyboards = CountrySelectionKeyboards()

        text = "🌍 <b>Выбор страны и ноды</b>\n\n"
        text += "Добро пожаловать в систему выбора стран!\n\n"
        text += "Здесь вы можете:\n"
        text += "• 🌍 Выбрать предпочитаемые страны\n"
        text += "• ⚡ Позволить системе автоматически выбрать лучший сервер\n"
        text += "• ⚙️ Настроить свои предпочтения\n"
        text += "• 📊 Посмотреть статистику нод\n\n"
        text += "Выберите действие:"

        await message.answer(
            text=text,
            reply_markup=keyboards.get_countries_menu_keyboard()
        )

    except Exception as e:
        await message.answer(
            "❌ Ошибка при загрузке меню выбора стран. Попробуйте позже."
        )


def register_commands(dp: Dispatcher):
    dp.include_router(router)
