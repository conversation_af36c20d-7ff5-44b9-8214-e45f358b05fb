"""
NodeMonitoringService - сервис для мониторинга состояния нод.
Периодически проверяет доступность нод, собирает статистику и обновляет БД.
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from repositories.country_node_repository import (
    MarzbanNodeRepository, NodeStatisticsRepository
)
from db.models_countries_nodes import MarzbanNode, NodeStatus, NodeStatistics
from utils.marzban_api import MarzbanAPI
from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class NodeHealthCheck:
    """Результат проверки здоровья ноды."""
    node_id: str
    is_online: bool
    response_time_ms: float
    cpu_usage: float
    memory_usage: float
    users_count: int
    error_message: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class MonitoringStats:
    """Статистика мониторинга."""
    total_nodes: int
    online_nodes: int
    offline_nodes: int
    average_response_time: float
    last_check_time: datetime
    errors_count: int


class NodeMonitoringService:
    """Сервис для мониторинга состояния нод."""
    
    def __init__(self,
                 node_repo: MarzbanNodeRepository,
                 stats_repo: NodeStatisticsRepository,
                 marzban_api: MarzbanAPI):
        self.node_repo = node_repo
        self.stats_repo = stats_repo
        self.marzban_api = marzban_api
        
        # Настройки мониторинга
        self.check_interval = 300  # 5 минут
        self.timeout_threshold = 5000  # 5 секунд
        self.max_retries = 3
        
        # Состояние мониторинга
        self._monitoring_task: Optional[asyncio.Task] = None
        self._is_running = False
        self._last_check_time: Optional[datetime] = None
        self._monitoring_stats = MonitoringStats(0, 0, 0, 0.0, datetime.utcnow(), 0)
    
    async def start_monitoring(self):
        """Запустить мониторинг нод."""
        if self._is_running:
            logger.warning("Node monitoring is already running")
            return
        
        self._is_running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Node monitoring started")
    
    async def stop_monitoring(self):
        """Остановить мониторинг нод."""
        if not self._is_running:
            logger.warning("Node monitoring is not running")
            return
        
        self._is_running = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Node monitoring stopped")
    
    async def check_all_nodes(self) -> List[NodeHealthCheck]:
        """Проверить все ноды."""
        try:
            # Получаем все активные ноды
            nodes = await self.node_repo.get_all()
            
            if not nodes:
                logger.warning("No nodes found for monitoring")
                return []
            
            logger.info(f"Starting health check for {len(nodes)} nodes")
            
            # Проверяем ноды параллельно
            tasks = []
            for node in nodes:
                task = asyncio.create_task(self._check_single_node(node))
                tasks.append(task)
            
            # Ждем завершения всех проверок
            health_checks = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Фильтруем успешные результаты
            valid_checks = []
            errors_count = 0
            
            for i, result in enumerate(health_checks):
                if isinstance(result, Exception):
                    logger.error(f"Error checking node {nodes[i].id}: {result}")
                    errors_count += 1
                elif isinstance(result, NodeHealthCheck):
                    valid_checks.append(result)
            
            # Обновляем статистику мониторинга
            self._update_monitoring_stats(valid_checks, errors_count)
            
            logger.info(f"Completed health check: {len(valid_checks)} nodes checked, {errors_count} errors")
            
            return valid_checks
            
        except Exception as e:
            logger.error(f"Error during nodes health check: {e}")
            return []
    
    async def check_single_node_by_id(self, node_id: str) -> Optional[NodeHealthCheck]:
        """Проверить конкретную ноду по ID."""
        try:
            node = await self.node_repo.get_by_id(node_id)
            if not node:
                logger.warning(f"Node {node_id} not found")
                return None
            
            return await self._check_single_node(node)
            
        except Exception as e:
            logger.error(f"Error checking node {node_id}: {e}")
            return None
    
    async def get_monitoring_stats(self) -> MonitoringStats:
        """Получить статистику мониторинга."""
        return self._monitoring_stats
    
    async def get_node_uptime_stats(self, node_id: str, hours: int = 24) -> Dict[str, Any]:
        """Получить статистику uptime ноды за период."""
        try:
            # Получаем статистику за период
            stats = await self.stats_repo.get_recent_stats(node_id, hours)
            
            if not stats:
                return {
                    'uptime_percentage': 0.0,
                    'total_checks': 0,
                    'online_checks': 0,
                    'average_response_time': 0.0
                }
            
            total_checks = len(stats)
            online_checks = sum(1 for stat in stats if stat.is_online)
            uptime_percentage = (online_checks / total_checks * 100) if total_checks > 0 else 0.0
            
            # Средний response time только для онлайн проверок
            online_stats = [stat for stat in stats if stat.is_online and stat.response_time_ms]
            avg_response_time = (
                sum(stat.response_time_ms for stat in online_stats) / len(online_stats)
                if online_stats else 0.0
            )
            
            return {
                'uptime_percentage': uptime_percentage,
                'total_checks': total_checks,
                'online_checks': online_checks,
                'average_response_time': avg_response_time,
                'period_hours': hours
            }
            
        except Exception as e:
            logger.error(f"Error getting uptime stats for node {node_id}: {e}")
            return {
                'uptime_percentage': 0.0,
                'total_checks': 0,
                'online_checks': 0,
                'average_response_time': 0.0
            }
    
    async def _monitoring_loop(self):
        """Основной цикл мониторинга."""
        logger.info(f"Starting monitoring loop with interval {self.check_interval} seconds")
        
        while self._is_running:
            try:
                start_time = datetime.utcnow()
                
                # Проверяем все ноды
                health_checks = await self.check_all_nodes()
                
                # Сохраняем результаты в БД
                await self._save_health_checks(health_checks)
                
                # Обновляем статус нод
                await self._update_nodes_status(health_checks)
                
                duration = (datetime.utcnow() - start_time).total_seconds()
                logger.debug(f"Monitoring cycle completed in {duration:.2f} seconds")
                
                # Ждем до следующей проверки
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                logger.info("Monitoring loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                # Ждем меньше времени при ошибке
                await asyncio.sleep(min(60, self.check_interval))
    
    async def _check_single_node(self, node: MarzbanNode) -> NodeHealthCheck:
        """Проверить одну ноду."""
        start_time = datetime.utcnow()
        
        try:
            # Пытаемся получить статус ноды через Marzban API
            node_status = await self.marzban_api.get_node_status(node.id)
            
            if node_status:
                # Получаем статистику ноды
                node_stats = await self.marzban_api.get_node_stats(node.id)
                
                response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
                
                # Извлекаем данные из ответа API
                cpu_usage = node_stats.get('cpu_usage', 0.0) if node_stats else 0.0
                memory_usage = node_stats.get('memory_usage', 0.0) if node_stats else 0.0
                users_count = node_stats.get('users_count', node.current_users) if node_stats else node.current_users
                
                return NodeHealthCheck(
                    node_id=node.id,
                    is_online=True,
                    response_time_ms=response_time,
                    cpu_usage=cpu_usage,
                    memory_usage=memory_usage,
                    users_count=users_count,
                    timestamp=datetime.utcnow()
                )
            else:
                # Нода недоступна
                response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
                
                return NodeHealthCheck(
                    node_id=node.id,
                    is_online=False,
                    response_time_ms=response_time,
                    cpu_usage=0.0,
                    memory_usage=0.0,
                    users_count=0,
                    error_message="Node status unavailable",
                    timestamp=datetime.utcnow()
                )
                
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            return NodeHealthCheck(
                node_id=node.id,
                is_online=False,
                response_time_ms=response_time,
                cpu_usage=0.0,
                memory_usage=0.0,
                users_count=0,
                error_message=str(e),
                timestamp=datetime.utcnow()
            )
    
    async def _save_health_checks(self, health_checks: List[NodeHealthCheck]):
        """Сохранить результаты проверок в БД."""
        try:
            for check in health_checks:
                stats_data = {
                    'users_count': check.users_count,
                    'cpu_usage': check.cpu_usage,
                    'memory_usage': check.memory_usage,
                    'response_time_ms': check.response_time_ms,
                    'is_online': check.is_online,
                    'timestamp': check.timestamp
                }
                
                await self.stats_repo.add_statistics(check.node_id, stats_data)
            
            logger.debug(f"Saved {len(health_checks)} health check results to database")
            
        except Exception as e:
            logger.error(f"Error saving health checks to database: {e}")
    
    async def _update_nodes_status(self, health_checks: List[NodeHealthCheck]):
        """Обновить статус нод в БД."""
        try:
            for check in health_checks:
                # Определяем новый статус
                if check.is_online:
                    new_status = NodeStatus.CONNECTED
                else:
                    new_status = NodeStatus.DISCONNECTED
                
                # Обновляем ноду
                update_data = {
                    'status': new_status,
                    'current_users': check.users_count,
                    'cpu_usage': check.cpu_usage,
                    'memory_usage': check.memory_usage,
                    'last_check': check.timestamp
                }
                
                await self.node_repo.update(check.node_id, **update_data)
            
            logger.debug(f"Updated status for {len(health_checks)} nodes")
            
        except Exception as e:
            logger.error(f"Error updating nodes status: {e}")
    
    def _update_monitoring_stats(self, health_checks: List[NodeHealthCheck], errors_count: int):
        """Обновить статистику мониторинга."""
        online_nodes = sum(1 for check in health_checks if check.is_online)
        offline_nodes = len(health_checks) - online_nodes
        
        # Средний response time только для онлайн нод
        online_checks = [check for check in health_checks if check.is_online]
        avg_response_time = (
            sum(check.response_time_ms for check in online_checks) / len(online_checks)
            if online_checks else 0.0
        )
        
        self._monitoring_stats = MonitoringStats(
            total_nodes=len(health_checks),
            online_nodes=online_nodes,
            offline_nodes=offline_nodes,
            average_response_time=avg_response_time,
            last_check_time=datetime.utcnow(),
            errors_count=errors_count
        )
        
        self._last_check_time = datetime.utcnow()
    
    @property
    def is_running(self) -> bool:
        """Проверить, запущен ли мониторинг."""
        return self._is_running
    
    @property
    def last_check_time(self) -> Optional[datetime]:
        """Время последней проверки."""
        return self._last_check_time
