"""
Сервис автоматического продления подписок.

Этот модуль содержит бизнес-логику для автоматического
продления подписок с использованием сохраненных платежных методов.
"""

import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from repositories.payment_method_repository import PaymentMethodRepositoryInterface
from repositories.subscription_repository import SubscriptionRepositoryInterface
from services.payment_service import PaymentService
from services.notification_service import NotificationService
from db.models_payment_methods import (
    AutoRenewalQueue, PaymentMethodUsageLog,
    AutoRenewalStatus, PaymentMethodStatus
)
from utils.encryption import decrypt_data
from utils.logging_config import get_logger

logger = get_logger(__name__)

# Импортируем generate_id в начале файла
try:
    from utils.id_generator import generate_id
except ImportError:
    # Fallback если модуль не найден
    import uuid
    def generate_id():
        return str(uuid.uuid4()).replace("-", "")[:32]


@dataclass
class AutoRenewalResult:
    """Результат автоматического продления."""
    success: bool
    subscription_id: Optional[str] = None
    error_message: Optional[str] = None
    transaction_id: Optional[str] = None
    retry_needed: bool = False


class AutoRenewalService:
    """Сервис автоматического продления подписок."""
    
    def __init__(
        self,
        payment_method_repo: PaymentMethodRepositoryInterface,
        subscription_repo: SubscriptionRepositoryInterface,
        payment_service: PaymentService,
        notification_service: NotificationService
    ):
        self.payment_method_repo = payment_method_repo
        self.subscription_repo = subscription_repo
        self.payment_service = payment_service
        self.notification_service = notification_service
    
    async def process_pending_renewals(self, limit: int = 50) -> Dict[str, int]:
        """
        Обрабатывает ожидающие автопродления.
        
        Args:
            limit: Максимальное количество задач для обработки
            
        Returns:
            Статистика обработки
        """
        stats = {
            "processed": 0,
            "successful": 0,
            "failed": 0,
            "retries": 0
        }
        
        try:
            # Получаем ожидающие задачи
            pending_renewals = await self.payment_method_repo.get_pending_auto_renewals(limit)
            
            for renewal_task in pending_renewals:
                stats["processed"] += 1
                
                # Обновляем статус на "processing"
                await self.payment_method_repo.update_auto_renewal_queue_status(
                    renewal_task.id, "processing"
                )
                
                # Обрабатываем задачу
                result = await self._process_single_renewal(renewal_task)
                
                if result.success:
                    stats["successful"] += 1
                    await self.payment_method_repo.update_auto_renewal_queue_status(
                        renewal_task.id, "completed", "success",
                        new_subscription_id=result.subscription_id
                    )
                    
                    # Отправляем уведомление об успешном продлении
                    await self._send_success_notification(renewal_task, result)
                    
                elif result.retry_needed and renewal_task.attempts < renewal_task.max_attempts:
                    stats["retries"] += 1
                    # Планируем повторную попытку через час
                    next_attempt = datetime.utcnow() + timedelta(hours=1)
                    await self.payment_method_repo.update_auto_renewal_queue_status(
                        renewal_task.id, "pending", error_message=result.error_message
                    )
                    # Обновляем время следующей попытки
                    await self.payment_method_repo.update_auto_renewal_queue_status(
                        renewal_task.id, "pending"
                    )
                    
                else:
                    stats["failed"] += 1
                    await self.payment_method_repo.update_auto_renewal_queue_status(
                        renewal_task.id, "failed", "failure",
                        error_message=result.error_message
                    )
                    
                    # Отправляем уведомление о неудаче
                    await self._send_failure_notification(renewal_task, result)
            
            logger.info(f"Processed auto renewals: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error processing pending renewals: {e}")
            return stats
    
    async def schedule_auto_renewal(self, user_id: int, subscription_id: str,
                                  expires_at: datetime) -> bool:
        """
        Планирует автоматическое продление подписки.
        
        Args:
            user_id: ID пользователя
            subscription_id: ID подписки
            expires_at: Дата истечения подписки
            
        Returns:
            True если задача успешно запланирована
        """
        try:
            # Получаем настройки автопродления пользователя
            auto_renewal_settings = await self.payment_method_repo.get_auto_renewal_settings(user_id)
            
            if not auto_renewal_settings or not auto_renewal_settings.is_enabled:
                logger.info(f"Auto renewal not enabled for user {user_id}")
                return False
            
            # Вычисляем время планирования
            scheduled_at = expires_at - timedelta(days=auto_renewal_settings.renewal_days_before)
            
            # Если время уже прошло, планируем на сейчас
            if scheduled_at <= datetime.utcnow():
                scheduled_at = datetime.utcnow()
            
            # Создаем задачу в очереди
            queue_item = AutoRenewalQueue(
                id=generate_id(),
                user_id=user_id,
                auto_renewal_settings_id=auto_renewal_settings.id,
                subscription_id=subscription_id,
                subscription_expires_at=expires_at,
                scheduled_at=scheduled_at,
                max_attempts=auto_renewal_settings.max_retry_attempts
            )
            
            await self.payment_method_repo.create_auto_renewal_queue_item(queue_item)
            
            logger.info(f"Scheduled auto renewal for user {user_id}, subscription {subscription_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling auto renewal for user {user_id}: {e}")
            return False
    
    async def _process_single_renewal(self, renewal_task: AutoRenewalQueue) -> AutoRenewalResult:
        """
        Обрабатывает одну задачу автопродления.
        
        Args:
            renewal_task: Задача автопродления
            
        Returns:
            Результат обработки
        """
        try:
            # Получаем настройки автопродления
            auto_renewal_settings = await self.payment_method_repo.get_auto_renewal_settings(
                renewal_task.user_id
            )
            
            if not auto_renewal_settings:
                return AutoRenewalResult(
                    success=False,
                    error_message="Auto renewal settings not found"
                )
            
            # Получаем платежный метод
            payment_method = await self.payment_method_repo.get_payment_method_by_id(
                auto_renewal_settings.payment_method_id
            )
            
            if not payment_method or not payment_method.is_active:
                return AutoRenewalResult(
                    success=False,
                    error_message="Payment method not available"
                )
            
            # Получаем информацию о текущей подписке
            current_subscription = await self.subscription_repo.get_subscription_by_id(
                renewal_task.subscription_id
            )
            
            if not current_subscription:
                return AutoRenewalResult(
                    success=False,
                    error_message="Current subscription not found"
                )
            
            # Расшифровываем данные платежного метода
            try:
                decrypted_data = decrypt_data(payment_method.encrypted_data)
                payment_data = json.loads(decrypted_data)
            except Exception as e:
                return AutoRenewalResult(
                    success=False,
                    error_message=f"Failed to decrypt payment data: {str(e)}"
                )
            
            # Создаем новую подписку с тем же планом
            renewal_result = await self._create_renewal_subscription(
                renewal_task.user_id,
                current_subscription,
                payment_method,
                payment_data
            )
            
            if renewal_result.success:
                # Логируем успешное использование платежного метода
                await self._log_auto_renewal_usage(
                    renewal_task.user_id,
                    payment_method.id,
                    "auto_renewal",
                    "success",
                    subscription_id=renewal_result.subscription_id,
                    transaction_id=renewal_result.transaction_id
                )
                
                # Обновляем время последнего использования платежного метода
                await self.payment_method_repo.update_payment_method(
                    payment_method.id,
                    {'last_used_at': datetime.utcnow()}
                )
                
                # Обновляем статистику автопродления
                await self.payment_method_repo.update_auto_renewal_settings(
                    auto_renewal_settings.id,
                    {
                        'last_successful_renewal': datetime.utcnow(),
                        'current_retry_count': 0
                    }
                )
            else:
                # Логируем неудачную попытку
                await self._log_auto_renewal_usage(
                    renewal_task.user_id,
                    payment_method.id,
                    "auto_renewal",
                    "failure",
                    error_message=renewal_result.error_message
                )
                
                # Обновляем статистику неудач
                await self.payment_method_repo.update_auto_renewal_settings(
                    auto_renewal_settings.id,
                    {
                        'last_renewal_attempt': datetime.utcnow(),
                        'last_failure_reason': renewal_result.error_message,
                        'current_retry_count': auto_renewal_settings.current_retry_count + 1
                    }
                )
            
            return renewal_result
            
        except Exception as e:
            logger.error(f"Error processing renewal task {renewal_task.id}: {e}")
            return AutoRenewalResult(
                success=False,
                error_message=str(e),
                retry_needed=True
            )
    
    async def _create_renewal_subscription(
        self,
        user_id: int,
        current_subscription: Any,
        payment_method: Any,
        payment_data: Dict[str, Any]
    ) -> AutoRenewalResult:
        """
        Создает новую подписку для продления.
        
        Args:
            user_id: ID пользователя
            current_subscription: Текущая подписка
            payment_method: Платежный метод
            payment_data: Данные платежного метода
            
        Returns:
            Результат создания подписки
        """
        try:
            # TODO: Реализовать создание подписки через платежную систему
            # Пока возвращаем заглушку
            
            # Имитируем создание платежа
            if payment_method.payment_type.value == "yookassa":
                # Создаем платеж через YooKassa
                payment_result = await self._process_yookassa_renewal(
                    user_id, current_subscription, payment_data
                )
            elif payment_method.payment_type.value == "cryptomus":
                # Создаем платеж через Cryptomus
                payment_result = await self._process_cryptomus_renewal(
                    user_id, current_subscription, payment_data
                )
            else:
                return AutoRenewalResult(
                    success=False,
                    error_message="Unsupported payment method type"
                )
            
            return payment_result
            
        except Exception as e:
            logger.error(f"Error creating renewal subscription: {e}")
            return AutoRenewalResult(
                success=False,
                error_message=str(e),
                retry_needed=True
            )
    
    async def _process_yookassa_renewal(
        self,
        user_id: int,
        current_subscription: Any,
        payment_data: Dict[str, Any]
    ) -> AutoRenewalResult:
        """Обрабатывает продление через YooKassa."""
        # TODO: Реализовать интеграцию с YooKassa API для автопродления
        # Пока возвращаем заглушку
        return AutoRenewalResult(
            success=True,
            subscription_id=generate_id(),
            transaction_id=f"yoo_{generate_id()[:8]}"
        )
    
    async def _process_cryptomus_renewal(
        self,
        user_id: int,
        current_subscription: Any,
        payment_data: Dict[str, Any]
    ) -> AutoRenewalResult:
        """Обрабатывает продление через Cryptomus."""
        # TODO: Реализовать интеграцию с Cryptomus API для автопродления
        # Пока возвращаем заглушку
        return AutoRenewalResult(
            success=True,
            subscription_id=generate_id(),
            transaction_id=f"crypto_{generate_id()[:8]}"
        )
    
    async def _log_auto_renewal_usage(
        self,
        user_id: int,
        payment_method_id: str,
        operation: str,
        result: str,
        subscription_id: Optional[str] = None,
        transaction_id: Optional[str] = None,
        error_message: Optional[str] = None
    ):
        """Логирует использование платежного метода для автопродления."""
        try:
            log = PaymentMethodUsageLog(
                id=generate_id(),
                user_id=user_id,
                payment_method_id=payment_method_id,
                operation_type=operation,
                operation_result=result,
                subscription_id=subscription_id,
                transaction_id=transaction_id,
                is_auto_renewal=True,
                error_message=error_message
            )
            await self.payment_method_repo.log_payment_method_usage(log)
        except Exception as e:
            logger.error(f"Failed to log auto renewal usage: {e}")
    
    async def _send_success_notification(self, renewal_task: AutoRenewalQueue, result: AutoRenewalResult):
        """Отправляет уведомление об успешном автопродлении."""
        try:
            # TODO: Реализовать отправку уведомлений
            logger.info(f"Auto renewal successful for user {renewal_task.user_id}")
        except Exception as e:
            logger.error(f"Failed to send success notification: {e}")
    
    async def _send_failure_notification(self, renewal_task: AutoRenewalQueue, result: AutoRenewalResult):
        """Отправляет уведомление о неудачном автопродлении."""
        try:
            # TODO: Реализовать отправку уведомлений
            logger.error(f"Auto renewal failed for user {renewal_task.user_id}: {result.error_message}")
        except Exception as e:
            logger.error(f"Failed to send failure notification: {e}")
