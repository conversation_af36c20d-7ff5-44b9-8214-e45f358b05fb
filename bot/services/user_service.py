"""
UserService - сервис для управления пользователями VPN.
Содержит бизнес-логику работы с пользователями.
"""

from typing import Optional, Dict, Any
from datetime import datetime

from .base import ServiceWithRepository
from db.models import VPNUsers
from utils.logging_config import get_logger

logger = get_logger(__name__)


class UserService(ServiceWithRepository):
    """
    Сервис для управления пользователями VPN.
    Содержит всю бизнес-логику работы с пользователями.
    """
    
    async def initialize(self) -> None:
        """Инициализация UserService."""
        logger.info("UserService initialized")
    
    async def cleanup(self) -> None:
        """Очистка ресурсов UserService."""
        logger.info("UserService cleaned up")
    
    async def create_or_get_user(self, telegram_id: int) -> VPNUsers:
        """
        Создает нового пользователя или возвращает существующего.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            Объект пользователя
            
        Raises:
            Exception: При ошибке создания пользователя
        """
        try:
            async with self.get_user_repository() as user_repo:
                # Проверяем существование пользователя
                user = await user_repo.get_by_telegram_id(telegram_id)
                
                if user:
                    logger.debug(f"User {telegram_id} already exists")
                    return user
                
                # Создаем нового пользователя
                user = await user_repo.create_user(telegram_id)
                logger.info(f"Created new user: {telegram_id}")
                return user
                
        except Exception as e:
            logger.error(f"Error creating/getting user {telegram_id}: {e}")
            raise
    
    async def get_user_profile(self, telegram_id: int) -> Optional[VPNUsers]:
        """
        Получает профиль пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            Профиль пользователя или None
        """
        try:
            async with self.get_user_repository() as user_repo:
                user = await user_repo.get_by_telegram_id(telegram_id)
                
                if user:
                    logger.debug(f"Retrieved profile for user {telegram_id}")
                else:
                    logger.debug(f"Profile not found for user {telegram_id}")
                
                return user
                
        except Exception as e:
            logger.error(f"Error getting user profile {telegram_id}: {e}")
            raise
    
    async def get_user_by_vpn_id(self, vpn_id: str) -> Optional[VPNUsers]:
        """
        Получает пользователя по VPN ID.
        
        Args:
            vpn_id: VPN ID пользователя
            
        Returns:
            Пользователь или None
        """
        try:
            async with self.get_user_repository() as user_repo:
                user = await user_repo.get_by_vpn_id(vpn_id)
                
                if user:
                    logger.debug(f"Retrieved user by VPN ID {vpn_id}")
                else:
                    logger.debug(f"User not found by VPN ID {vpn_id}")
                
                return user
                
        except Exception as e:
            logger.error(f"Error getting user by VPN ID {vpn_id}: {e}")
            raise
    
    async def check_test_subscription_used(self, telegram_id: int) -> bool:
        """
        Проверяет, использовал ли пользователь тестовую подписку.
        Теперь проверяем по наличию expire_date.

        Args:
            telegram_id: Telegram ID пользователя

        Returns:
            True если тестовая подписка использована
        """
        try:
            async with self.get_user_repository() as user_repo:
                user = await user_repo.get_by_telegram_id(telegram_id)

                if not user:
                    logger.debug(f"User {telegram_id} not found")
                    return False

                # Считаем, что тестовая подписка использована, если есть expire_date
                has_test = user.expire_date is not None
                logger.debug(f"Test subscription check for user {telegram_id}: {has_test}")
                return has_test

        except Exception as e:
            logger.error(f"Error checking test subscription for user {telegram_id}: {e}")
            raise
    
    async def create_test_subscription(self, telegram_id: int, days: int = 5) -> bool:
        """
        Создает тестовую подписку на указанное количество дней.

        Args:
            telegram_id: Telegram ID пользователя
            days: Количество дней тестовой подписки

        Returns:
            True если операция прошла успешно
        """
        try:
            from datetime import datetime, timedelta

            async with self.get_user_repository() as user_repo:
                # Вычисляем дату истечения
                expire_date = datetime.now() + timedelta(days=days)

                # Обновляем данные пользователя
                result = await user_repo.update_user_vpn_data(
                    telegram_id,
                    expire_date=expire_date
                )

                if result:
                    logger.info(f"Created {days}-day test subscription for user {telegram_id}")
                else:
                    logger.warning(f"Failed to create test subscription for user {telegram_id}")

                return result

        except Exception as e:
            logger.error(f"Error creating test subscription for user {telegram_id}: {e}")
            raise
    
    async def get_user_stats(self) -> Dict[str, Any]:
        """
        Получает статистику пользователей.
        
        Returns:
            Словарь со статистикой
        """
        try:
            async with self.get_user_repository() as user_repo:
                stats = await user_repo.get_user_stats()
                logger.debug(f"Retrieved user stats: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            raise
    
    async def validate_user_exists(self, telegram_id: int) -> bool:
        """
        Проверяет существование пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            True если пользователь существует
        """
        try:
            user = await self.get_user_profile(telegram_id)
            return user is not None
            
        except Exception as e:
            logger.error(f"Error validating user existence {telegram_id}: {e}")
            return False
    
    async def get_user_creation_info(self, telegram_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает информацию о создании пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            Информация о пользователе
        """
        try:
            user = await self.get_user_profile(telegram_id)
            
            if not user:
                return None
            
            return {
                'telegram_id': user.tg_id,
                'vpn_id': user.vpn_id,
                'test_subscription_used': user.expire_date is not None,
                'expire_date': user.expire_date,
                'created_at': getattr(user, 'created_at', None),
                'exists': True
            }
            
        except Exception as e:
            logger.error(f"Error getting user creation info {telegram_id}: {e}")
            raise
    
    async def ensure_user_exists(self, telegram_id: int) -> VPNUsers:
        """
        Гарантирует существование пользователя (создает если не существует).
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            Объект пользователя
        """
        try:
            # Сначала пытаемся получить существующего пользователя
            user = await self.get_user_profile(telegram_id)
            
            if user:
                return user
            
            # Если пользователя нет, создаем
            user = await self.create_or_get_user(telegram_id)
            logger.info(f"Ensured user exists: {telegram_id}")
            return user
            
        except Exception as e:
            logger.error(f"Error ensuring user exists {telegram_id}: {e}")
            raise
    
    async def update_user_activity(self, telegram_id: int) -> bool:
        """
        Обновляет активность пользователя (для будущего расширения).
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            True если обновление прошло успешно
        """
        try:
            # Пока что просто проверяем существование пользователя
            user = await self.ensure_user_exists(telegram_id)
            
            # В будущем здесь можно добавить обновление last_activity
            logger.debug(f"Updated activity for user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating user activity {telegram_id}: {e}")
            return False
    
    async def get_users_with_test_subscription(self) -> list:
        """
        Получает список пользователей с использованной тестовой подпиской.
        
        Returns:
            Список пользователей
        """
        try:
            async with self.get_user_repository() as user_repo:
                # Получаем всех пользователей и фильтруем
                # В будущем можно добавить специальный метод в Repository
                stats = await user_repo.get_user_stats()
                
                # Возвращаем количество пользователей с тестовой подпиской
                return {
                    'count': stats.get('test_users', 0),
                    'total': stats.get('total_users', 0)
                }
                
        except Exception as e:
            logger.error(f"Error getting users with test subscription: {e}")
            raise
