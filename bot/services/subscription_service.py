"""
SubscriptionService - сервис для управления подписками.
Содержит бизнес-логику работы с подписками через Marzban API.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from .base import ServiceWithRepository
from utils import marzban_api
from utils.logging_config import get_logger

logger = get_logger(__name__)


class SubscriptionService(ServiceWithRepository):
    """
    Сервис для управления подписками VPN.
    Содержит бизнес-логику работы с подписками через Marzban API.
    """
    
    async def initialize(self) -> None:
        """Инициализация SubscriptionService."""
        logger.info("SubscriptionService initialized")
    
    async def cleanup(self) -> None:
        """Очистка ресурсов SubscriptionService."""
        logger.info("SubscriptionService cleaned up")
    
    async def get_user_subscription_info(self, telegram_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает информацию о подписке пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            Информация о подписке или None
        """
        try:
            # Получаем информацию через Marzban API
            subscription_info = await marzban_api.get_marzban_profile(telegram_id)
            
            if subscription_info:
                logger.debug(f"Retrieved subscription info for user {telegram_id}")
                
                # Обогащаем информацию дополнительными данными
                enriched_info = await self._enrich_subscription_info(subscription_info)
                return enriched_info
            else:
                logger.debug(f"No subscription found for user {telegram_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting subscription info for user {telegram_id}: {e}")
            return None
    
    async def _enrich_subscription_info(self, subscription_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обогащает информацию о подписке дополнительными данными.
        
        Args:
            subscription_info: Базовая информация о подписке
            
        Returns:
            Обогащенная информация
        """
        try:
            # Добавляем вычисляемые поля
            enriched = subscription_info.copy()
            
            # Проверяем статус подписки
            if 'expire' in subscription_info:
                expire_timestamp = subscription_info['expire']
                if expire_timestamp:
                    expire_date = datetime.fromtimestamp(expire_timestamp)
                    now = datetime.now()
                    
                    enriched['expire_date'] = expire_date.isoformat()
                    enriched['days_remaining'] = (expire_date - now).days
                    enriched['is_expired'] = expire_date < now
                    enriched['expires_soon'] = (expire_date - now).days <= 3
                else:
                    enriched['is_unlimited'] = True
            
            # Добавляем информацию о статусе
            status = subscription_info.get('status', 'unknown')
            enriched['is_active'] = status == 'active'
            
            return enriched
            
        except Exception as e:
            logger.error(f"Error enriching subscription info: {e}")
            return subscription_info
    
    async def create_test_subscription(self, telegram_id: int) -> bool:
        """
        Создает тестовую подписку для пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            True если подписка создана успешно
        """
        try:
            # Получаем UserService для работы с пользователем
            if self.container:
                from .user_service import UserService
                user_service = self.container.get_service(UserService)
                
                # Проверяем, не использовал ли пользователь уже тестовую подписку
                has_test = await user_service.check_test_subscription_used(telegram_id)
                if has_test:
                    logger.warning(f"User {telegram_id} already used test subscription")
                    return False
                
                # Получаем профиль пользователя
                user = await user_service.get_user_profile(telegram_id)
                if not user:
                    # Создаем пользователя если не существует
                    user = await user_service.create_or_get_user(telegram_id)
                
                # Создаем тестовую подписку через Marzban API
                result = await marzban_api.generate_test_subscription(user.vpn_id)
                
                if result:
                    # Отмечаем тестовую подписку как использованную
                    await user_service.mark_test_subscription_used(telegram_id)
                    logger.info(f"Created test subscription for user {telegram_id}")
                    return True
                else:
                    logger.error(f"Failed to create test subscription for user {telegram_id}")
                    return False
            else:
                logger.error("Service container not available for test subscription creation")
                return False
                
        except Exception as e:
            logger.error(f"Error creating test subscription for user {telegram_id}: {e}")
            return False
    
    async def create_paid_subscription(self, telegram_id: int, product_data: Dict[str, Any]) -> bool:
        """
        Создает платную подписку для пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            product_data: Данные о продукте
            
        Returns:
            True если подписка создана успешно
        """
        try:
            # Получаем UserService для работы с пользователем
            if self.container:
                from .user_service import UserService
                user_service = self.container.get_service(UserService)
                
                # Получаем профиль пользователя
                user = await user_service.get_user_profile(telegram_id)
                if not user:
                    # Создаем пользователя если не существует
                    user = await user_service.create_or_get_user(telegram_id)
                
                # Создаем платную подписку через Marzban API
                result = await marzban_api.generate_marzban_subscription(user.vpn_id, product_data)
                
                if result:
                    logger.info(f"Created paid subscription for user {telegram_id}")
                    return True
                else:
                    logger.error(f"Failed to create paid subscription for user {telegram_id}")
                    return False
            else:
                logger.error("Service container not available for paid subscription creation")
                return False
                
        except Exception as e:
            logger.error(f"Error creating paid subscription for user {telegram_id}: {e}")
            return False
    
    async def check_subscription_status(self, telegram_id: int) -> Dict[str, Any]:
        """
        Проверяет статус подписки пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            Статус подписки
        """
        try:
            subscription_info = await self.get_user_subscription_info(telegram_id)
            
            if not subscription_info:
                return {
                    'has_subscription': False,
                    'status': 'no_subscription',
                    'message': 'No active subscription found'
                }
            
            is_active = subscription_info.get('is_active', False)
            is_expired = subscription_info.get('is_expired', True)
            expires_soon = subscription_info.get('expires_soon', False)
            days_remaining = subscription_info.get('days_remaining', 0)
            
            if is_expired:
                status = 'expired'
                message = 'Subscription has expired'
            elif expires_soon:
                status = 'expires_soon'
                message = f'Subscription expires in {days_remaining} days'
            elif is_active:
                status = 'active'
                message = f'Subscription is active, {days_remaining} days remaining'
            else:
                status = 'inactive'
                message = 'Subscription is inactive'
            
            return {
                'has_subscription': True,
                'status': status,
                'message': message,
                'days_remaining': days_remaining,
                'is_active': is_active,
                'is_expired': is_expired,
                'expires_soon': expires_soon,
                'subscription_info': subscription_info
            }
            
        except Exception as e:
            logger.error(f"Error checking subscription status for user {telegram_id}: {e}")
            return {
                'has_subscription': False,
                'status': 'error',
                'message': 'Error checking subscription status'
            }
    
    async def get_subscription_url(self, telegram_id: int) -> Optional[str]:
        """
        Получает URL подписки для пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            URL подписки или None
        """
        try:
            subscription_info = await self.get_user_subscription_info(telegram_id)
            
            if not subscription_info:
                return None
            
            subscription_url = subscription_info.get('subscription_url')
            if not subscription_url:
                return None
            
            # Проверяем, содержит ли URL уже полный путь
            if not subscription_url.startswith('http'):
                # Если это относительный путь, добавляем базовый URL
                import glv
                base_url = glv.config.get('PANEL_GLOBAL', '')
                subscription_url = base_url + subscription_url
            
            logger.debug(f"Retrieved subscription URL for user {telegram_id}")
            return subscription_url
            
        except Exception as e:
            logger.error(f"Error getting subscription URL for user {telegram_id}: {e}")
            return None
    
    async def extend_subscription(self, telegram_id: int, days: int) -> bool:
        """
        Продлевает подписку пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            days: Количество дней для продления
            
        Returns:
            True если продление прошло успешно
        """
        try:
            # Получаем текущую информацию о подписке
            subscription_info = await self.get_user_subscription_info(telegram_id)
            
            if not subscription_info:
                logger.error(f"No subscription found for extension: {telegram_id}")
                return False
            
            # Здесь можно добавить логику продления через Marzban API
            # Пока что это заглушка для будущего расширения
            logger.info(f"Extended subscription for user {telegram_id} by {days} days")
            return True
            
        except Exception as e:
            logger.error(f"Error extending subscription for user {telegram_id}: {e}")
            return False
    
    async def get_expiring_subscriptions(self, days_before: int = 3) -> List[Dict[str, Any]]:
        """
        Получает список подписок, истекающих в ближайшие дни.
        
        Args:
            days_before: За сколько дней до истечения искать
            
        Returns:
            Список истекающих подписок
        """
        try:
            # Пока что возвращаем пустой список
            # В будущем здесь будет логика получения всех пользователей
            # и проверки их подписок
            logger.debug(f"Getting subscriptions expiring in {days_before} days")
            return []
            
        except Exception as e:
            logger.error(f"Error getting expiring subscriptions: {e}")
            return []
    
    async def validate_subscription_access(self, telegram_id: int) -> bool:
        """
        Проверяет доступ пользователя к подписке.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            True если доступ разрешен
        """
        try:
            status = await self.check_subscription_status(telegram_id)
            
            # Разрешаем доступ если подписка активна и не истекла
            has_access = (
                status.get('has_subscription', False) and
                status.get('is_active', False) and
                not status.get('is_expired', True)
            )
            
            logger.debug(f"Subscription access check for user {telegram_id}: {has_access}")
            return has_access
            
        except Exception as e:
            logger.error(f"Error validating subscription access for user {telegram_id}: {e}")
            return False
