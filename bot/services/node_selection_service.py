"""
Сервис для выбора оптимальных нод.
Реализует алгоритмы выбора нод на основе загрузки, производительности и предпочтений пользователя.
"""

from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import random

from repositories.country_node_repository import (
    MarzbanNodeRepository, MarzbanInboundRepository, NodeStatisticsRepository
)
from services.user_preference_service import UserPreferenceService, UserPreferences
from db.models_countries_nodes import MarzbanNode, MarzbanInbound, NodeStatus
from utils.logging_config import get_logger

logger = get_logger(__name__)


class SelectionStrategy(str, Enum):
    """Стратегии выбора нод."""
    LOAD_BALANCED = "load_balanced"      # По загрузке
    PERFORMANCE = "performance"          # По производительности
    GEOGRAPHIC = "geographic"            # По географии
    RANDOM = "random"                   # Случайный выбор
    HYBRID = "hybrid"                   # Гибридный подход


@dataclass
class NodeScore:
    """Оценка ноды для выбора."""
    node: MarzbanNode
    total_score: float
    load_score: float
    performance_score: float
    geographic_score: float
    availability_score: float
    reasons: List[str]


@dataclass
class NodeSelectionResult:
    """Результат выбора ноды."""
    selected_node: Optional[MarzbanNode]
    available_inbounds: List[MarzbanInbound]
    fallback_nodes: List[MarzbanNode]
    selection_reasons: List[str]
    total_candidates: int
    success: bool


class NodeSelectionService:
    """Сервис для выбора оптимальных нод."""
    
    def __init__(self,
                 node_repo: MarzbanNodeRepository,
                 inbound_repo: MarzbanInboundRepository,
                 stats_repo: NodeStatisticsRepository,
                 preference_service: UserPreferenceService):
        self.node_repo = node_repo
        self.inbound_repo = inbound_repo
        self.stats_repo = stats_repo
        self.preference_service = preference_service
    
    async def select_optimal_node(self, 
                                 tg_id: int,
                                 strategy: SelectionStrategy = SelectionStrategy.HYBRID,
                                 fallback_count: int = 3) -> NodeSelectionResult:
        """
        Выбрать оптимальную ноду для пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            strategy: Стратегия выбора
            fallback_count: Количество резервных нод
        """
        try:
            # Получаем предпочтения пользователя
            preferences = await self.preference_service.get_user_preferences(tg_id)
            
            # Получаем кандидатов
            candidates = await self._get_candidate_nodes(preferences)
            
            if not candidates:
                logger.warning(f"No candidate nodes found for user {tg_id}")
                return NodeSelectionResult(
                    selected_node=None,
                    available_inbounds=[],
                    fallback_nodes=[],
                    selection_reasons=["No available nodes found"],
                    total_candidates=0,
                    success=False
                )
            
            # Оцениваем ноды
            scored_nodes = await self._score_nodes(candidates, preferences, strategy)
            
            if not scored_nodes:
                logger.warning(f"No scored nodes for user {tg_id}")
                return NodeSelectionResult(
                    selected_node=None,
                    available_inbounds=[],
                    fallback_nodes=[],
                    selection_reasons=["No suitable nodes found"],
                    total_candidates=len(candidates),
                    success=False
                )
            
            # Сортируем по оценке
            scored_nodes.sort(key=lambda x: x.total_score, reverse=True)
            
            # Выбираем лучшую ноду
            best_node_score = scored_nodes[0]
            selected_node = best_node_score.node
            
            # Получаем доступные inbounds для выбранной ноды
            available_inbounds = await self._get_compatible_inbounds(
                selected_node, preferences.preferred_protocols
            )
            
            # Выбираем резервные ноды
            fallback_nodes = [score.node for score in scored_nodes[1:fallback_count+1]]
            
            # Обновляем последний выбор пользователя
            await self.preference_service.update_last_selection(
                tg_id, selected_node.country_id, selected_node.id
            )
            
            logger.info(f"Selected node {selected_node.id} for user {tg_id} with score {best_node_score.total_score}")
            
            return NodeSelectionResult(
                selected_node=selected_node,
                available_inbounds=available_inbounds,
                fallback_nodes=fallback_nodes,
                selection_reasons=best_node_score.reasons,
                total_candidates=len(candidates),
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error selecting optimal node for user {tg_id}: {e}")
            return NodeSelectionResult(
                selected_node=None,
                available_inbounds=[],
                fallback_nodes=[],
                selection_reasons=[f"Selection error: {str(e)}"],
                total_candidates=0,
                success=False
            )
    
    async def select_node_by_country(self, 
                                   tg_id: int, 
                                   country_id: str,
                                   strategy: SelectionStrategy = SelectionStrategy.HYBRID) -> NodeSelectionResult:
        """Выбрать ноду в конкретной стране."""
        try:
            preferences = await self.preference_service.get_user_preferences(tg_id)
            
            # Получаем ноды только в указанной стране
            candidates = await self.node_repo.get_nodes_by_country(country_id.upper())
            
            if not candidates:
                return NodeSelectionResult(
                    selected_node=None,
                    available_inbounds=[],
                    fallback_nodes=[],
                    selection_reasons=[f"No nodes available in {country_id}"],
                    total_candidates=0,
                    success=False
                )
            
            # Фильтруем по протоколам
            compatible_candidates = []
            for node in candidates:
                inbounds = await self._get_compatible_inbounds(node, preferences.preferred_protocols)
                if inbounds:
                    compatible_candidates.append(node)
            
            if not compatible_candidates:
                return NodeSelectionResult(
                    selected_node=None,
                    available_inbounds=[],
                    fallback_nodes=[],
                    selection_reasons=[f"No compatible nodes in {country_id} for protocols {preferences.preferred_protocols}"],
                    total_candidates=len(candidates),
                    success=False
                )
            
            # Оцениваем и выбираем лучшую ноду
            scored_nodes = await self._score_nodes(compatible_candidates, preferences, strategy)
            scored_nodes.sort(key=lambda x: x.total_score, reverse=True)
            
            selected_node = scored_nodes[0].node
            available_inbounds = await self._get_compatible_inbounds(
                selected_node, preferences.preferred_protocols
            )
            fallback_nodes = [score.node for score in scored_nodes[1:4]]
            
            # Обновляем последний выбор
            await self.preference_service.update_last_selection(tg_id, country_id, selected_node.id)
            
            return NodeSelectionResult(
                selected_node=selected_node,
                available_inbounds=available_inbounds,
                fallback_nodes=fallback_nodes,
                selection_reasons=scored_nodes[0].reasons,
                total_candidates=len(candidates),
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error selecting node by country {country_id} for user {tg_id}: {e}")
            return NodeSelectionResult(
                selected_node=None,
                available_inbounds=[],
                fallback_nodes=[],
                selection_reasons=[f"Selection error: {str(e)}"],
                total_candidates=0,
                success=False
            )
    
    async def _get_candidate_nodes(self, preferences: UserPreferences) -> List[MarzbanNode]:
        """Получить кандидатов для выбора."""
        if preferences.preferred_countries:
            # Если есть предпочитаемые страны, ищем ноды в них
            candidates = await self.node_repo.get_optimal_nodes(
                country_ids=preferences.preferred_countries,
                protocols=preferences.preferred_protocols,
                max_load_percent=85.0,
                limit=20
            )
        else:
            # Если предпочтений нет, ищем среди всех доступных нод
            candidates = await self.node_repo.get_optimal_nodes(
                protocols=preferences.preferred_protocols,
                max_load_percent=85.0,
                limit=15
            )
        
        return candidates
    
    async def _score_nodes(self, 
                          nodes: List[MarzbanNode], 
                          preferences: UserPreferences,
                          strategy: SelectionStrategy) -> List[NodeScore]:
        """Оценить ноды по различным критериям."""
        scored_nodes = []
        
        for node in nodes:
            # Получаем статистику ноды
            avg_stats = await self.stats_repo.get_average_performance(node.id, hours=24)
            
            # Вычисляем оценки по разным критериям
            load_score = self._calculate_load_score(node)
            performance_score = self._calculate_performance_score(node, avg_stats)
            geographic_score = self._calculate_geographic_score(node, preferences)
            availability_score = self._calculate_availability_score(node)
            
            # Вычисляем общую оценку в зависимости от стратегии
            total_score = self._calculate_total_score(
                load_score, performance_score, geographic_score, availability_score, strategy
            )
            
            # Формируем причины выбора
            reasons = self._generate_selection_reasons(
                node, load_score, performance_score, geographic_score, availability_score
            )
            
            scored_node = NodeScore(
                node=node,
                total_score=total_score,
                load_score=load_score,
                performance_score=performance_score,
                geographic_score=geographic_score,
                availability_score=availability_score,
                reasons=reasons
            )
            
            scored_nodes.append(scored_node)
        
        return scored_nodes
    
    def _calculate_load_score(self, node: MarzbanNode) -> float:
        """Вычислить оценку загрузки ноды (0-100)."""
        if node.max_users == 0:
            return 0.0
        
        load_percent = (node.current_users / node.max_users) * 100
        
        # Инвертируем: меньше загрузка = выше оценка
        if load_percent <= 50:
            return 100.0
        elif load_percent <= 70:
            return 80.0
        elif load_percent <= 85:
            return 60.0
        else:
            return 20.0
    
    def _calculate_performance_score(self, node: MarzbanNode, avg_stats: Dict[str, float]) -> float:
        """Вычислить оценку производительности ноды (0-100)."""
        cpu_score = max(0, 100 - avg_stats.get('avg_cpu_usage', 0))
        memory_score = max(0, 100 - avg_stats.get('avg_memory_usage', 0))
        
        # Оценка времени отклика (меньше = лучше)
        response_time = avg_stats.get('avg_response_time_ms', 100)
        if response_time <= 50:
            response_score = 100.0
        elif response_time <= 100:
            response_score = 80.0
        elif response_time <= 200:
            response_score = 60.0
        else:
            response_score = 30.0
        
        # Средневзвешенная оценка
        return (cpu_score * 0.3 + memory_score * 0.3 + response_score * 0.4)
    
    def _calculate_geographic_score(self, node: MarzbanNode, preferences: UserPreferences) -> float:
        """Вычислить географическую оценку ноды (0-100)."""
        # Если нода в предпочитаемых странах
        if preferences.preferred_countries and node.country_id in preferences.preferred_countries:
            return 100.0
        
        # Если это последняя выбранная страна
        if preferences.last_selected_country and node.country_id == preferences.last_selected_country:
            return 80.0
        
        # Базовая оценка для других стран
        return 50.0
    
    def _calculate_availability_score(self, node: MarzbanNode) -> float:
        """Вычислить оценку доступности ноды (0-100)."""
        if node.status != NodeStatus.CONNECTED:
            return 0.0
        
        if not node.is_active:
            return 0.0
        
        # Оценка на основе времени последней проверки
        if node.last_check:
            from datetime import datetime, timedelta
            time_since_check = datetime.utcnow() - node.last_check
            
            if time_since_check <= timedelta(minutes=5):
                return 100.0
            elif time_since_check <= timedelta(minutes=15):
                return 80.0
            elif time_since_check <= timedelta(hours=1):
                return 60.0
            else:
                return 40.0
        
        return 70.0  # Если нет данных о последней проверке
    
    def _calculate_total_score(self, 
                              load_score: float, 
                              performance_score: float,
                              geographic_score: float, 
                              availability_score: float,
                              strategy: SelectionStrategy) -> float:
        """Вычислить общую оценку ноды."""
        if strategy == SelectionStrategy.LOAD_BALANCED:
            return load_score * 0.6 + performance_score * 0.2 + availability_score * 0.2
        elif strategy == SelectionStrategy.PERFORMANCE:
            return performance_score * 0.6 + load_score * 0.2 + availability_score * 0.2
        elif strategy == SelectionStrategy.GEOGRAPHIC:
            return geographic_score * 0.6 + load_score * 0.2 + availability_score * 0.2
        elif strategy == SelectionStrategy.RANDOM:
            return random.uniform(0, 100)
        else:  # HYBRID
            return (load_score * 0.3 + performance_score * 0.3 + 
                   geographic_score * 0.2 + availability_score * 0.2)
    
    def _generate_selection_reasons(self, 
                                  node: MarzbanNode,
                                  load_score: float,
                                  performance_score: float, 
                                  geographic_score: float,
                                  availability_score: float) -> List[str]:
        """Сгенерировать причины выбора ноды."""
        reasons = []
        
        if load_score >= 80:
            reasons.append(f"Low load ({node.current_users}/{node.max_users} users)")
        if performance_score >= 80:
            reasons.append("High performance")
        if geographic_score >= 80:
            reasons.append("Preferred location")
        if availability_score >= 90:
            reasons.append("Recently checked and online")
        
        if not reasons:
            reasons.append("Best available option")
        
        return reasons
    
    async def _get_compatible_inbounds(self, node: MarzbanNode, protocols: List[str]) -> List[MarzbanInbound]:
        """Получить совместимые inbounds для ноды."""
        all_inbounds = await self.inbound_repo.get_inbounds_by_node(node.id)
        
        compatible = []
        for inbound in all_inbounds:
            if inbound.protocol in protocols and inbound.is_active:
                compatible.append(inbound)
        
        return compatible
