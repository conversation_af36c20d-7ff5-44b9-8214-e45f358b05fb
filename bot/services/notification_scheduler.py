"""
Планировщик уведомлений.

Этот модуль содержит логику автоматической обработки и отправки
запланированных уведомлений.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

from services.advanced_notification_service import AdvancedNotificationService
from utils.logging_config import get_logger

logger = get_logger(__name__)


class NotificationScheduler:
    """
    Планировщик для автоматической отправки уведомлений.
    
    Запускается в фоновом режиме и периодически проверяет
    базу данных на наличие уведомлений, готовых к отправке.
    """
    
    def __init__(self, 
                 notification_service: AdvancedNotificationService,
                 telegram_service=None,
                 check_interval_seconds: int = 60,
                 batch_size: int = 50):
        """
        Инициализирует планировщик.
        
        Args:
            notification_service: Сервис уведомлений
            telegram_service: Сервис для отправки в Telegram
            check_interval_seconds: Интервал проверки в секундах
            batch_size: Размер пакета для обработки
        """
        self.notification_service = notification_service
        self.telegram_service = telegram_service
        self.check_interval = check_interval_seconds
        self.batch_size = batch_size
        
        self.is_running = False
        self.task: Optional[asyncio.Task] = None
        
        # Статистика
        self.stats = {
            'total_processed': 0,
            'total_sent': 0,
            'total_failed': 0,
            'last_run': None,
            'last_error': None,
            'uptime_start': None
        }
    
    async def start(self):
        """Запускает планировщик."""
        if self.is_running:
            logger.warning("Notification scheduler is already running")
            return
        
        self.is_running = True
        self.stats['uptime_start'] = datetime.utcnow()
        
        logger.info(f"Starting notification scheduler with {self.check_interval}s interval")
        
        # Запускаем основной цикл в отдельной задаче
        self.task = asyncio.create_task(self._run_scheduler_loop())
        
        return self.task
    
    async def stop(self):
        """Останавливает планировщик."""
        if not self.is_running:
            logger.warning("Notification scheduler is not running")
            return
        
        logger.info("Stopping notification scheduler...")
        self.is_running = False
        
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        logger.info("Notification scheduler stopped")
    
    async def _run_scheduler_loop(self):
        """Основной цикл планировщика."""
        logger.info("Notification scheduler loop started")
        
        while self.is_running:
            try:
                await self._process_pending_notifications()
                self.stats['last_run'] = datetime.utcnow()
                
                # Ждем до следующей проверки
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                logger.info("Scheduler loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                self.stats['last_error'] = str(e)
                
                # При ошибке ждем дольше перед следующей попыткой
                await asyncio.sleep(min(self.check_interval * 2, 300))
        
        logger.info("Notification scheduler loop ended")
    
    async def _process_pending_notifications(self):
        """Обрабатывает ожидающие уведомления."""
        try:
            # Получаем пакет ожидающих уведомлений
            pending_notifications = await self.notification_service.get_pending_notifications(
                limit=self.batch_size
            )
            
            if not pending_notifications:
                logger.debug("No pending notifications found")
                return
            
            logger.info(f"Processing {len(pending_notifications)} pending notifications")
            
            # Обрабатываем каждое уведомление
            sent_count = 0
            failed_count = 0
            
            for notification in pending_notifications:
                try:
                    result = await self.notification_service.send_scheduled_notification(
                        notification, self.telegram_service
                    )
                    
                    if result.success:
                        sent_count += 1
                        logger.debug(f"Successfully sent notification {notification.id}")
                    else:
                        failed_count += 1
                        logger.warning(f"Failed to send notification {notification.id}: {result.error_message}")
                    
                    self.stats['total_processed'] += 1
                    
                    # Небольшая задержка между отправками для избежания rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error processing notification {notification.id}: {e}")
            
            # Обновляем статистику
            self.stats['total_sent'] += sent_count
            self.stats['total_failed'] += failed_count
            
            logger.info(f"Batch processed: {sent_count} sent, {failed_count} failed")
            
        except Exception as e:
            logger.error(f"Error processing pending notifications: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Возвращает статистику работы планировщика.
        
        Returns:
            Словарь со статистикой
        """
        stats = self.stats.copy()
        
        if stats['uptime_start']:
            uptime = datetime.utcnow() - stats['uptime_start']
            stats['uptime_seconds'] = int(uptime.total_seconds())
            stats['uptime_formatted'] = str(uptime).split('.')[0]  # Убираем микросекунды
        
        stats['is_running'] = self.is_running
        stats['check_interval'] = self.check_interval
        stats['batch_size'] = self.batch_size
        
        return stats
    
    def reset_stats(self):
        """Сбрасывает статистику."""
        self.stats = {
            'total_processed': 0,
            'total_sent': 0,
            'total_failed': 0,
            'last_run': None,
            'last_error': None,
            'uptime_start': datetime.utcnow() if self.is_running else None
        }
        logger.info("Scheduler stats reset")
    
    @asynccontextmanager
    async def running_context(self):
        """
        Контекстный менеджер для автоматического запуска и остановки планировщика.
        
        Usage:
            async with scheduler.running_context():
                # Планировщик работает
                await asyncio.sleep(60)
            # Планировщик автоматически остановлен
        """
        await self.start()
        try:
            yield self
        finally:
            await self.stop()


class NotificationSchedulerManager:
    """
    Менеджер для управления несколькими планировщиками.
    
    Позволяет запускать планировщики с разными настройками
    для разных типов уведомлений.
    """
    
    def __init__(self):
        self.schedulers: Dict[str, NotificationScheduler] = {}
    
    def add_scheduler(self, name: str, scheduler: NotificationScheduler):
        """Добавляет планировщик."""
        self.schedulers[name] = scheduler
        logger.info(f"Added scheduler '{name}'")
    
    async def start_all(self):
        """Запускает все планировщики."""
        logger.info(f"Starting {len(self.schedulers)} schedulers")
        
        tasks = []
        for name, scheduler in self.schedulers.items():
            task = await scheduler.start()
            tasks.append(task)
            logger.info(f"Started scheduler '{name}'")
        
        return tasks
    
    async def stop_all(self):
        """Останавливает все планировщики."""
        logger.info(f"Stopping {len(self.schedulers)} schedulers")
        
        for name, scheduler in self.schedulers.items():
            await scheduler.stop()
            logger.info(f"Stopped scheduler '{name}'")
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Возвращает статистику всех планировщиков."""
        return {
            name: scheduler.get_stats()
            for name, scheduler in self.schedulers.items()
        }
    
    def get_scheduler(self, name: str) -> Optional[NotificationScheduler]:
        """Возвращает планировщик по имени."""
        return self.schedulers.get(name)
    
    @asynccontextmanager
    async def running_context(self):
        """
        Контекстный менеджер для автоматического запуска и остановки всех планировщиков.
        """
        await self.start_all()
        try:
            yield self
        finally:
            await self.stop_all()


# Фабричная функция для создания стандартного планировщика
def create_default_scheduler(notification_service: AdvancedNotificationService,
                           telegram_service=None) -> NotificationScheduler:
    """
    Создает планировщик с настройками по умолчанию.
    
    Args:
        notification_service: Сервис уведомлений
        telegram_service: Сервис Telegram
        
    Returns:
        Настроенный планировщик
    """
    return NotificationScheduler(
        notification_service=notification_service,
        telegram_service=telegram_service,
        check_interval_seconds=60,  # Проверяем каждую минуту
        batch_size=50  # Обрабатываем до 50 уведомлений за раз
    )


# Фабричная функция для создания менеджера с несколькими планировщиками
def create_multi_scheduler_manager(notification_service: AdvancedNotificationService,
                                 telegram_service=None) -> NotificationSchedulerManager:
    """
    Создает менеджер с несколькими планировщиками для разных приоритетов.
    
    Args:
        notification_service: Сервис уведомлений
        telegram_service: Сервис Telegram
        
    Returns:
        Настроенный менеджер планировщиков
    """
    manager = NotificationSchedulerManager()
    
    # Высокоприоритетный планировщик (проверяет каждые 30 секунд)
    high_priority_scheduler = NotificationScheduler(
        notification_service=notification_service,
        telegram_service=telegram_service,
        check_interval_seconds=30,
        batch_size=20
    )
    manager.add_scheduler("high_priority", high_priority_scheduler)
    
    # Обычный планировщик (проверяет каждую минуту)
    normal_scheduler = NotificationScheduler(
        notification_service=notification_service,
        telegram_service=telegram_service,
        check_interval_seconds=60,
        batch_size=50
    )
    manager.add_scheduler("normal", normal_scheduler)
    
    # Низкоприоритетный планировщик (проверяет каждые 5 минут)
    low_priority_scheduler = NotificationScheduler(
        notification_service=notification_service,
        telegram_service=telegram_service,
        check_interval_seconds=300,
        batch_size=100
    )
    manager.add_scheduler("low_priority", low_priority_scheduler)
    
    return manager
