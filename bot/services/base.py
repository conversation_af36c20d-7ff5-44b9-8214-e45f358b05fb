"""
Базовые классы и интерфейсы для Service Layer.
"""

from abc import ABC, abstractmethod
from typing import Dict, Type, Any, Optional, TypeVar, Generic
from contextlib import asynccontextmanager

from repositories.base import UnitOfWork
from repositories.user_repository import SQLUserRepository
from repositories.payment_repository import SQLPaymentRepository
from repositories.subscription_repository import SQLSubscriptionRepository
from db.models import VPNUsers, YPayments, CPayments
from utils.logging_config import get_logger

logger = get_logger(__name__)

T = TypeVar('T')


class BaseService(ABC):
    """
    Базовый абстрактный класс для всех сервисов.
    Определяет общие принципы работы сервисов.
    """
    
    def __init__(self, container: 'ServiceContainer' = None):
        """
        Инициализирует сервис.
        
        Args:
            container: Контейнер зависимостей
        """
        self.container = container
        self.logger = get_logger(self.__class__.__name__)
    
    @abstractmethod
    async def initialize(self) -> None:
        """Инициализация сервиса."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Очистка ресурсов сервиса."""
        pass


class ServiceContainer:
    """
    Контейнер зависимостей для Service Layer.
    Реализует Dependency Injection pattern.
    """
    
    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._singletons: Dict[Type, Any] = {}
        self._factories: Dict[Type, callable] = {}
        self._initialized = False
    
    def register_singleton(self, service_type: Type[T], instance: T) -> None:
        """
        Регистрирует singleton сервис.
        
        Args:
            service_type: Тип сервиса
            instance: Экземпляр сервиса
        """
        self._singletons[service_type] = instance
        logger.debug(f"Registered singleton service: {service_type.__name__}")
    
    def register_factory(self, service_type: Type[T], factory: callable) -> None:
        """
        Регистрирует фабрику для создания сервиса.
        
        Args:
            service_type: Тип сервиса
            factory: Фабричная функция
        """
        self._factories[service_type] = factory
        logger.debug(f"Registered factory for service: {service_type.__name__}")
    
    def register_transient(self, service_type: Type[T], implementation: Type[T]) -> None:
        """
        Регистрирует transient сервис (создается каждый раз новый).
        
        Args:
            service_type: Тип сервиса
            implementation: Класс реализации
        """
        self._services[service_type] = implementation
        logger.debug(f"Registered transient service: {service_type.__name__}")
    
    def get_service(self, service_type: Type[T]) -> T:
        """
        Получает экземпляр сервиса.
        
        Args:
            service_type: Тип сервиса
            
        Returns:
            Экземпляр сервиса
        """
        # Проверяем singleton
        if service_type in self._singletons:
            return self._singletons[service_type]
        
        # Проверяем фабрику
        if service_type in self._factories:
            factory = self._factories[service_type]
            instance = factory(self)
            return instance
        
        # Проверяем transient
        if service_type in self._services:
            implementation = self._services[service_type]
            instance = implementation(self)
            return instance
        
        raise ValueError(f"Service {service_type.__name__} not registered")
    
    async def initialize_all(self) -> None:
        """Инициализирует все зарегистрированные сервисы."""
        if self._initialized:
            return
        
        logger.info("Initializing service container...")
        
        # Инициализируем singleton сервисы
        for service_type, instance in self._singletons.items():
            if hasattr(instance, 'initialize'):
                await instance.initialize()
                logger.debug(f"Initialized singleton service: {service_type.__name__}")
        
        self._initialized = True
        logger.info("Service container initialized successfully")
    
    async def cleanup_all(self) -> None:
        """Очищает все сервисы."""
        if not self._initialized:
            return
        
        logger.info("Cleaning up service container...")
        
        # Очищаем singleton сервисы
        for service_type, instance in self._singletons.items():
            if hasattr(instance, 'cleanup'):
                try:
                    await instance.cleanup()
                    logger.debug(f"Cleaned up singleton service: {service_type.__name__}")
                except Exception as e:
                    logger.error(f"Error cleaning up service {service_type.__name__}: {e}")
        
        self._initialized = False
        logger.info("Service container cleaned up")
    
    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику контейнера."""
        return {
            'initialized': self._initialized,
            'singletons_count': len(self._singletons),
            'factories_count': len(self._factories),
            'transients_count': len(self._services),
            'registered_services': [
                service_type.__name__ 
                for service_type in list(self._singletons.keys()) + 
                                   list(self._factories.keys()) + 
                                   list(self._services.keys())
            ]
        }


class ServiceWithRepository(BaseService):
    """
    Базовый класс для сервисов, работающих с Repository.
    Предоставляет удобные методы для работы с UnitOfWork.
    """
    
    @asynccontextmanager
    async def get_unit_of_work(self):
        """
        Получает Unit of Work для выполнения операций с БД.
        
        Yields:
            UnitOfWork экземпляр
        """
        async with UnitOfWork() as uow:
            try:
                yield uow
            except Exception as e:
                logger.error(f"Error in {self.__class__.__name__} UnitOfWork: {e}")
                raise
    
    @asynccontextmanager
    async def get_user_repository(self):
        """Получает UserRepository с автоматическим управлением сессией."""
        async with self.get_unit_of_work() as uow:
            repo = uow.get_repository(SQLUserRepository, VPNUsers)
            yield repo
    
    @asynccontextmanager
    async def get_payment_repository(self):
        """Получает PaymentRepository с автоматическим управлением сессией."""
        async with self.get_unit_of_work() as uow:
            repo = uow.get_repository(SQLPaymentRepository)
            yield repo
    
    @asynccontextmanager
    async def get_subscription_repository(self):
        """Получает SubscriptionRepository с автоматическим управлением сессией."""
        async with self.get_unit_of_work() as uow:
            repo = uow.get_repository(SQLSubscriptionRepository)
            yield repo


# Глобальный контейнер сервисов
_service_container: Optional[ServiceContainer] = None


def get_service_container() -> ServiceContainer:
    """
    Получает глобальный контейнер сервисов.
    
    Returns:
        ServiceContainer экземпляр
    """
    global _service_container
    if _service_container is None:
        _service_container = ServiceContainer()
    return _service_container


def _create_preference_service():
    """Создает экземпляр UserPreferenceService с зависимостями."""
    # Пока что возвращаем None, реализуем позже
    return None


def _create_node_selection_service():
    """Создает экземпляр NodeSelectionService с зависимостями."""
    # Пока что возвращаем None, реализуем позже
    return None


def setup_service_container() -> ServiceContainer:
    """
    Настраивает и возвращает контейнер сервисов с зарегистрированными сервисами.
    
    Returns:
        Настроенный ServiceContainer
    """
    container = get_service_container()
    
    # Регистрируем сервисы как фабрики для lazy loading
    from .user_service import UserService
    from .payment_service import PaymentService
    from .subscription_service import SubscriptionService
    from .notification_service import NotificationService
    from .user_preference_service import UserPreferenceService
    from .node_selection_service import NodeSelectionService

    container.register_factory(UserService, lambda c: UserService(c))
    container.register_factory(PaymentService, lambda c: PaymentService(c))
    container.register_factory(SubscriptionService, lambda c: SubscriptionService(c))
    container.register_factory(NotificationService, lambda c: NotificationService(c))
    container.register_factory(UserPreferenceService, lambda c: _create_preference_service())
    container.register_factory(NodeSelectionService, lambda c: _create_node_selection_service())
    
    logger.info("Service container configured with all services")
    return container


async def initialize_services() -> None:
    """Инициализирует все сервисы."""
    global _service_container
    _service_container = setup_service_container()
    await _service_container.initialize_all()


async def cleanup_services() -> None:
    """Очищает все сервисы."""
    container = get_service_container()
    await container.cleanup_all()


# Декоратор для автоматического внедрения зависимостей
def inject_service(service_type: Type[T]):
    """
    Декоратор для автоматического внедрения сервиса.
    
    Args:
        service_type: Тип сервиса для внедрения
        
    Returns:
        Декоратор функции
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            container = get_service_container()
            service = container.get_service(service_type)
            return await func(service, *args, **kwargs)
        return wrapper
    return decorator
