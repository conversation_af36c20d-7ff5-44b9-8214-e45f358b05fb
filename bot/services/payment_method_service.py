"""
Сервис для управления сохраненными платежными методами.

Этот модуль содержит бизнес-логику для работы с сохраненными
платежными методами, автопродлением подписок и безопасностью.
"""

import hashlib
import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

from repositories.payment_method_repository import PaymentMethodRepositoryInterface
from db.models_payment_methods import (
    SavedPaymentMethod, AutoRenewalSettings, PaymentMethodUsageLog,
    AutoRenewalQueue, PaymentMethodSecurityLog,
    PaymentMethodType, PaymentMethodStatus, AutoRenewalStatus
)
from schemas.payment_methods import (
    SavedPaymentMethodCreate, SavedPaymentMethodUpdate, SavedPaymentMethodResponse,
    AutoRenewalSettingsCreate, AutoRenewalSettingsUpdate,
    PaymentMethodTestResult, UserPaymentMethodsResponse
)
from utils.encryption import encrypt_data, decrypt_data
from utils.logging_config import get_logger

logger = get_logger(__name__)

# Импортируем generate_id в начале файла
try:
    from utils.id_generator import generate_id
except ImportError:
    # Fallback если модуль не найден
    import uuid
    def generate_id():
        return str(uuid.uuid4()).replace("-", "")[:32]


@dataclass
class PaymentMethodOperationResult:
    """Результат операции с платежным методом."""
    success: bool
    payment_method: Optional[SavedPaymentMethod] = None
    error_message: Optional[str] = None
    operation_id: Optional[str] = None


class PaymentMethodService:
    """Сервис для управления сохраненными платежными методами."""
    
    def __init__(self, payment_method_repo: PaymentMethodRepositoryInterface):
        self.payment_method_repo = payment_method_repo
    
    async def save_payment_method(self, user_id: int, 
                                payment_data: SavedPaymentMethodCreate,
                                raw_payment_data: Dict[str, Any]) -> PaymentMethodOperationResult:
        """
        Сохраняет новый платежный метод пользователя.
        
        Args:
            user_id: ID пользователя
            payment_data: Данные для создания платежного метода
            raw_payment_data: Сырые данные платежного метода для шифрования
            
        Returns:
            Результат операции
        """
        try:
            # Шифруем данные платежного метода
            encrypted_data = encrypt_data(json.dumps(raw_payment_data))
            
            # Создаем хеш для идентификации
            data_hash = self._create_data_hash(raw_payment_data)
            
            # Проверяем, не существует ли уже такой платежный метод
            existing_method = await self.payment_method_repo.get_payment_method_by_hash(data_hash)
            if existing_method and existing_method.user_id == user_id:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Payment method already exists"
                )
            
            # Генерируем отображаемые данные
            display_name, masked_data = self._generate_display_data(
                payment_data.payment_type, raw_payment_data
            )
            
            # Создаем объект платежного метода
            payment_method = SavedPaymentMethod(
                id=generate_id(),
                user_id=user_id,
                payment_type=PaymentMethodType(payment_data.payment_type),
                encrypted_data=encrypted_data,
                data_hash=data_hash,
                display_name=payment_data.display_name or display_name,
                masked_data=masked_data,
                is_default=payment_data.is_default,
                provider_payment_method_id=payment_data.provider_payment_method_id,
                expires_at=payment_data.expires_at
            )
            
            # Если это первый платежный метод пользователя, делаем его по умолчанию
            user_methods = await self.payment_method_repo.get_user_payment_methods(user_id)
            if not user_methods:
                payment_method.is_default = True
            
            # Сохраняем в базе данных
            saved_method = await self.payment_method_repo.create_payment_method(payment_method)
            
            # Если установлен как default, обновляем остальные методы
            if payment_method.is_default:
                await self.payment_method_repo.set_default_payment_method(user_id, saved_method.id)
            
            # Логируем операцию
            await self._log_payment_method_operation(
                user_id, saved_method.id, "create", "success"
            )
            
            logger.info(f"Saved payment method {saved_method.id} for user {user_id}")
            
            return PaymentMethodOperationResult(
                success=True,
                payment_method=saved_method,
                operation_id=saved_method.id
            )
            
        except Exception as e:
            logger.error(f"Failed to save payment method for user {user_id}: {e}")
            await self._log_payment_method_operation(
                user_id, None, "create", "failure", str(e)
            )
            return PaymentMethodOperationResult(
                success=False,
                error_message=str(e)
            )
    
    async def get_user_payment_methods(self, user_id: int) -> UserPaymentMethodsResponse:
        """
        Получает все платежные методы пользователя с настройками автопродления.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Информация о платежных методах пользователя
        """
        try:
            # Получаем платежные методы
            payment_methods = await self.payment_method_repo.get_user_payment_methods(user_id)
            
            # Получаем настройки автопродления
            auto_renewal_settings = await self.payment_method_repo.get_auto_renewal_settings(user_id)
            auto_renewal_list = [auto_renewal_settings] if auto_renewal_settings else []
            
            # Находим метод по умолчанию
            default_method_id = None
            for method in payment_methods:
                if method.is_default:
                    default_method_id = method.id
                    break
            
            # Подсчитываем статистику
            active_count = sum(1 for method in payment_methods if method.is_active)
            
            return UserPaymentMethodsResponse(
                payment_methods=[
                    SavedPaymentMethodResponse.from_orm(method) 
                    for method in payment_methods
                ],
                auto_renewal_settings=auto_renewal_list,
                default_payment_method_id=default_method_id,
                total_count=len(payment_methods),
                active_count=active_count
            )
            
        except Exception as e:
            logger.error(f"Failed to get payment methods for user {user_id}: {e}")
            raise
    
    async def update_payment_method(self, user_id: int, method_id: str,
                                  update_data: SavedPaymentMethodUpdate) -> PaymentMethodOperationResult:
        """
        Обновляет платежный метод пользователя.
        
        Args:
            user_id: ID пользователя
            method_id: ID платежного метода
            update_data: Данные для обновления
            
        Returns:
            Результат операции
        """
        try:
            # Проверяем, что метод принадлежит пользователю
            existing_method = await self.payment_method_repo.get_payment_method_by_id(method_id)
            if not existing_method or existing_method.user_id != user_id:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Payment method not found or access denied"
                )
            
            # Подготавливаем данные для обновления
            update_dict = {}
            if update_data.display_name is not None:
                update_dict['display_name'] = update_data.display_name
            if update_data.is_active is not None:
                update_dict['is_active'] = update_data.is_active
            if update_data.status is not None:
                update_dict['status'] = PaymentMethodStatus(update_data.status)
            
            # Обрабатываем установку по умолчанию
            if update_data.is_default is True:
                await self.payment_method_repo.set_default_payment_method(user_id, method_id)
            elif update_data.is_default is False:
                update_dict['is_default'] = False
            
            # Обновляем метод
            if update_dict:
                updated_method = await self.payment_method_repo.update_payment_method(
                    method_id, update_dict
                )
            else:
                updated_method = existing_method
            
            # Логируем операцию
            await self._log_payment_method_operation(
                user_id, method_id, "update", "success"
            )
            
            logger.info(f"Updated payment method {method_id} for user {user_id}")
            
            return PaymentMethodOperationResult(
                success=True,
                payment_method=updated_method,
                operation_id=method_id
            )
            
        except Exception as e:
            logger.error(f"Failed to update payment method {method_id} for user {user_id}: {e}")
            await self._log_payment_method_operation(
                user_id, method_id, "update", "failure", str(e)
            )
            return PaymentMethodOperationResult(
                success=False,
                error_message=str(e)
            )
    
    async def delete_payment_method(self, user_id: int, method_id: str) -> PaymentMethodOperationResult:
        """
        Удаляет платежный метод пользователя.
        
        Args:
            user_id: ID пользователя
            method_id: ID платежного метода
            
        Returns:
            Результат операции
        """
        try:
            # Проверяем, что метод принадлежит пользователю
            existing_method = await self.payment_method_repo.get_payment_method_by_id(method_id)
            if not existing_method or existing_method.user_id != user_id:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Payment method not found or access denied"
                )
            
            # Логируем событие безопасности
            await self._log_security_event(
                user_id, method_id, "deletion", "info",
                f"User requested deletion of payment method {method_id}"
            )
            
            # Удаляем метод
            deleted = await self.payment_method_repo.delete_payment_method(method_id)
            
            if deleted:
                # Логируем операцию
                await self._log_payment_method_operation(
                    user_id, method_id, "delete", "success"
                )
                
                logger.info(f"Deleted payment method {method_id} for user {user_id}")
                
                return PaymentMethodOperationResult(
                    success=True,
                    operation_id=method_id
                )
            else:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Failed to delete payment method"
                )
            
        except Exception as e:
            logger.error(f"Failed to delete payment method {method_id} for user {user_id}: {e}")
            await self._log_payment_method_operation(
                user_id, method_id, "delete", "failure", str(e)
            )
            return PaymentMethodOperationResult(
                success=False,
                error_message=str(e)
            )
    
    # Приватные методы
    
    def _create_data_hash(self, payment_data: Dict[str, Any]) -> str:
        """Создает хеш для идентификации платежных данных."""
        # Сортируем ключи для консистентности
        sorted_data = json.dumps(payment_data, sort_keys=True)
        return hashlib.sha256(sorted_data.encode()).hexdigest()
    
    def _generate_display_data(self, payment_type: str, 
                             raw_data: Dict[str, Any]) -> tuple[str, str]:
        """Генерирует отображаемые данные для платежного метода."""
        if payment_type == "yookassa":
            card_type = raw_data.get('card_type', 'Card')
            last4 = raw_data.get('card_last4', '****')
            display_name = f"{card_type} ****{last4}"
            masked_data = f"****{last4}"
        elif payment_type == "cryptomus":
            currency = raw_data.get('currency', 'CRYPTO')
            wallet = raw_data.get('wallet_address', '')
            masked_wallet = f"{wallet[:6]}...{wallet[-4:]}" if len(wallet) > 10 else wallet
            display_name = f"{currency} {masked_wallet}"
            masked_data = masked_wallet
        elif payment_type == "telegram_stars":
            display_name = "Telegram Stars"
            masked_data = "⭐ Stars"
        else:
            display_name = f"{payment_type.title()} Payment"
            masked_data = "****"
        
        return display_name, masked_data
    
    async def _log_payment_method_operation(self, user_id: int, method_id: Optional[str],
                                          operation: str, result: str,
                                          error_message: Optional[str] = None):
        """Логирует операцию с платежным методом."""
        try:
            log = PaymentMethodUsageLog(
                id=generate_id(),
                user_id=user_id,
                payment_method_id=method_id or "unknown",
                operation_type=operation,
                operation_result=result,
                error_message=error_message
            )
            await self.payment_method_repo.log_payment_method_usage(log)
        except Exception as e:
            logger.error(f"Failed to log payment method operation: {e}")
    
    async def _log_security_event(self, user_id: int, method_id: Optional[str],
                                event_type: str, severity: str, description: str):
        """Логирует событие безопасности."""
        try:
            security_log = PaymentMethodSecurityLog(
                id=generate_id(),
                user_id=user_id,
                payment_method_id=method_id,
                event_type=event_type,
                severity=severity,
                description=description
            )
            await self.payment_method_repo.log_security_event(security_log)
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")

    # Методы для автопродления

    async def setup_auto_renewal(self, user_id: int,
                               settings_data: AutoRenewalSettingsCreate) -> PaymentMethodOperationResult:
        """
        Настраивает автопродление подписки для пользователя.

        Args:
            user_id: ID пользователя
            settings_data: Настройки автопродления

        Returns:
            Результат операции
        """
        try:
            # Проверяем, что платежный метод принадлежит пользователю
            payment_method = await self.payment_method_repo.get_payment_method_by_id(
                settings_data.payment_method_id
            )
            if not payment_method or payment_method.user_id != user_id:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Payment method not found or access denied"
                )

            # Проверяем, нет ли уже настроек автопродления
            existing_settings = await self.payment_method_repo.get_auto_renewal_settings(user_id)
            if existing_settings:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Auto renewal already configured"
                )

            # Создаем настройки автопродления
            auto_renewal = AutoRenewalSettings(
                id=generate_id(),
                user_id=user_id,
                payment_method_id=settings_data.payment_method_id,
                renewal_days_before=settings_data.renewal_days_before,
                max_retry_attempts=settings_data.max_retry_attempts,
                notify_on_success=settings_data.notify_on_success,
                notify_on_failure=settings_data.notify_on_failure,
                notify_before_renewal=settings_data.notify_before_renewal
            )

            # Сохраняем настройки
            saved_settings = await self.payment_method_repo.create_auto_renewal_settings(auto_renewal)

            # Логируем операцию
            await self._log_payment_method_operation(
                user_id, settings_data.payment_method_id, "setup_auto_renewal", "success"
            )

            logger.info(f"Setup auto renewal for user {user_id} with payment method {settings_data.payment_method_id}")

            return PaymentMethodOperationResult(
                success=True,
                operation_id=saved_settings.id
            )

        except Exception as e:
            logger.error(f"Failed to setup auto renewal for user {user_id}: {e}")
            await self._log_payment_method_operation(
                user_id, settings_data.payment_method_id, "setup_auto_renewal", "failure", str(e)
            )
            return PaymentMethodOperationResult(
                success=False,
                error_message=str(e)
            )

    async def update_auto_renewal_settings(self, user_id: int,
                                         update_data: AutoRenewalSettingsUpdate) -> PaymentMethodOperationResult:
        """
        Обновляет настройки автопродления.

        Args:
            user_id: ID пользователя
            update_data: Данные для обновления

        Returns:
            Результат операции
        """
        try:
            # Получаем существующие настройки
            existing_settings = await self.payment_method_repo.get_auto_renewal_settings(user_id)
            if not existing_settings:
                return PaymentMethodOperationResult(
                    success=False,
                    error_message="Auto renewal settings not found"
                )

            # Подготавливаем данные для обновления
            update_dict = {}
            if update_data.status is not None:
                update_dict['status'] = AutoRenewalStatus(update_data.status)
            if update_data.is_enabled is not None:
                update_dict['is_enabled'] = update_data.is_enabled
            if update_data.renewal_days_before is not None:
                update_dict['renewal_days_before'] = update_data.renewal_days_before
            if update_data.max_retry_attempts is not None:
                update_dict['max_retry_attempts'] = update_data.max_retry_attempts
            if update_data.notify_on_success is not None:
                update_dict['notify_on_success'] = update_data.notify_on_success
            if update_data.notify_on_failure is not None:
                update_dict['notify_on_failure'] = update_data.notify_on_failure
            if update_data.notify_before_renewal is not None:
                update_dict['notify_before_renewal'] = update_data.notify_before_renewal

            # Обновляем настройки
            updated_settings = await self.payment_method_repo.update_auto_renewal_settings(
                existing_settings.id, update_dict
            )

            # Логируем операцию
            await self._log_payment_method_operation(
                user_id, existing_settings.payment_method_id, "update_auto_renewal", "success"
            )

            logger.info(f"Updated auto renewal settings for user {user_id}")

            return PaymentMethodOperationResult(
                success=True,
                operation_id=existing_settings.id
            )

        except Exception as e:
            logger.error(f"Failed to update auto renewal settings for user {user_id}: {e}")
            await self._log_payment_method_operation(
                user_id, None, "update_auto_renewal", "failure", str(e)
            )
            return PaymentMethodOperationResult(
                success=False,
                error_message=str(e)
            )

    async def test_payment_method(self, user_id: int, method_id: str) -> PaymentMethodTestResult:
        """
        Тестирует платежный метод на работоспособность.

        Args:
            user_id: ID пользователя
            method_id: ID платежного метода

        Returns:
            Результат тестирования
        """
        try:
            start_time = datetime.utcnow()

            # Проверяем, что метод принадлежит пользователю
            payment_method = await self.payment_method_repo.get_payment_method_by_id(method_id)
            if not payment_method or payment_method.user_id != user_id:
                return PaymentMethodTestResult(
                    success=False,
                    message="Payment method not found or access denied",
                    response_time_ms=0
                )

            # Расшифровываем данные платежного метода
            try:
                decrypted_data = decrypt_data(payment_method.encrypted_data)
                payment_data = json.loads(decrypted_data)
            except Exception as e:
                return PaymentMethodTestResult(
                    success=False,
                    message="Failed to decrypt payment method data",
                    response_time_ms=0,
                    error_details={"decryption_error": str(e)}
                )

            # Выполняем тест в зависимости от типа платежного метода
            if payment_method.payment_type == PaymentMethodType.YOOKASSA:
                test_result = await self._test_yookassa_method(payment_data)
            elif payment_method.payment_type == PaymentMethodType.CRYPTOMUS:
                test_result = await self._test_cryptomus_method(payment_data)
            elif payment_method.payment_type == PaymentMethodType.TELEGRAM_STARS:
                test_result = await self._test_telegram_stars_method(payment_data)
            else:
                test_result = PaymentMethodTestResult(
                    success=False,
                    message="Unsupported payment method type",
                    response_time_ms=0
                )

            # Вычисляем время ответа
            end_time = datetime.utcnow()
            response_time = int((end_time - start_time).total_seconds() * 1000)
            test_result.response_time_ms = response_time

            # Обновляем время последнего использования
            if test_result.success:
                await self.payment_method_repo.update_payment_method(
                    method_id, {'last_used_at': datetime.utcnow()}
                )

            # Логируем операцию
            await self._log_payment_method_operation(
                user_id, method_id, "test",
                "success" if test_result.success else "failure",
                test_result.message if not test_result.success else None
            )

            return test_result

        except Exception as e:
            logger.error(f"Failed to test payment method {method_id} for user {user_id}: {e}")
            return PaymentMethodTestResult(
                success=False,
                message=f"Test failed: {str(e)}",
                response_time_ms=0,
                error_details={"exception": str(e)}
            )

    async def _test_yookassa_method(self, payment_data: Dict[str, Any]) -> PaymentMethodTestResult:
        """Тестирует платежный метод YooKassa."""
        # TODO: Реализовать тестирование через YooKassa API
        # Пока возвращаем заглушку
        return PaymentMethodTestResult(
            success=True,
            message="YooKassa payment method is valid",
            test_transaction_id="test_" + generate_id()[:8]
        )

    async def _test_cryptomus_method(self, payment_data: Dict[str, Any]) -> PaymentMethodTestResult:
        """Тестирует платежный метод Cryptomus."""
        # TODO: Реализовать тестирование через Cryptomus API
        # Пока возвращаем заглушку
        return PaymentMethodTestResult(
            success=True,
            message="Cryptomus payment method is valid",
            test_transaction_id="test_" + generate_id()[:8]
        )

    async def _test_telegram_stars_method(self, payment_data: Dict[str, Any]) -> PaymentMethodTestResult:
        """Тестирует платежный метод Telegram Stars."""
        # TODO: Реализовать тестирование через Telegram Bot API
        # Пока возвращаем заглушку
        return PaymentMethodTestResult(
            success=True,
            message="Telegram Stars payment method is valid",
            test_transaction_id="test_" + generate_id()[:8]
        )
