"""
Сервис для управления предпочтениями пользователей.
Реализует бизнес-логику работы с предпочтениями по странам и протоколам.
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime

from repositories.country_node_repository import (
    CountryRepository, MarzbanNodeRepository, UserNodePreferenceRepository
)
from db.models_countries_nodes import UserNodePreference, Country, MarzbanNode
from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class UserPreferences:
    """DTO для предпочтений пользователя."""
    tg_id: int
    preferred_countries: List[str]
    preferred_protocols: List[str]
    auto_select_optimal: bool = True
    prefer_low_latency: bool = True
    prefer_low_load: bool = True
    last_selected_country: Optional[str] = None
    last_selected_node: Optional[str] = None


@dataclass
class CountryInfo:
    """DTO для информации о стране."""
    id: str
    name: str
    name_ru: str
    flag: str
    continent: str
    region: str
    active_nodes_count: int
    is_available: bool


class UserPreferenceService:
    """Сервис для управления предпочтениями пользователей."""
    
    def __init__(self,
                 preference_repo: UserNodePreferenceRepository,
                 country_repo: CountryRepository,
                 node_repo: MarzbanNodeRepository):
        self.preference_repo = preference_repo
        self.country_repo = country_repo
        self.node_repo = node_repo
    
    async def get_user_preferences(self, tg_id: int) -> UserPreferences:
        """Получить предпочтения пользователя."""
        preference = await self.preference_repo.get_by_telegram_id(tg_id)
        
        if preference:
            return UserPreferences(
                tg_id=preference.tg_id,
                preferred_countries=preference.preferred_countries or [],
                preferred_protocols=preference.preferred_protocols or ["vless", "vmess"],
                auto_select_optimal=preference.auto_select_optimal,
                prefer_low_latency=preference.prefer_low_latency,
                prefer_low_load=preference.prefer_low_load,
                last_selected_country=preference.last_selected_country,
                last_selected_node=preference.last_selected_node
            )
        else:
            # Возвращаем дефолтные предпочтения
            return UserPreferences(
                tg_id=tg_id,
                preferred_countries=[],
                preferred_protocols=["vless", "vmess"],
                auto_select_optimal=True,
                prefer_low_latency=True,
                prefer_low_load=True
            )
    
    async def update_user_preferences(self, preferences: UserPreferences) -> bool:
        """Обновить предпочтения пользователя."""
        try:
            # Валидируем страны
            if preferences.preferred_countries:
                valid_countries = await self._validate_countries(preferences.preferred_countries)
                preferences.preferred_countries = valid_countries
            
            # Валидируем протоколы
            preferences.preferred_protocols = self._validate_protocols(preferences.preferred_protocols)
            
            # Сохраняем в БД
            await self.preference_repo.create_or_update_preferences(
                tg_id=preferences.tg_id,
                preferences={
                    'preferred_countries': preferences.preferred_countries,
                    'preferred_protocols': preferences.preferred_protocols,
                    'auto_select_optimal': preferences.auto_select_optimal,
                    'prefer_low_latency': preferences.prefer_low_latency,
                    'prefer_low_load': preferences.prefer_low_load,
                    'last_selected_country': preferences.last_selected_country,
                    'last_selected_node': preferences.last_selected_node
                }
            )
            
            logger.info(f"Updated preferences for user {preferences.tg_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating preferences for user {preferences.tg_id}: {e}")
            return False
    
    async def add_preferred_country(self, tg_id: int, country_id: str) -> bool:
        """Добавить страну в предпочтения."""
        try:
            # Валидируем страну
            country = await self.country_repo.get_by_iso_code(country_id)
            if not country or not country.is_active:
                logger.warning(f"Invalid or inactive country: {country_id}")
                return False
            
            preferences = await self.get_user_preferences(tg_id)
            
            # Добавляем страну если её нет
            if country_id.upper() not in preferences.preferred_countries:
                preferences.preferred_countries.append(country_id.upper())
                await self.update_user_preferences(preferences)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding preferred country {country_id} for user {tg_id}: {e}")
            return False
    
    async def remove_preferred_country(self, tg_id: int, country_id: str) -> bool:
        """Удалить страну из предпочтений."""
        try:
            preferences = await self.get_user_preferences(tg_id)
            
            # Удаляем страну если она есть
            if country_id.upper() in preferences.preferred_countries:
                preferences.preferred_countries.remove(country_id.upper())
                await self.update_user_preferences(preferences)
            
            return True
            
        except Exception as e:
            logger.error(f"Error removing preferred country {country_id} for user {tg_id}: {e}")
            return False
    
    async def set_preferred_protocols(self, tg_id: int, protocols: List[str]) -> bool:
        """Установить предпочитаемые протоколы."""
        try:
            # Валидируем протоколы
            valid_protocols = self._validate_protocols(protocols)
            if not valid_protocols:
                logger.warning(f"No valid protocols provided: {protocols}")
                return False
            
            preferences = await self.get_user_preferences(tg_id)
            preferences.preferred_protocols = valid_protocols
            
            await self.update_user_preferences(preferences)
            return True
            
        except Exception as e:
            logger.error(f"Error setting preferred protocols for user {tg_id}: {e}")
            return False
    
    async def update_last_selection(self, tg_id: int, country_id: str = None, node_id: str = None) -> bool:
        """Обновить последний выбор пользователя."""
        try:
            return await self.preference_repo.update_last_selection(tg_id, country_id, node_id)
        except Exception as e:
            logger.error(f"Error updating last selection for user {tg_id}: {e}")
            return False
    
    async def get_available_countries(self) -> List[CountryInfo]:
        """Получить список доступных стран с информацией о нодах."""
        try:
            countries = await self.country_repo.get_countries_with_active_nodes()
            result = []
            
            for country in countries:
                # Подсчитываем активные ноды
                active_nodes = await self.node_repo.get_nodes_by_country(country.id)
                
                country_info = CountryInfo(
                    id=country.id,
                    name=country.name,
                    name_ru=country.name_ru,
                    flag=country.flag,
                    continent=country.continent or "",
                    region=country.region or "",
                    active_nodes_count=len(active_nodes),
                    is_available=len(active_nodes) > 0
                )
                result.append(country_info)
            
            # Сортируем по приоритету и количеству нод
            result.sort(key=lambda x: (-x.active_nodes_count, x.name))
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting available countries: {e}")
            return []
    
    async def get_recommended_countries(self, tg_id: int, limit: int = 5) -> List[CountryInfo]:
        """Получить рекомендуемые страны для пользователя."""
        try:
            preferences = await self.get_user_preferences(tg_id)
            all_countries = await self.get_available_countries()
            
            # Если у пользователя есть предпочтения, показываем их первыми
            if preferences.preferred_countries:
                preferred = [c for c in all_countries if c.id in preferences.preferred_countries]
                other = [c for c in all_countries if c.id not in preferences.preferred_countries]
                
                # Объединяем: сначала предпочитаемые, потом остальные
                recommended = preferred + other[:limit - len(preferred)]
            else:
                # Если предпочтений нет, показываем страны с наибольшим количеством нод
                recommended = all_countries[:limit]
            
            return recommended
            
        except Exception as e:
            logger.error(f"Error getting recommended countries for user {tg_id}: {e}")
            return []
    
    async def _validate_countries(self, country_ids: List[str]) -> List[str]:
        """Валидировать список стран."""
        valid_countries = []
        
        for country_id in country_ids:
            country = await self.country_repo.get_by_iso_code(country_id)
            if country and country.is_active:
                valid_countries.append(country_id.upper())
            else:
                logger.warning(f"Invalid country ID: {country_id}")
        
        return valid_countries
    
    def _validate_protocols(self, protocols: List[str]) -> List[str]:
        """Валидировать список протоколов."""
        valid_protocols = ["vless", "vmess", "trojan", "shadowsocks"]
        result = []
        
        for protocol in protocols:
            if protocol.lower() in valid_protocols:
                result.append(protocol.lower())
            else:
                logger.warning(f"Invalid protocol: {protocol}")
        
        # Если нет валидных протоколов, возвращаем дефолтные
        if not result:
            result = ["vless", "vmess"]
        
        return result
