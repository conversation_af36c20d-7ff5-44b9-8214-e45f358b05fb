"""
Сервис для работы с шаблонами уведомлений.

Этот модуль содержит логику рендеринга шаблонов уведомлений
с использованием Jinja2 и валидации контекстных данных.
"""

import re
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from jinja2 import Environment, DictLoader, select_autoescape, TemplateError
from jinja2.sandbox import SandboxedEnvironment

from db.models_notifications import NotificationTemplate
from schemas.notification_enums import NotificationType, ABTestVariant
from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class RenderedNotification:
    """Результат рендеринга шаблона уведомления."""
    subject: Optional[str]
    body: str
    template_id: str
    ab_test_variant: ABTestVariant
    variables_used: List[str]
    render_time_ms: int


class TemplateRenderError(Exception):
    """Ошибка рендеринга шаблона."""
    pass


class TemplateValidationError(Exception):
    """Ошибка валидации шаблона."""
    pass


class TemplateService:
    """Сервис для работы с шаблонами уведомлений."""
    
    def __init__(self):
        # Используем SandboxedEnvironment для безопасности
        self.jinja_env = SandboxedEnvironment(
            loader=DictLoader({}),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Добавляем кастомные фильтры
        self._register_custom_filters()
    
    def _register_custom_filters(self):
        """Регистрирует кастомные фильтры для Jinja2."""
        
        def format_datetime(value: datetime, format_str: str = "%d.%m.%Y %H:%M") -> str:
            """Форматирует дату и время."""
            if isinstance(value, datetime):
                return value.strftime(format_str)
            return str(value)
        
        def format_currency(value: float, currency: str = "₽") -> str:
            """Форматирует денежную сумму."""
            return f"{value:.2f} {currency}"
        
        def pluralize(count: int, forms: str) -> str:
            """Склоняет слова по числам (для русского языка)."""
            forms_list = forms.split(',')
            if len(forms_list) != 3:
                return forms_list[0] if forms_list else ""
            
            if count % 10 == 1 and count % 100 != 11:
                return forms_list[0]  # 1 день
            elif 2 <= count % 10 <= 4 and (count % 100 < 10 or count % 100 >= 20):
                return forms_list[1]  # 2-4 дня
            else:
                return forms_list[2]  # 5+ дней
        
        def truncate_text(text: str, length: int = 100, suffix: str = "...") -> str:
            """Обрезает текст до указанной длины."""
            if len(text) <= length:
                return text
            return text[:length - len(suffix)] + suffix
        
        # Регистрируем фильтры
        self.jinja_env.filters['datetime'] = format_datetime
        self.jinja_env.filters['currency'] = format_currency
        self.jinja_env.filters['pluralize'] = pluralize
        self.jinja_env.filters['truncate'] = truncate_text
    
    async def render_template(self, template: NotificationTemplate, 
                            context: Dict[str, Any]) -> RenderedNotification:
        """
        Рендерит шаблон уведомления с контекстными данными.
        
        Args:
            template: Шаблон уведомления
            context: Контекстные данные для подстановки
            
        Returns:
            Отрендеренное уведомление
            
        Raises:
            TemplateRenderError: При ошибке рендеринга
        """
        start_time = datetime.utcnow()
        
        try:
            # Валидируем контекст
            validated_context = self._validate_and_prepare_context(template, context)
            
            # Рендерим заголовок (если есть)
            rendered_subject = None
            if template.subject_template:
                subject_template = self.jinja_env.from_string(template.subject_template)
                rendered_subject = subject_template.render(**validated_context)
            
            # Рендерим тело сообщения
            body_template = self.jinja_env.from_string(template.body_template)
            rendered_body = body_template.render(**validated_context)
            
            # Извлекаем использованные переменные
            variables_used = self._extract_variables_from_templates(
                template.subject_template, template.body_template
            )
            
            # Вычисляем время рендеринга
            render_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            logger.info(f"Successfully rendered template {template.id} in {render_time:.2f}ms")
            
            return RenderedNotification(
                subject=rendered_subject,
                body=rendered_body,
                template_id=template.id,
                ab_test_variant=template.ab_test_variant,
                variables_used=variables_used,
                render_time_ms=int(render_time)
            )
            
        except TemplateError as e:
            logger.error(f"Jinja2 template error for template {template.id}: {e}")
            raise TemplateRenderError(f"Template rendering failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error rendering template {template.id}: {e}")
            raise TemplateRenderError(f"Unexpected rendering error: {str(e)}")
    
    def _validate_and_prepare_context(self, template: NotificationTemplate, 
                                    context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Валидирует и подготавливает контекст для рендеринга.
        
        Args:
            template: Шаблон уведомления
            context: Исходный контекст
            
        Returns:
            Валидированный и дополненный контекст
        """
        # Начинаем с копии исходного контекста
        validated_context = context.copy()
        
        # Добавляем значения по умолчанию из шаблона
        if template.default_values:
            for key, value in template.default_values.items():
                if key not in validated_context:
                    validated_context[key] = value
        
        # Добавляем системные переменные
        validated_context.update({
            'current_date': datetime.utcnow(),
            'template_id': template.id,
            'notification_type': template.notification_type.value
        })
        
        # Валидируем обязательные переменные
        if template.variables:
            for var_name, var_config in template.variables.items():
                if var_config.get('required', False) and var_name not in validated_context:
                    raise TemplateValidationError(f"Required variable '{var_name}' is missing")
        
        return validated_context
    
    def _extract_variables_from_templates(self, subject_template: Optional[str], 
                                        body_template: str) -> List[str]:
        """
        Извлекает список переменных, используемых в шаблонах.
        
        Args:
            subject_template: Шаблон заголовка
            body_template: Шаблон тела
            
        Returns:
            Список имен переменных
        """
        variables = set()
        
        # Регулярное выражение для поиска переменных Jinja2
        variable_pattern = r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s*(?:\|[^}]*)?\}\}'
        
        templates_to_check = [body_template]
        if subject_template:
            templates_to_check.append(subject_template)
        
        for template_text in templates_to_check:
            matches = re.findall(variable_pattern, template_text)
            for match in matches:
                # Берем только первую часть (до точки) для вложенных объектов
                var_name = match.split('.')[0]
                variables.add(var_name)
        
        return sorted(list(variables))
    
    async def validate_template(self, subject_template: Optional[str], 
                              body_template: str) -> Dict[str, Any]:
        """
        Валидирует синтаксис шаблонов.
        
        Args:
            subject_template: Шаблон заголовка
            body_template: Шаблон тела
            
        Returns:
            Результат валидации с информацией об ошибках
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'variables': []
        }
        
        try:
            # Проверяем синтаксис заголовка
            if subject_template:
                try:
                    self.jinja_env.from_string(subject_template)
                except TemplateError as e:
                    validation_result['is_valid'] = False
                    validation_result['errors'].append(f"Subject template error: {str(e)}")
            
            # Проверяем синтаксис тела
            try:
                self.jinja_env.from_string(body_template)
            except TemplateError as e:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"Body template error: {str(e)}")
            
            # Извлекаем переменные
            if validation_result['is_valid']:
                validation_result['variables'] = self._extract_variables_from_templates(
                    subject_template, body_template
                )
            
            # Проверяем на потенциальные проблемы
            self._check_template_warnings(subject_template, body_template, validation_result)
            
        except Exception as e:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    def _check_template_warnings(self, subject_template: Optional[str], 
                               body_template: str, validation_result: Dict[str, Any]):
        """Проверяет шаблоны на потенциальные проблемы."""
        warnings = []
        
        # Проверяем длину тела сообщения
        if len(body_template) > 4000:
            warnings.append("Body template is very long (>4000 chars), may cause Telegram message limits")
        
        # Проверяем на использование небезопасных фильтров
        unsafe_patterns = ['|safe', '|raw']
        for pattern in unsafe_patterns:
            if pattern in body_template or (subject_template and pattern in subject_template):
                warnings.append(f"Using potentially unsafe filter: {pattern}")
        
        # Проверяем на отсутствие переменных
        if not validation_result.get('variables'):
            warnings.append("Template doesn't use any variables, consider if this is intentional")
        
        validation_result['warnings'].extend(warnings)
    
    def get_available_filters(self) -> Dict[str, str]:
        """
        Возвращает список доступных фильтров с описанием.
        
        Returns:
            Словарь с именами фильтров и их описанием
        """
        return {
            'datetime': 'Форматирует дату и время (по умолчанию: %d.%m.%Y %H:%M)',
            'currency': 'Форматирует денежную сумму (по умолчанию: ₽)',
            'pluralize': 'Склоняет слова по числам (формат: "день,дня,дней")',
            'truncate': 'Обрезает текст до указанной длины',
            'upper': 'Преобразует в верхний регистр',
            'lower': 'Преобразует в нижний регистр',
            'title': 'Преобразует в заглавный регистр',
            'length': 'Возвращает длину строки или списка'
        }
