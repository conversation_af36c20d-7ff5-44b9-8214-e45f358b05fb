"""
MarzbanCountryNodeService - высокоуровневый сервис для работы со странами и нодами.
Интегрирует UserPreferenceService, NodeSelectionService и Marzban API.
"""

from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
# Простая реализация TTL кэша без внешних зависимостей
import time
from typing import Dict, Any, Tuple

class TTLCache:
    """Простая реализация TTL кэша."""

    def __init__(self, maxsize: int, ttl: int):
        self.maxsize = maxsize
        self.ttl = ttl
        self._cache: Dict[str, Tuple[Any, float]] = {}

    def __contains__(self, key: str) -> bool:
        if key in self._cache:
            value, timestamp = self._cache[key]
            if time.time() - timestamp < self.ttl:
                return True
            else:
                del self._cache[key]
        return False

    def __getitem__(self, key: str) -> Any:
        if key in self:
            return self._cache[key][0]
        raise KeyError(key)

    def __setitem__(self, key: str, value: Any):
        # Очищаем старые записи если кэш переполнен
        if len(self._cache) >= self.maxsize:
            self._cleanup()

        self._cache[key] = (value, time.time())

    def clear(self):
        """Очистить весь кэш."""
        self._cache.clear()

    def keys(self):
        """Получить ключи кэша."""
        return self._cache.keys()

    def pop(self, key: str, default=None):
        """Удалить и вернуть элемент."""
        return self._cache.pop(key, (default, 0))[0]

    def _cleanup(self):
        """Очистить устаревшие записи."""
        current_time = time.time()
        expired_keys = []

        for key, (value, timestamp) in self._cache.items():
            if current_time - timestamp >= self.ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._cache[key]

        # Если все еще переполнен, удаляем самые старые
        if len(self._cache) >= self.maxsize:
            sorted_items = sorted(self._cache.items(), key=lambda x: x[1][1])
            for key, _ in sorted_items[:len(self._cache) - self.maxsize + 1]:
                del self._cache[key]

    def __len__(self):
        return len(self._cache)

from services.user_preference_service import UserPreferenceService, CountryInfo
from services.node_selection_service import NodeSelectionService, NodeSelectionResult, SelectionStrategy
from repositories.country_node_repository import (
    CountryRepository, MarzbanNodeRepository, NodeStatisticsRepository
)
from db.models_countries_nodes import Country, MarzbanNode, NodeStatus
from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class NodeInfo:
    """DTO для информации о ноде."""
    id: str
    name: str
    country_id: str
    country_name: str
    country_flag: str
    city: str
    current_users: int
    max_users: int
    load_percentage: float
    cpu_usage: float
    memory_usage: float
    response_time_ms: float
    status: NodeStatus
    is_available: bool
    supported_protocols: List[str]


@dataclass
class CountryNodeInfo:
    """DTO для информации о стране с нодами."""
    country: CountryInfo
    nodes: List[NodeInfo]
    total_nodes: int
    available_nodes: int
    total_capacity: int
    current_load: int
    average_load_percentage: float
    best_node: Optional[NodeInfo]


@dataclass
class OptimalNodeResult:
    """Результат выбора оптимальной ноды."""
    success: bool
    selected_node: Optional[NodeInfo]
    fallback_nodes: List[NodeInfo]
    selection_strategy: str
    selection_reasons: List[str]
    total_candidates: int
    cache_hit: bool = False


class MarzbanCountryNodeService:
    """Высокоуровневый сервис для работы со странами и нодами."""
    
    def __init__(self,
                 preference_service: UserPreferenceService,
                 selection_service: NodeSelectionService,
                 country_repo: CountryRepository,
                 node_repo: MarzbanNodeRepository,
                 stats_repo: NodeStatisticsRepository):
        self.preference_service = preference_service
        self.selection_service = selection_service
        self.country_repo = country_repo
        self.node_repo = node_repo
        self.stats_repo = stats_repo
        
        # Кэш для результатов (TTL = 5 минут)
        self._countries_cache = TTLCache(maxsize=100, ttl=300)
        self._nodes_cache = TTLCache(maxsize=500, ttl=180)  # 3 минуты для нод
        self._selection_cache = TTLCache(maxsize=1000, ttl=60)  # 1 минута для выбора
    
    async def get_available_countries_with_nodes(self, 
                                               include_stats: bool = True,
                                               use_cache: bool = True) -> List[CountryNodeInfo]:
        """
        Получить список доступных стран с информацией о нодах.
        
        Args:
            include_stats: Включать статистику нод
            use_cache: Использовать кэширование
        """
        cache_key = f"countries_with_nodes_{include_stats}"
        
        if use_cache and cache_key in self._countries_cache:
            logger.debug("Cache hit for countries with nodes")
            return self._countries_cache[cache_key]
        
        try:
            # Получаем доступные страны
            countries = await self.preference_service.get_available_countries()
            result = []
            
            for country in countries:
                # Получаем ноды для страны
                nodes = await self.node_repo.get_nodes_by_country(country.id)
                
                if not nodes:
                    continue
                
                # Конвертируем в NodeInfo
                node_infos = []
                total_capacity = 0
                current_load = 0
                available_count = 0
                
                for node in nodes:
                    # Получаем статистику если нужно
                    if include_stats:
                        stats = await self.stats_repo.get_average_performance(node.id, hours=1)
                        cpu_usage = stats.get('avg_cpu_usage', node.cpu_usage or 0)
                        memory_usage = stats.get('avg_memory_usage', node.memory_usage or 0)
                        response_time = stats.get('avg_response_time_ms', 50)
                    else:
                        cpu_usage = node.cpu_usage or 0
                        memory_usage = node.memory_usage or 0
                        response_time = 50
                    
                    # Получаем поддерживаемые протоколы
                    inbounds = await self.node_repo.get_inbounds_by_node(node.id)
                    protocols = list(set([inbound.protocol for inbound in inbounds if inbound.is_active]))
                    
                    load_percentage = (node.current_users / node.max_users * 100) if node.max_users > 0 else 0
                    is_available = (node.status == NodeStatus.CONNECTED and 
                                  node.is_active and 
                                  load_percentage < 90)
                    
                    node_info = NodeInfo(
                        id=node.id,
                        name=node.name,
                        country_id=node.country_id,
                        country_name=country.name_ru,
                        country_flag=country.flag,
                        city=node.city or "",
                        current_users=node.current_users,
                        max_users=node.max_users,
                        load_percentage=load_percentage,
                        cpu_usage=cpu_usage,
                        memory_usage=memory_usage,
                        response_time_ms=response_time,
                        status=node.status,
                        is_available=is_available,
                        supported_protocols=protocols
                    )
                    
                    node_infos.append(node_info)
                    total_capacity += node.max_users
                    current_load += node.current_users
                    
                    if is_available:
                        available_count += 1
                
                # Находим лучшую ноду
                best_node = None
                if node_infos:
                    # Сортируем по доступности, затем по загрузке
                    sorted_nodes = sorted(
                        node_infos,
                        key=lambda x: (not x.is_available, x.load_percentage, x.response_time_ms)
                    )
                    best_node = sorted_nodes[0] if sorted_nodes else None
                
                avg_load = (current_load / total_capacity * 100) if total_capacity > 0 else 0
                
                country_node_info = CountryNodeInfo(
                    country=country,
                    nodes=node_infos,
                    total_nodes=len(nodes),
                    available_nodes=available_count,
                    total_capacity=total_capacity,
                    current_load=current_load,
                    average_load_percentage=avg_load,
                    best_node=best_node
                )
                
                result.append(country_node_info)
            
            # Сортируем по количеству доступных нод и средней загрузке
            result.sort(key=lambda x: (-x.available_nodes, x.average_load_percentage))
            
            # Кэшируем результат
            if use_cache:
                self._countries_cache[cache_key] = result
            
            logger.info(f"Retrieved {len(result)} countries with nodes")
            return result
            
        except Exception as e:
            logger.error(f"Error getting countries with nodes: {e}")
            return []
    
    async def get_optimal_node_for_user(self,
                                      tg_id: int,
                                      country_id: Optional[str] = None,
                                      strategy: SelectionStrategy = SelectionStrategy.HYBRID,
                                      use_cache: bool = True) -> OptimalNodeResult:
        """
        Выбрать оптимальную ноду для пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            country_id: Конкретная страна (опционально)
            strategy: Стратегия выбора
            use_cache: Использовать кэширование
        """
        cache_key = f"optimal_node_{tg_id}_{country_id}_{strategy.value}"
        
        if use_cache and cache_key in self._selection_cache:
            logger.debug(f"Cache hit for optimal node selection: {cache_key}")
            result = self._selection_cache[cache_key]
            result.cache_hit = True
            return result
        
        try:
            # Выбираем ноду через NodeSelectionService
            if country_id:
                selection_result = await self.selection_service.select_node_by_country(
                    tg_id, country_id, strategy
                )
            else:
                selection_result = await self.selection_service.select_optimal_node(
                    tg_id, strategy
                )
            
            if not selection_result.success:
                return OptimalNodeResult(
                    success=False,
                    selected_node=None,
                    fallback_nodes=[],
                    selection_strategy=strategy.value,
                    selection_reasons=selection_result.selection_reasons,
                    total_candidates=selection_result.total_candidates
                )
            
            # Конвертируем результат в NodeInfo
            selected_node_info = await self._convert_node_to_info(selection_result.selected_node)
            fallback_node_infos = []
            
            for fallback_node in selection_result.fallback_nodes:
                fallback_info = await self._convert_node_to_info(fallback_node)
                if fallback_info:
                    fallback_node_infos.append(fallback_info)
            
            result = OptimalNodeResult(
                success=True,
                selected_node=selected_node_info,
                fallback_nodes=fallback_node_infos,
                selection_strategy=strategy.value,
                selection_reasons=selection_result.selection_reasons,
                total_candidates=selection_result.total_candidates
            )
            
            # Кэшируем результат
            if use_cache:
                self._selection_cache[cache_key] = result
            
            logger.info(f"Selected optimal node for user {tg_id}: {selected_node_info.name if selected_node_info else 'None'}")
            return result
            
        except Exception as e:
            logger.error(f"Error selecting optimal node for user {tg_id}: {e}")
            return OptimalNodeResult(
                success=False,
                selected_node=None,
                fallback_nodes=[],
                selection_strategy=strategy.value,
                selection_reasons=[f"Selection error: {str(e)}"],
                total_candidates=0
            )
    
    async def get_country_recommendations(self,
                                        tg_id: int,
                                        limit: int = 5,
                                        use_cache: bool = True) -> List[CountryNodeInfo]:
        """Получить рекомендуемые страны для пользователя."""
        try:
            # Получаем рекомендуемые страны от preference service
            recommended_countries = await self.preference_service.get_recommended_countries(tg_id, limit)
            
            # Получаем полную информацию о странах с нодами
            all_countries = await self.get_available_countries_with_nodes(use_cache=use_cache)
            
            # Фильтруем по рекомендуемым
            result = []
            recommended_ids = [c.id for c in recommended_countries]
            
            for country_info in all_countries:
                if country_info.country.id in recommended_ids:
                    result.append(country_info)
            
            # Сортируем по порядку рекомендаций
            result.sort(key=lambda x: recommended_ids.index(x.country.id))
            
            return result[:limit]
            
        except Exception as e:
            logger.error(f"Error getting country recommendations for user {tg_id}: {e}")
            return []
    
    async def update_user_country_preference(self,
                                           tg_id: int,
                                           country_id: str,
                                           action: str = "add") -> bool:
        """
        Обновить предпочтения пользователя по стране.
        
        Args:
            tg_id: Telegram ID пользователя
            country_id: ID страны
            action: "add" или "remove"
        """
        try:
            if action == "add":
                success = await self.preference_service.add_preferred_country(tg_id, country_id)
            elif action == "remove":
                success = await self.preference_service.remove_preferred_country(tg_id, country_id)
            else:
                logger.warning(f"Unknown action: {action}")
                return False
            
            if success:
                # Очищаем кэш для этого пользователя
                self._clear_user_cache(tg_id)
                logger.info(f"Updated country preference for user {tg_id}: {action} {country_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating country preference for user {tg_id}: {e}")
            return False
    
    async def _convert_node_to_info(self, node: MarzbanNode) -> Optional[NodeInfo]:
        """Конвертировать MarzbanNode в NodeInfo."""
        try:
            # Получаем информацию о стране
            country = await self.country_repo.get_by_id(node.country_id)
            if not country:
                return None
            
            # Получаем статистику
            stats = await self.stats_repo.get_average_performance(node.id, hours=1)
            
            # Получаем поддерживаемые протоколы
            inbounds = await self.node_repo.get_inbounds_by_node(node.id)
            protocols = list(set([inbound.protocol for inbound in inbounds if inbound.is_active]))
            
            load_percentage = (node.current_users / node.max_users * 100) if node.max_users > 0 else 0
            is_available = (node.status == NodeStatus.CONNECTED and 
                          node.is_active and 
                          load_percentage < 90)
            
            return NodeInfo(
                id=node.id,
                name=node.name,
                country_id=node.country_id,
                country_name=country.name_ru,
                country_flag=country.flag,
                city=node.city or "",
                current_users=node.current_users,
                max_users=node.max_users,
                load_percentage=load_percentage,
                cpu_usage=stats.get('avg_cpu_usage', node.cpu_usage or 0),
                memory_usage=stats.get('avg_memory_usage', node.memory_usage or 0),
                response_time_ms=stats.get('avg_response_time_ms', 50),
                status=node.status,
                is_available=is_available,
                supported_protocols=protocols
            )
            
        except Exception as e:
            logger.error(f"Error converting node {node.id} to NodeInfo: {e}")
            return None
    
    def _clear_user_cache(self, tg_id: int):
        """Очистить кэш для конкретного пользователя."""
        keys_to_remove = []
        for key in self._selection_cache.keys():
            if f"_{tg_id}_" in str(key):
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            self._selection_cache.pop(key, None)
    
    def clear_all_cache(self):
        """Очистить весь кэш."""
        self._countries_cache.clear()
        self._nodes_cache.clear()
        self._selection_cache.clear()
        logger.info("All cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Получить статистику кэша."""
        return {
            'countries_cache': {
                'size': len(self._countries_cache),
                'maxsize': self._countries_cache.maxsize,
                'ttl': 300
            },
            'nodes_cache': {
                'size': len(self._nodes_cache),
                'maxsize': self._nodes_cache.maxsize,
                'ttl': 180
            },
            'selection_cache': {
                'size': len(self._selection_cache),
                'maxsize': self._selection_cache.maxsize,
                'ttl': 60
            }
        }
