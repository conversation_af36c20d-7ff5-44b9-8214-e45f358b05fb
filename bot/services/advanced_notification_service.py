"""
Расширенный сервис для работы с уведомлениями.

Этот модуль содержит новую архитектуру для планирования, отправки
и управления уведомлениями с поддержкой шаблонов и A/B тестирования.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from repositories.notification_repository import NotificationRepositoryInterface
from services.template_service import TemplateService, TemplateRenderError
from db.models_notifications import (
    NotificationTemplate, NotificationSchedule, NotificationLog,
    NotificationPreference
)
from schemas.notification_enums import (
    NotificationType, NotificationStatus, DeliveryStatus,
    NotificationPriority, NotificationChannel, ABTestVariant,
    get_notification_priority
)
from utils.logging_config import get_logger
from utils.id_generator import generate_id

logger = get_logger(__name__)


@dataclass
class NotificationResult:
    """Результат отправки уведомления."""
    success: bool
    message_id: Optional[int] = None
    error_message: Optional[str] = None
    delivery_status: DeliveryStatus = DeliveryStatus.DELIVERED
    processing_time_ms: int = 0


class AdvancedNotificationService:
    """Расширенный сервис для работы с уведомлениями."""
    
    def __init__(self,
                 notification_repo: NotificationRepositoryInterface,
                 template_service: TemplateService):
        self.notification_repo = notification_repo
        self.template_service = template_service
    
    async def schedule_subscription_expiry_notifications(self, 
                                                       user_id: int,
                                                       subscription_expires_at: datetime,
                                                       subscription_data: Dict[str, Any]):
        """
        Планирует уведомления об истечении подписки.
        
        Args:
            user_id: ID пользователя
            subscription_expires_at: Дата истечения подписки
            subscription_data: Данные подписки для контекста
        """
        try:
            # Отменяем существующие уведомления об истечении подписки
            await self._cancel_existing_expiry_notifications(user_id)
            
            # Планируем новые уведомления
            notifications_to_schedule = [
                (NotificationType.SUBSCRIPTION_EXPIRING_7_DAYS, 7),
                (NotificationType.SUBSCRIPTION_EXPIRING_3_DAYS, 3),
                (NotificationType.SUBSCRIPTION_EXPIRING_1_DAY, 1),
            ]
            
            scheduled_count = 0
            for notification_type, days_before in notifications_to_schedule:
                scheduled_at = subscription_expires_at - timedelta(days=days_before)
                
                # Не планируем уведомления в прошлом
                if scheduled_at <= datetime.utcnow():
                    logger.info(f"Skipping {notification_type} for user {user_id} - scheduled time in past")
                    continue
                
                # Получаем шаблон для этого типа уведомления
                template = await self.notification_repo.get_template_by_type(notification_type)
                if not template:
                    logger.warning(f"No template found for {notification_type}")
                    continue
                
                # Подготавливаем контекстные данные
                context_data = self._prepare_subscription_context(
                    subscription_data, days_before, subscription_expires_at
                )
                
                # Создаем запланированное уведомление
                schedule = NotificationSchedule(
                    id=generate_id(),
                    user_id=user_id,
                    template_id=template.id,
                    scheduled_at=scheduled_at,
                    notification_type=notification_type,
                    priority=get_notification_priority(notification_type),
                    context_data=context_data,
                    expires_at=subscription_expires_at + timedelta(days=1)  # Истекает через день после подписки
                )
                
                await self.notification_repo.create_schedule(schedule)
                scheduled_count += 1
                
                logger.info(f"Scheduled {notification_type} for user {user_id} at {scheduled_at}")
            
            logger.info(f"Scheduled {scheduled_count} expiry notifications for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to schedule expiry notifications for user {user_id}: {e}")
            raise
    
    async def send_immediate_notification(self,
                                        user_id: int,
                                        notification_type: NotificationType,
                                        context_data: Dict[str, Any],
                                        telegram_service=None) -> NotificationResult:
        """
        Отправляет немедленное уведомление.
        
        Args:
            user_id: ID пользователя
            notification_type: Тип уведомления
            context_data: Контекстные данные
            telegram_service: Сервис для отправки в Telegram
            
        Returns:
            Результат отправки
        """
        start_time = datetime.utcnow()
        
        try:
            # Проверяем настройки пользователя
            if not await self._is_notification_enabled_for_user(user_id, notification_type):
                logger.info(f"Notification {notification_type} disabled for user {user_id}")
                return NotificationResult(
                    success=False,
                    error_message="Notification disabled by user",
                    delivery_status=DeliveryStatus.BLOCKED_BY_USER
                )
            
            # Получаем шаблон
            template = await self._select_template_for_notification(notification_type)
            if not template:
                logger.error(f"No template found for {notification_type}")
                return NotificationResult(
                    success=False,
                    error_message="Template not found",
                    delivery_status=DeliveryStatus.FAILED
                )
            
            # Рендерим шаблон
            try:
                rendered = await self.template_service.render_template(template, context_data)
            except TemplateRenderError as e:
                logger.error(f"Template rendering failed: {e}")
                return NotificationResult(
                    success=False,
                    error_message=str(e),
                    delivery_status=DeliveryStatus.FAILED
                )
            
            # Отправляем уведомление
            if telegram_service:
                result = await self._send_telegram_notification(
                    user_id, rendered, telegram_service
                )
            else:
                # Заглушка для тестирования
                result = NotificationResult(
                    success=True,
                    message_id=12345,
                    delivery_status=DeliveryStatus.DELIVERED
                )
            
            # Вычисляем время обработки
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            result.processing_time_ms = int(processing_time)
            
            # Логируем отправку
            await self._log_notification_delivery(
                user_id, template, None, rendered, result, context_data
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to send immediate notification: {e}")
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            return NotificationResult(
                success=False,
                error_message=str(e),
                delivery_status=DeliveryStatus.FAILED,
                processing_time_ms=int(processing_time)
            )
    
    async def send_scheduled_notification(self, schedule: NotificationSchedule,
                                        telegram_service=None) -> NotificationResult:
        """
        Отправляет запланированное уведомление.
        
        Args:
            schedule: Запланированное уведомление
            telegram_service: Сервис для отправки в Telegram
            
        Returns:
            Результат отправки
        """
        try:
            # Обновляем статус на "в процессе"
            await self.notification_repo.update_schedule_status(
                schedule.id, NotificationStatus.PROCESSING
            )
            
            # Отправляем уведомление
            result = await self.send_immediate_notification(
                schedule.user_id,
                schedule.notification_type,
                schedule.context_data or {},
                telegram_service
            )
            
            # Обновляем статус в зависимости от результата
            if result.success:
                await self.notification_repo.update_schedule_status(
                    schedule.id, NotificationStatus.SENT
                )
            else:
                # Проверяем, нужно ли повторить попытку
                if schedule.attempts < schedule.max_attempts:
                    await self.notification_repo.update_schedule_status(
                        schedule.id, NotificationStatus.PENDING, result.error_message
                    )
                else:
                    await self.notification_repo.update_schedule_status(
                        schedule.id, NotificationStatus.FAILED, result.error_message
                    )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to send scheduled notification {schedule.id}: {e}")
            await self.notification_repo.update_schedule_status(
                schedule.id, NotificationStatus.FAILED, str(e)
            )
            return NotificationResult(
                success=False,
                error_message=str(e),
                delivery_status=DeliveryStatus.FAILED
            )
    
    async def get_pending_notifications(self, limit: int = 100) -> List[NotificationSchedule]:
        """Получает ожидающие отправки уведомления."""
        return await self.notification_repo.get_pending_notifications(limit)
    
    async def cancel_user_notifications(self, user_id: int, 
                                      notification_type: Optional[NotificationType] = None) -> int:
        """
        Отменяет уведомления пользователя.
        
        Args:
            user_id: ID пользователя
            notification_type: Тип уведомления (если None, отменяются все)
            
        Returns:
            Количество отмененных уведомлений
        """
        if notification_type:
            return await self.notification_repo.cancel_scheduled_notifications(
                user_id, notification_type
            )
        else:
            # Отменяем все типы уведомлений
            total_cancelled = 0
            for nt in NotificationType:
                cancelled = await self.notification_repo.cancel_scheduled_notifications(
                    user_id, nt
                )
                total_cancelled += cancelled
            return total_cancelled
    
    async def update_user_notification_preference(self, user_id: int,
                                                notification_type: NotificationType,
                                                is_enabled: bool) -> NotificationPreference:
        """Обновляет настройку уведомления для пользователя."""
        return await self.notification_repo.update_user_preference(
            user_id, notification_type, is_enabled
        )
    
    # Приватные методы
    
    async def _cancel_existing_expiry_notifications(self, user_id: int):
        """Отменяет существующие уведомления об истечении подписки."""
        expiry_types = [
            NotificationType.SUBSCRIPTION_EXPIRING_7_DAYS,
            NotificationType.SUBSCRIPTION_EXPIRING_3_DAYS,
            NotificationType.SUBSCRIPTION_EXPIRING_1_DAY,
        ]
        
        for notification_type in expiry_types:
            await self.notification_repo.cancel_scheduled_notifications(
                user_id, notification_type
            )
    
    def _prepare_subscription_context(self, subscription_data: Dict[str, Any],
                                    days_before: int, expires_at: datetime) -> Dict[str, Any]:
        """Подготавливает контекстные данные для уведомления о подписке."""
        context = subscription_data.copy()
        context.update({
            'days_left': days_before,
            'expires_at': expires_at,
            'expires_date': expires_at.strftime("%d.%m.%Y"),
            'expires_time': expires_at.strftime("%H:%M"),
            'is_urgent': days_before <= 1,
            'renewal_url': f"https://t.me/your_bot?start=renew"  # TODO: Заменить на реальный URL
        })
        return context

    async def _is_notification_enabled_for_user(self, user_id: int,
                                              notification_type: NotificationType) -> bool:
        """Проверяет, включены ли уведомления для пользователя."""
        try:
            preferences = await self.notification_repo.get_user_preferences(user_id)

            # Ищем настройку для конкретного типа
            for pref in preferences:
                if pref.notification_type == notification_type:
                    return pref.is_enabled

            # Если настройки нет, по умолчанию включено
            return True

        except Exception as e:
            logger.error(f"Failed to check notification preferences for user {user_id}: {e}")
            # В случае ошибки считаем, что уведомления включены
            return True

    async def _select_template_for_notification(self,
                                              notification_type: NotificationType) -> Optional[NotificationTemplate]:
        """Выбирает шаблон для уведомления (с поддержкой A/B тестирования)."""
        templates = await self.notification_repo.get_templates_by_type(notification_type)

        if not templates:
            return None

        if len(templates) == 1:
            return templates[0]

        # Простая логика A/B тестирования на основе весов
        # TODO: Реализовать более сложную логику с учетом пользователя
        total_weight = sum(t.ab_test_weight for t in templates)
        if total_weight == 0:
            return templates[0]

        import random
        rand_value = random.randint(1, total_weight)
        current_weight = 0

        for template in templates:
            current_weight += template.ab_test_weight
            if rand_value <= current_weight:
                return template

        return templates[0]  # Fallback

    async def _send_telegram_notification(self, user_id: int, rendered, telegram_service):
        """Отправляет уведомление через Telegram."""
        # TODO: Реализовать интеграцию с Telegram сервисом
        # Пока возвращаем заглушку
        return NotificationResult(
            success=True,
            message_id=12345,
            delivery_status=DeliveryStatus.DELIVERED
        )

    async def _log_notification_delivery(self, user_id: int, template: NotificationTemplate,
                                       schedule: Optional[NotificationSchedule],
                                       rendered, result: NotificationResult,
                                       context_data: Dict[str, Any]):
        """Логирует доставку уведомления."""
        log = NotificationLog(
            id=generate_id(),
            user_id=user_id,
            template_id=template.id,
            schedule_id=schedule.id if schedule else None,
            notification_type=template.notification_type,
            channel=NotificationChannel.TELEGRAM,
            priority=template.priority,
            delivery_status=result.delivery_status,
            telegram_message_id=result.message_id,
            rendered_subject=rendered.subject,
            rendered_body=rendered.body,
            context_data=context_data,
            ab_test_variant=rendered.ab_test_variant,
            processing_time_ms=result.processing_time_ms,
            error_message=result.error_message
        )

        await self.notification_repo.log_notification(log)
