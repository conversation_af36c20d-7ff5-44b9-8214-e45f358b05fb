"""
NotificationService - сервис для централизованного управления уведомлениями.
Содержит бизнес-логику отправки уведомлений пользователям.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from aiogram import Bot
from aiogram.utils.i18n import gettext as _

from .base import BaseService
from keyboards import get_main_menu_keyboard, get_subscription_keyboard
from utils.logging_config import get_logger
import glv

logger = get_logger(__name__)


class NotificationService(BaseService):
    """
    Сервис для централизованного управления уведомлениями.
    Содержит бизнес-логику отправки различных типов уведомлений.
    """
    
    def __init__(self, container=None, bot: Optional[Bot] = None):
        super().__init__(container)
        self.bot = bot
        self.notification_templates = {
            'welcome': self._get_welcome_template,
            'test_subscription_created': self._get_test_subscription_template,
            'paid_subscription_created': self._get_paid_subscription_template,
            'subscription_expires_soon': self._get_expiring_subscription_template,
            'subscription_expired': self._get_expired_subscription_template,
            'payment_successful': self._get_payment_success_template,
            'payment_failed': self._get_payment_failed_template
        }
    
    async def initialize(self) -> None:
        """Инициализация NotificationService."""
        logger.info("NotificationService initialized")
    
    async def cleanup(self) -> None:
        """Очистка ресурсов NotificationService."""
        logger.info("NotificationService cleaned up")
    
    def set_bot(self, bot: Bot) -> None:
        """
        Устанавливает экземпляр бота для отправки сообщений.
        
        Args:
            bot: Экземпляр Telegram бота
        """
        self.bot = bot
        logger.debug("Bot instance set for NotificationService")
    
    async def send_welcome_message(self, telegram_id: int, user_name: str, has_test_subscription: bool) -> bool:
        """
        Отправляет приветственное сообщение.
        
        Args:
            telegram_id: Telegram ID пользователя
            user_name: Имя пользователя
            has_test_subscription: Использована ли тестовая подписка
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending welcome message")
                return False
            
            template_data = {
                'user_name': user_name,
                'shop_name': glv.config.get('SHOP_NAME', 'VPN Shop')
            }
            
            text = self._get_welcome_template(template_data)
            keyboard = get_main_menu_keyboard(has_test_subscription)
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent welcome message to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending welcome message to user {telegram_id}: {e}")
            return False
    
    async def send_test_subscription_created(self, telegram_id: int) -> bool:
        """
        Отправляет уведомление о создании тестовой подписки.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending test subscription notification")
                return False
            
            template_data = {
                'info_channel_link': glv.config.get('TG_INFO_CHANEL', '#')
            }
            
            text = self._get_test_subscription_template(template_data)
            keyboard = get_main_menu_keyboard(True)
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent test subscription notification to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending test subscription notification to user {telegram_id}: {e}")
            return False
    
    async def send_paid_subscription_created(self, telegram_id: int, subscription_url: Optional[str] = None) -> bool:
        """
        Отправляет уведомление о создании платной подписки.
        
        Args:
            telegram_id: Telegram ID пользователя
            subscription_url: URL подписки
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending paid subscription notification")
                return False
            
            template_data = {
                'info_channel_link': glv.config.get('TG_INFO_CHANEL', '#')
            }
            
            text = self._get_paid_subscription_template(template_data)
            
            if subscription_url:
                keyboard = get_subscription_keyboard(subscription_url)
            else:
                keyboard = get_main_menu_keyboard(True)
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent paid subscription notification to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending paid subscription notification to user {telegram_id}: {e}")
            return False
    
    async def send_subscription_expiring_soon(self, telegram_id: int, days_remaining: int) -> bool:
        """
        Отправляет уведомление о скором истечении подписки.
        
        Args:
            telegram_id: Telegram ID пользователя
            days_remaining: Количество дней до истечения
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending expiring subscription notification")
                return False
            
            template_data = {
                'days_remaining': days_remaining
            }
            
            text = self._get_expiring_subscription_template(template_data)
            keyboard = get_main_menu_keyboard(True)
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent expiring subscription notification to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending expiring subscription notification to user {telegram_id}: {e}")
            return False
    
    async def send_subscription_expired(self, telegram_id: int) -> bool:
        """
        Отправляет уведомление об истечении подписки.
        
        Args:
            telegram_id: Telegram ID пользователя
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending expired subscription notification")
                return False
            
            text = self._get_expired_subscription_template({})
            keyboard = get_main_menu_keyboard(True)
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent expired subscription notification to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending expired subscription notification to user {telegram_id}: {e}")
            return False
    
    async def send_payment_successful(self, telegram_id: int, amount: str, currency: str) -> bool:
        """
        Отправляет уведомление об успешном платеже.
        
        Args:
            telegram_id: Telegram ID пользователя
            amount: Сумма платежа
            currency: Валюта
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending payment success notification")
                return False
            
            template_data = {
                'amount': amount,
                'currency': currency,
                'info_channel_link': glv.config.get('TG_INFO_CHANEL', '#')
            }
            
            text = self._get_payment_success_template(template_data)
            keyboard = get_main_menu_keyboard(True)
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent payment success notification to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending payment success notification to user {telegram_id}: {e}")
            return False
    
    async def send_custom_notification(self, telegram_id: int, text: str, keyboard=None) -> bool:
        """
        Отправляет кастомное уведомление.
        
        Args:
            telegram_id: Telegram ID пользователя
            text: Текст сообщения
            keyboard: Клавиатура (опционально)
            
        Returns:
            True если сообщение отправлено успешно
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for sending custom notification")
                return False
            
            await self.bot.send_message(
                chat_id=telegram_id,
                text=text,
                reply_markup=keyboard
            )
            
            logger.info(f"Sent custom notification to user {telegram_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending custom notification to user {telegram_id}: {e}")
            return False
    
    async def broadcast_notification(self, user_ids: List[int], text: str, keyboard=None) -> Dict[str, int]:
        """
        Отправляет уведомление множеству пользователей.
        
        Args:
            user_ids: Список Telegram ID пользователей
            text: Текст сообщения
            keyboard: Клавиатура (опционально)
            
        Returns:
            Статистика отправки
        """
        try:
            if not self.bot:
                logger.error("Bot instance not set for broadcasting")
                return {'sent': 0, 'failed': 0}
            
            sent_count = 0
            failed_count = 0
            
            for user_id in user_ids:
                try:
                    await self.bot.send_message(
                        chat_id=user_id,
                        text=text,
                        reply_markup=keyboard
                    )
                    sent_count += 1
                except Exception as e:
                    logger.warning(f"Failed to send broadcast to user {user_id}: {e}")
                    failed_count += 1
            
            logger.info(f"Broadcast completed: {sent_count} sent, {failed_count} failed")
            return {'sent': sent_count, 'failed': failed_count}
            
        except Exception as e:
            logger.error(f"Error broadcasting notification: {e}")
            return {'sent': 0, 'failed': len(user_ids)}
    
    # Шаблоны сообщений
    def _get_welcome_template(self, data: Dict[str, Any]) -> str:
        """Шаблон приветственного сообщения."""
        return _("Hello, {name} 👋🏻\n\nSelect an action ⬇️").format(
            name=data.get('user_name', 'User')
        )
    
    def _get_test_subscription_template(self, data: Dict[str, Any]) -> str:
        """Шаблон уведомления о тестовой подписке."""
        return _("Thank you for choice ❤️\n️\n<a href=\"{link}\">Subscribe</a> so you don't miss any announcements ✅\n️\nYour subscription is purchased and available in the \"My subscription 👤\" section.").format(
            link=data.get('info_channel_link', '#')
        )
    
    def _get_paid_subscription_template(self, data: Dict[str, Any]) -> str:
        """Шаблон уведомления о платной подписке."""
        return _("Thank you for choice ❤️\n️\n<a href=\"{link}\">Subscribe</a> so you don't miss any announcements ✅\n️\nYour subscription is purchased and available in the \"My subscription 👤\" section.").format(
            link=data.get('info_channel_link', '#')
        )
    
    def _get_expiring_subscription_template(self, data: Dict[str, Any]) -> str:
        """Шаблон уведомления о скором истечении подписки."""
        days = data.get('days_remaining', 0)
        return f"⚠️ Your subscription expires in {days} days. Renew it to continue using the service."
    
    def _get_expired_subscription_template(self, data: Dict[str, Any]) -> str:
        """Шаблон уведомления об истечении подписки."""
        return "❌ Your subscription has expired. Please renew it to continue using the service."
    
    def _get_payment_success_template(self, data: Dict[str, Any]) -> str:
        """Шаблон уведомления об успешном платеже."""
        return _("Thank you for choice ❤️\n️\n<a href=\"{link}\">Subscribe</a> so you don't miss any announcements ✅\n️\nYour subscription is purchased and available in the \"My subscription 👤\" section.").format(
            link=data.get('info_channel_link', '#')
        )
    
    def _get_payment_failed_template(self, data: Dict[str, Any]) -> str:
        """Шаблон уведомления о неудачном платеже."""
        return "❌ Payment failed. Please try again or contact support."
