"""
PaymentService - сервис для обработки платежей.
Содержит бизнес-логику работы с платежными системами.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal

from .base import ServiceWithRepository
from db.models import YPayments, CPayments
from utils import goods, yookassa, cryptomus
from utils.logging_config import get_logger

logger = get_logger(__name__)


class PaymentService(ServiceWithRepository):
    """
    Сервис для обработки платежей.
    Содержит бизнес-логику работы с платежными системами.
    """
    
    def __init__(self, container=None):
        super().__init__(container)
        self.supported_providers = ['yookassa', 'cryptomus', 'telegram_stars']
    
    async def initialize(self) -> None:
        """Инициализация PaymentService."""
        logger.info("PaymentService initialized")
    
    async def cleanup(self) -> None:
        """Очистка ресурсов PaymentService."""
        logger.info("PaymentService cleaned up")
    
    async def validate_product(self, product_callback: str) -> bool:
        """
        Проверяет существование товара.
        
        Args:
            product_callback: Callback товара
            
        Returns:
            True если товар существует
        """
        try:
            available_callbacks = goods.get_callbacks()
            is_valid = product_callback in available_callbacks
            
            if not is_valid:
                logger.warning(f"Product not found: {product_callback}")
            else:
                logger.debug(f"Product validated: {product_callback}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error validating product {product_callback}: {e}")
            return False
    
    async def get_product_info(self, product_callback: str) -> Optional[Dict[str, Any]]:
        """
        Получает информацию о товаре.
        
        Args:
            product_callback: Callback товара
            
        Returns:
            Информация о товаре или None
        """
        try:
            if not await self.validate_product(product_callback):
                return None
            
            product = goods.get(product_callback)
            logger.debug(f"Retrieved product info: {product_callback}")
            return product
            
        except Exception as e:
            logger.error(f"Error getting product info {product_callback}: {e}")
            return None
    
    async def create_yookassa_payment(self, 
                                    telegram_id: int, 
                                    product_callback: str, 
                                    chat_id: int, 
                                    language_code: str) -> Optional[Dict[str, Any]]:
        """
        Создает платеж YooKassa.
        
        Args:
            telegram_id: Telegram ID пользователя
            product_callback: Callback товара
            chat_id: ID чата
            language_code: Код языка
            
        Returns:
            Данные платежа или None
        """
        try:
            # Валидируем товар
            if not await self.validate_product(product_callback):
                logger.error(f"Invalid product for YooKassa payment: {product_callback}")
                return None
            
            # Создаем платеж через YooKassa API
            payment_result = await yookassa.create_payment(
                telegram_id, 
                product_callback, 
                chat_id, 
                language_code
            )
            
            if payment_result:
                logger.info(f"Created YooKassa payment for user {telegram_id}: {payment_result.get('id')}")
                return payment_result
            else:
                logger.error(f"Failed to create YooKassa payment for user {telegram_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating YooKassa payment for user {telegram_id}: {e}")
            return None
    
    async def create_cryptomus_payment(self, 
                                     telegram_id: int, 
                                     product_callback: str, 
                                     chat_id: int, 
                                     language_code: str) -> Optional[Dict[str, Any]]:
        """
        Создает платеж Cryptomus.
        
        Args:
            telegram_id: Telegram ID пользователя
            product_callback: Callback товара
            chat_id: ID чата
            language_code: Код языка
            
        Returns:
            Данные платежа или None
        """
        try:
            # Валидируем товар
            if not await self.validate_product(product_callback):
                logger.error(f"Invalid product for Cryptomus payment: {product_callback}")
                return None
            
            # Создаем платеж через Cryptomus API
            payment_result = await cryptomus.create_payment(
                telegram_id, 
                product_callback, 
                chat_id, 
                language_code
            )
            
            if payment_result:
                logger.info(f"Created Cryptomus payment for user {telegram_id}: {payment_result.get('order_id')}")
                return payment_result
            else:
                logger.error(f"Failed to create Cryptomus payment for user {telegram_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating Cryptomus payment for user {telegram_id}: {e}")
            return None
    
    async def create_telegram_stars_payment(self, 
                                          telegram_id: int, 
                                          product_callback: str) -> Optional[Dict[str, Any]]:
        """
        Создает платеж Telegram Stars.
        
        Args:
            telegram_id: Telegram ID пользователя
            product_callback: Callback товара
            
        Returns:
            Данные платежа или None
        """
        try:
            # Валидируем товар
            product = await self.get_product_info(product_callback)
            if not product:
                logger.error(f"Invalid product for Telegram Stars payment: {product_callback}")
                return None
            
            # Формируем данные для Telegram Stars
            stars_amount = product.get('stars_price', 50)  # Цена в звездах
            
            payment_data = {
                'title': product.get('name', 'VPN Subscription'),
                'description': product.get('description', 'VPN subscription payment'),
                'payload': product_callback,
                'currency': 'XTR',
                'prices': [{'label': 'VPN Subscription', 'amount': stars_amount}]
            }
            
            logger.info(f"Created Telegram Stars payment for user {telegram_id}: {stars_amount} stars")
            return payment_data
            
        except Exception as e:
            logger.error(f"Error creating Telegram Stars payment for user {telegram_id}: {e}")
            return None
    
    async def get_payment_by_id(self, payment_id: str, provider: str = 'yookassa') -> Optional[Any]:
        """
        Получает платеж по ID.
        
        Args:
            payment_id: ID платежа
            provider: Провайдер платежа
            
        Returns:
            Объект платежа или None
        """
        try:
            async with self.get_payment_repository() as payment_repo:
                if provider == 'yookassa':
                    payment = await payment_repo.get_yookassa_payment(payment_id)
                elif provider == 'cryptomus':
                    payment = await payment_repo.get_cryptomus_payment(payment_id)
                else:
                    logger.error(f"Unsupported payment provider: {provider}")
                    return None
                
                if payment:
                    logger.debug(f"Retrieved {provider} payment: {payment_id}")
                else:
                    logger.debug(f"{provider} payment not found: {payment_id}")
                
                return payment
                
        except Exception as e:
            logger.error(f"Error getting {provider} payment {payment_id}: {e}")
            return None
    
    async def get_user_payments(self, telegram_id: int, provider: Optional[str] = None) -> List[Any]:
        """
        Получает все платежи пользователя.
        
        Args:
            telegram_id: Telegram ID пользователя
            provider: Провайдер платежа (опционально)
            
        Returns:
            Список платежей
        """
        try:
            async with self.get_payment_repository() as payment_repo:
                payments = await payment_repo.get_user_payments(telegram_id, provider)
                logger.debug(f"Retrieved {len(payments)} payments for user {telegram_id}")
                return payments
                
        except Exception as e:
            logger.error(f"Error getting payments for user {telegram_id}: {e}")
            return []
    
    async def process_successful_payment(self, payment_id: str, provider: str) -> bool:
        """
        Обрабатывает успешный платеж.
        
        Args:
            payment_id: ID платежа
            provider: Провайдер платежа
            
        Returns:
            True если обработка прошла успешно
        """
        try:
            # Получаем платеж
            payment = await self.get_payment_by_id(payment_id, provider)
            if not payment:
                logger.error(f"Payment not found for processing: {payment_id}")
                return False
            
            # Получаем информацию о товаре
            product = await self.get_product_info(payment.callback)
            if not product:
                logger.error(f"Product not found for payment: {payment.callback}")
                return False
            
            # Получаем UserService для работы с пользователем
            if self.container:
                from .user_service import UserService
                user_service = self.container.get_service(UserService)
                
                # Проверяем существование пользователя
                user = await user_service.get_user_profile(payment.tg_id)
                if not user:
                    logger.error(f"User not found for payment: {payment.tg_id}")
                    return False
                
                # Получаем SubscriptionService для создания подписки
                from .subscription_service import SubscriptionService
                subscription_service = self.container.get_service(SubscriptionService)
                
                # Создаем подписку
                subscription_result = await subscription_service.create_paid_subscription(
                    payment.tg_id, product
                )
                
                if subscription_result:
                    logger.info(f"Successfully processed payment {payment_id} for user {payment.tg_id}")
                    return True
                else:
                    logger.error(f"Failed to create subscription for payment {payment_id}")
                    return False
            else:
                logger.warning("Service container not available for payment processing")
                return False
                
        except Exception as e:
            logger.error(f"Error processing successful payment {payment_id}: {e}")
            return False
    
    async def delete_payment(self, payment_id: str, provider: str) -> bool:
        """
        Удаляет платеж.
        
        Args:
            payment_id: ID платежа
            provider: Провайдер платежа
            
        Returns:
            True если удаление прошло успешно
        """
        try:
            async with self.get_payment_repository() as payment_repo:
                result = await payment_repo.delete_payment(payment_id, provider)
                
                if result:
                    logger.info(f"Deleted {provider} payment: {payment_id}")
                else:
                    logger.warning(f"Failed to delete {provider} payment: {payment_id}")
                
                return result
                
        except Exception as e:
            logger.error(f"Error deleting {provider} payment {payment_id}: {e}")
            return False
    
    async def get_payment_stats(self) -> Dict[str, Any]:
        """
        Получает статистику платежей.
        
        Returns:
            Словарь со статистикой
        """
        try:
            async with self.get_payment_repository() as payment_repo:
                stats = await payment_repo.get_payment_stats()
                logger.debug(f"Retrieved payment stats: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Error getting payment stats: {e}")
            return {}
    
    async def validate_payment_amount(self, product_callback: str, amount: Decimal, currency: str) -> bool:
        """
        Валидирует сумму платежа.
        
        Args:
            product_callback: Callback товара
            amount: Сумма платежа
            currency: Валюта
            
        Returns:
            True если сумма корректна
        """
        try:
            product = await self.get_product_info(product_callback)
            if not product:
                return False
            
            expected_price = product.get('price', {}).get(currency.lower())
            if expected_price is None:
                logger.warning(f"Price not found for currency {currency} in product {product_callback}")
                return False
            
            is_valid = abs(float(amount) - float(expected_price)) < 0.01
            
            if not is_valid:
                logger.warning(f"Invalid payment amount: expected {expected_price}, got {amount}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error validating payment amount: {e}")
            return False
