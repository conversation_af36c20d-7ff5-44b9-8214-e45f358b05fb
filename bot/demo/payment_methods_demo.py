"""
Демо-скрипт для тестирования системы управления платежными методами.

Этот скрипт демонстрирует функциональность:
- Сохранения платежных методов
- Управления настройками автопродления
- Тестирования платежных методов
- Автоматического продления подписок
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any

# Добавляем путь к модулям бота
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.base import get_session
from repositories.payment_method_repository import PaymentMethodRepository
from services.payment_method_service import PaymentMethodService
from services.auto_renewal_service import AutoRenewalService
from schemas.payment_methods import (
    SavedPaymentMethodCreate, AutoRenewalSettingsCreate,
    PaymentMethodTypeEnum
)
from utils.logging_config import get_logger

logger = get_logger(__name__)

# Импортируем generate_id
try:
    from utils.id_generator import generate_id
except ImportError:
    import uuid
    def generate_id():
        return str(uuid.uuid4()).replace("-", "")[:32]


class PaymentMethodsDemo:
    """Демо-класс для тестирования системы платежных методов."""
    
    def __init__(self):
        self.test_user_id = 123456789
        self.payment_method_service = None
        self.auto_renewal_service = None
    
    async def setup(self):
        """Инициализация сервисов."""
        session = await get_session()
        payment_method_repo = PaymentMethodRepository(session)
        self.payment_method_service = PaymentMethodService(payment_method_repo)
        
        # Для демо создаем заглушки других сервисов
        self.auto_renewal_service = None  # TODO: Инициализировать когда будет готов
        
        logger.info("Payment methods demo setup completed")
    
    async def demo_save_payment_methods(self):
        """Демонстрация сохранения платежных методов."""
        print("\n🔹 Демо: Сохранение платежных методов")
        print("=" * 50)
        
        # Тестовые данные для YooKassa
        yookassa_data = {
            "payment_method_id": "pm_test_123456",
            "card_type": "Visa",
            "card_last4": "1234",
            "card_expiry_month": 12,
            "card_expiry_year": 2025
        }
        
        yookassa_create = SavedPaymentMethodCreate(
            payment_type=PaymentMethodTypeEnum.YOOKASSA,
            display_name="Visa ****1234",
            is_default=True,
            provider_payment_method_id="pm_test_123456",
            expires_at=datetime(2025, 12, 31)
        )
        
        # Сохраняем YooKassa метод
        result = await self.payment_method_service.save_payment_method(
            self.test_user_id, yookassa_create, yookassa_data
        )
        
        if result.success:
            print(f"✅ YooKassa метод сохранен: {result.payment_method.id}")
            yookassa_method_id = result.payment_method.id
        else:
            print(f"❌ Ошибка сохранения YooKassa: {result.error_message}")
            return
        
        # Тестовые данные для Cryptomus
        cryptomus_data = {
            "wallet_address": "**********************************",
            "currency": "BTC",
            "network": "Bitcoin"
        }
        
        cryptomus_create = SavedPaymentMethodCreate(
            payment_type=PaymentMethodTypeEnum.CRYPTOMUS,
            display_name="Bitcoin Wallet",
            is_default=False
        )
        
        # Сохраняем Cryptomus метод
        result = await self.payment_method_service.save_payment_method(
            self.test_user_id, cryptomus_create, cryptomus_data
        )
        
        if result.success:
            print(f"✅ Cryptomus метод сохранен: {result.payment_method.id}")
        else:
            print(f"❌ Ошибка сохранения Cryptomus: {result.error_message}")
        
        return yookassa_method_id
    
    async def demo_get_user_methods(self):
        """Демонстрация получения платежных методов пользователя."""
        print("\n🔹 Демо: Получение платежных методов пользователя")
        print("=" * 50)
        
        user_methods = await self.payment_method_service.get_user_payment_methods(
            self.test_user_id
        )
        
        print(f"📊 Всего методов: {user_methods.total_count}")
        print(f"📊 Активных методов: {user_methods.active_count}")
        print(f"📊 Метод по умолчанию: {user_methods.default_payment_method_id}")
        
        print("\n💳 Список методов:")
        for i, method in enumerate(user_methods.payment_methods, 1):
            status = "✅ Активен" if method.is_active else "❌ Неактивен"
            default = "⭐ По умолчанию" if method.is_default else ""
            
            print(f"{i}. {method.display_name} {default}")
            print(f"   {method.masked_data}")
            print(f"   Тип: {method.payment_type}")
            print(f"   Статус: {status}")
            print(f"   Создан: {method.created_at.strftime('%d.%m.%Y %H:%M')}")
            print()
        
        return user_methods.payment_methods
    
    async def demo_setup_auto_renewal(self, payment_method_id: str):
        """Демонстрация настройки автопродления."""
        print("\n🔹 Демо: Настройка автопродления")
        print("=" * 50)
        
        auto_renewal_create = AutoRenewalSettingsCreate(
            payment_method_id=payment_method_id,
            renewal_days_before=3,
            max_retry_attempts=3,
            notify_on_success=True,
            notify_on_failure=True,
            notify_before_renewal=True
        )
        
        result = await self.payment_method_service.setup_auto_renewal(
            self.test_user_id, auto_renewal_create
        )
        
        if result.success:
            print(f"✅ Автопродление настроено: {result.operation_id}")
            print("📋 Настройки:")
            print(f"   💳 Платежный метод: {payment_method_id}")
            print(f"   📅 Продлевать за: 3 дня до истечения")
            print(f"   🔄 Максимум попыток: 3")
            print(f"   🔔 Уведомления: включены")
        else:
            print(f"❌ Ошибка настройки автопродления: {result.error_message}")
        
        return result.success
    
    async def demo_test_payment_methods(self, methods):
        """Демонстрация тестирования платежных методов."""
        print("\n🔹 Демо: Тестирование платежных методов")
        print("=" * 50)
        
        for method in methods:
            print(f"\n🧪 Тестируем: {method.display_name}")
            
            test_result = await self.payment_method_service.test_payment_method(
                self.test_user_id, method.id
            )
            
            if test_result.success:
                print(f"✅ Тест прошел успешно!")
                print(f"   ⏱️ Время ответа: {test_result.response_time_ms}мс")
                print(f"   💬 Сообщение: {test_result.message}")
                if test_result.test_transaction_id:
                    print(f"   🔢 ID теста: {test_result.test_transaction_id}")
            else:
                print(f"❌ Тест не прошел")
                print(f"   💬 Ошибка: {test_result.message}")
                print(f"   ⏱️ Время ответа: {test_result.response_time_ms}мс")
    
    async def demo_update_payment_method(self, methods):
        """Демонстрация обновления платежного метода."""
        print("\n🔹 Демо: Обновление платежного метода")
        print("=" * 50)
        
        if not methods:
            print("❌ Нет методов для обновления")
            return
        
        # Берем первый метод для демо
        method = methods[0]
        print(f"📝 Обновляем метод: {method.display_name}")
        
        from schemas.payment_methods import SavedPaymentMethodUpdate
        update_data = SavedPaymentMethodUpdate(
            display_name=f"{method.display_name} (Обновлен)"
        )
        
        result = await self.payment_method_service.update_payment_method(
            self.test_user_id, method.id, update_data
        )
        
        if result.success:
            print(f"✅ Метод обновлен успешно!")
            print(f"   Новое название: {result.payment_method.display_name}")
        else:
            print(f"❌ Ошибка обновления: {result.error_message}")
    
    async def demo_delete_payment_method(self, methods):
        """Демонстрация удаления платежного метода."""
        print("\n🔹 Демо: Удаление платежного метода")
        print("=" * 50)
        
        if len(methods) < 2:
            print("❌ Недостаточно методов для демо удаления")
            return
        
        # Удаляем последний метод (не по умолчанию)
        method_to_delete = None
        for method in methods:
            if not method.is_default:
                method_to_delete = method
                break
        
        if not method_to_delete:
            print("❌ Нет подходящего метода для удаления")
            return
        
        print(f"🗑️ Удаляем метод: {method_to_delete.display_name}")
        
        result = await self.payment_method_service.delete_payment_method(
            self.test_user_id, method_to_delete.id
        )
        
        if result.success:
            print(f"✅ Метод удален успешно!")
        else:
            print(f"❌ Ошибка удаления: {result.error_message}")
    
    async def demo_statistics(self):
        """Демонстрация статистики после всех операций."""
        print("\n🔹 Демо: Финальная статистика")
        print("=" * 50)
        
        user_methods = await self.payment_method_service.get_user_payment_methods(
            self.test_user_id
        )
        
        print(f"📊 Итоговая статистика:")
        print(f"   💳 Всего методов: {user_methods.total_count}")
        print(f"   ✅ Активных методов: {user_methods.active_count}")
        print(f"   ⭐ Метод по умолчанию: {'Да' if user_methods.default_payment_method_id else 'Нет'}")
        print(f"   🔄 Автопродление: {'Настроено' if user_methods.auto_renewal_settings else 'Не настроено'}")
    
    async def run_full_demo(self):
        """Запуск полного демо."""
        print("🚀 Запуск демо системы управления платежными методами")
        print("=" * 60)
        
        try:
            await self.setup()
            
            # 1. Сохранение платежных методов
            yookassa_method_id = await self.demo_save_payment_methods()
            
            # 2. Получение методов пользователя
            methods = await self.demo_get_user_methods()
            
            # 3. Настройка автопродления
            if yookassa_method_id:
                await self.demo_setup_auto_renewal(yookassa_method_id)
            
            # 4. Тестирование методов
            await self.demo_test_payment_methods(methods)
            
            # 5. Обновление метода
            await self.demo_update_payment_method(methods)
            
            # 6. Удаление метода
            await self.demo_delete_payment_method(methods)
            
            # 7. Финальная статистика
            await self.demo_statistics()
            
            print("\n🎉 Демо завершено успешно!")
            
        except Exception as e:
            logger.error(f"Error in payment methods demo: {e}")
            print(f"\n❌ Ошибка в демо: {e}")


async def main():
    """Главная функция для запуска демо."""
    demo = PaymentMethodsDemo()
    await demo.run_full_demo()


if __name__ == "__main__":
    asyncio.run(main())
