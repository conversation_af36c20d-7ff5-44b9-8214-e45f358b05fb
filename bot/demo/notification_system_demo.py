"""
Демонстрация системы уведомлений.

Этот скрипт показывает, как работает новая система уведомлений
без подключения к реальной базе данных.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any

# Импорты системы уведомлений
from services.template_service import TemplateService
from services.advanced_notification_service import AdvancedNotificationService
from services.notification_scheduler import NotificationScheduler
from db.models_notifications import NotificationTemplate
from schemas.notification_enums import (
    NotificationType, NotificationPriority, NotificationChannel, ABTestVariant
)

# Используем mock-репозиторий из тестов
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'tests'))
from test_notification_system import MockNotificationRepository


def generate_id():
    """Простая функция генерации ID."""
    import uuid
    return str(uuid.uuid4()).replace("-", "")[:32]


async def create_sample_templates(repo: MockNotificationRepository):
    """Создает примеры шаблонов для демонстрации."""
    
    templates = [
        {
            'id': generate_id(),
            'name': 'Подписка истекает завтра',
            'notification_type': NotificationType.SUBSCRIPTION_EXPIRING_1_DAY,
            'priority': NotificationPriority.HIGH,
            'body_template': '''🔴 СРОЧНО! Ваша подписка истекает завтра!

📅 Дата истечения: {{ expires_at|datetime("%d.%m.%Y в %H:%M") }}

⚡ Продлите подписку прямо сейчас, чтобы не потерять доступ к VPN!

🔗 [Продлить подписку]({{ renewal_url }})

💬 Если у вас есть вопросы, обратитесь в поддержку.''',
            'variables': {
                'expires_at': {'type': 'datetime', 'required': True},
                'renewal_url': {'type': 'url', 'required': True}
            },
            'default_values': {
                'renewal_url': 'https://t.me/your_bot?start=renew'
            }
        },
        {
            'id': generate_id(),
            'name': 'Добро пожаловать',
            'notification_type': NotificationType.WELCOME_MESSAGE,
            'priority': NotificationPriority.NORMAL,
            'body_template': '''👋 Добро пожаловать, {{ user_name }}!

🎉 Спасибо за регистрацию в нашем VPN сервисе!

🚀 Что вы можете сделать:
• 🆓 Получить тестовый доступ
• 💎 Выбрать премиум подписку
• 🌍 Подключиться к серверам по всему миру

💡 Начните с тестового периода, чтобы оценить качество нашего сервиса.

🔗 [Получить тестовый доступ]({{ test_access_url }})

❓ Есть вопросы? Обратитесь в поддержку.''',
            'variables': {
                'user_name': {'type': 'string', 'required': True},
                'test_access_url': {'type': 'url', 'required': True}
            },
            'default_values': {
                'test_access_url': 'https://t.me/your_bot?start=test'
            }
        },
        {
            'id': generate_id(),
            'name': 'Платеж успешен',
            'notification_type': NotificationType.PAYMENT_SUCCESS,
            'priority': NotificationPriority.NORMAL,
            'body_template': '''✅ Платеж успешно обработан

💳 Сумма: {{ amount|currency }}
📅 Дата: {{ payment_date|datetime("%d.%m.%Y в %H:%M") }}

🎉 Ваша подписка активирована!

📱 Вы можете начать использовать VPN прямо сейчас.

🔗 [Получить конфигурацию]({{ config_url }})

❤️ Спасибо за выбор нашего сервиса!''',
            'variables': {
                'amount': {'type': 'float', 'required': True},
                'payment_date': {'type': 'datetime', 'required': True},
                'config_url': {'type': 'url', 'required': True}
            },
            'default_values': {
                'config_url': 'https://t.me/your_bot?start=config'
            }
        }
    ]
    
    for template_data in templates:
        template = NotificationTemplate(
            id=template_data['id'],
            name=template_data['name'],
            notification_type=template_data['notification_type'],
            priority=template_data['priority'],
            channel=NotificationChannel.TELEGRAM,
            subject_template=None,
            body_template=template_data['body_template'],
            variables=template_data['variables'],
            default_values=template_data['default_values'],
            ab_test_variant=ABTestVariant.CONTROL,
            ab_test_weight=100,
            is_active=True
        )
        
        await repo.create_template(template)
        print(f"✅ Создан шаблон: {template_data['name']}")


async def demo_template_rendering():
    """Демонстрация рендеринга шаблонов."""
    print("\n" + "="*60)
    print("🎨 ДЕМОНСТРАЦИЯ РЕНДЕРИНГА ШАБЛОНОВ")
    print("="*60)
    
    template_service = TemplateService()
    
    # Создаем простой шаблон
    template = NotificationTemplate(
        id="demo_template",
        name="Demo Template",
        notification_type=NotificationType.SUBSCRIPTION_EXPIRING_1_DAY,
        priority=NotificationPriority.HIGH,
        channel=NotificationChannel.TELEGRAM,
        subject_template="Подписка истекает!",
        body_template='''⚠️ Внимание, {{ user_name }}!

Ваша подписка истекает через {{ days_left }} {{ days_left|pluralize("день,дня,дней") }}.

💰 Стоимость продления: {{ price|currency }}
📅 Дата истечения: {{ expires_at|datetime("%d.%m.%Y") }}

{% if days_left <= 1 %}🔴 Это критически важно!{% endif %}''',
        is_active=True
    )
    
    # Контекстные данные
    context = {
        'user_name': 'Иван Петров',
        'days_left': 1,
        'price': 299.99,
        'expires_at': datetime.now() + timedelta(days=1)
    }
    
    # Рендерим шаблон
    result = await template_service.render_template(template, context)
    
    print(f"📧 Заголовок: {result.subject}")
    print(f"📝 Тело сообщения:")
    print("-" * 40)
    print(result.body)
    print("-" * 40)
    print(f"🔧 Использованные переменные: {', '.join(result.variables_used)}")
    print(f"⏱️ Время рендеринга: {result.render_time_ms}мс")


async def demo_notification_service():
    """Демонстрация сервиса уведомлений."""
    print("\n" + "="*60)
    print("📬 ДЕМОНСТРАЦИЯ СЕРВИСА УВЕДОМЛЕНИЙ")
    print("="*60)
    
    # Создаем компоненты
    repo = MockNotificationRepository()
    template_service = TemplateService()
    notification_service = AdvancedNotificationService(repo, template_service)
    
    # Создаем шаблоны
    await create_sample_templates(repo)
    
    print(f"\n📊 Создано шаблонов: {len(repo.templates)}")
    
    # Демонстрация отправки приветственного сообщения
    print("\n🎯 Отправка приветственного сообщения...")
    result = await notification_service.send_immediate_notification(
        user_id=12345,
        notification_type=NotificationType.WELCOME_MESSAGE,
        context_data={
            'user_name': 'Анна Смирнова'
        }
    )
    
    print(f"✅ Результат отправки: {'Успешно' if result.success else 'Ошибка'}")
    print(f"📨 ID сообщения: {result.message_id}")
    print(f"⏱️ Время обработки: {result.processing_time_ms}мс")
    
    # Демонстрация планирования уведомлений об истечении подписки
    print("\n📅 Планирование уведомлений об истечении подписки...")
    expires_at = datetime.now() + timedelta(days=5)
    
    await notification_service.schedule_subscription_expiry_notifications(
        user_id=12345,
        subscription_expires_at=expires_at,
        subscription_data={
            'plan': 'Premium',
            'price': 599.0
        }
    )
    
    pending = await repo.get_pending_notifications()
    print(f"📋 Запланировано уведомлений: {len(pending)}")
    
    for notification in pending:
        print(f"  • {notification.notification_type.value} - {notification.scheduled_at.strftime('%d.%m.%Y %H:%M')}")


async def demo_scheduler():
    """Демонстрация планировщика."""
    print("\n" + "="*60)
    print("⏰ ДЕМОНСТРАЦИЯ ПЛАНИРОВЩИКА")
    print("="*60)
    
    # Создаем компоненты
    repo = MockNotificationRepository()
    template_service = TemplateService()
    notification_service = AdvancedNotificationService(repo, template_service)
    
    # Создаем планировщик
    scheduler = NotificationScheduler(
        notification_service=notification_service,
        check_interval_seconds=2,  # Проверяем каждые 2 секунды для демо
        batch_size=10
    )
    
    print("🚀 Запуск планировщика...")
    
    # Используем контекстный менеджер
    async with scheduler.running_context():
        print("✅ Планировщик запущен")
        
        # Показываем статистику
        stats = scheduler.get_stats()
        print(f"📊 Статистика планировщика:")
        print(f"  • Интервал проверки: {stats['check_interval']}с")
        print(f"  • Размер пакета: {stats['batch_size']}")
        print(f"  • Статус: {'Работает' if stats['is_running'] else 'Остановлен'}")
        
        # Ждем немного
        print("⏳ Планировщик работает 5 секунд...")
        await asyncio.sleep(5)
        
        # Обновленная статистика
        updated_stats = scheduler.get_stats()
        print(f"📈 Обновленная статистика:")
        print(f"  • Обработано: {updated_stats['total_processed']}")
        print(f"  • Отправлено: {updated_stats['total_sent']}")
        print(f"  • Ошибок: {updated_stats['total_failed']}")
    
    print("🛑 Планировщик остановлен")


async def demo_template_validation():
    """Демонстрация валидации шаблонов."""
    print("\n" + "="*60)
    print("🔍 ДЕМОНСТРАЦИЯ ВАЛИДАЦИИ ШАБЛОНОВ")
    print("="*60)
    
    template_service = TemplateService()
    
    # Валидный шаблон
    print("✅ Проверка валидного шаблона:")
    valid_result = await template_service.validate_template(
        subject_template="Уведомление для {{ user_name }}",
        body_template="Привет, {{ user_name }}! Ваш баланс: {{ balance|currency }}"
    )
    
    print(f"  • Валидность: {'✅ Да' if valid_result['is_valid'] else '❌ Нет'}")
    print(f"  • Переменные: {', '.join(valid_result['variables'])}")
    print(f"  • Предупреждения: {len(valid_result['warnings'])}")
    
    # Невалидный шаблон
    print("\n❌ Проверка невалидного шаблона:")
    invalid_result = await template_service.validate_template(
        subject_template="Ошибка {{ unclosed",
        body_template="Привет {{ name }!"
    )
    
    print(f"  • Валидность: {'✅ Да' if invalid_result['is_valid'] else '❌ Нет'}")
    print(f"  • Ошибки: {len(invalid_result['errors'])}")
    if invalid_result['errors']:
        for error in invalid_result['errors']:
            print(f"    - {error}")
    
    # Доступные фильтры
    print("\n🔧 Доступные фильтры:")
    filters = template_service.get_available_filters()
    for filter_name, description in filters.items():
        print(f"  • {filter_name}: {description}")


async def main():
    """Главная функция демонстрации."""
    print("🎉 ДЕМОНСТРАЦИЯ СИСТЕМЫ УВЕДОМЛЕНИЙ")
    print("=" * 60)
    print("Эта демонстрация показывает возможности новой системы уведомлений:")
    print("• Рендеринг шаблонов с Jinja2")
    print("• Отправка немедленных уведомлений")
    print("• Планирование уведомлений")
    print("• Автоматический планировщик")
    print("• Валидация шаблонов")
    
    try:
        # Запускаем демонстрации
        await demo_template_rendering()
        await demo_notification_service()
        await demo_scheduler()
        await demo_template_validation()
        
        print("\n" + "="*60)
        print("🎊 ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!")
        print("="*60)
        print("Система уведомлений готова к использованию.")
        print("Следующие шаги:")
        print("• Интеграция с реальной базой данных")
        print("• Подключение к Telegram Bot API")
        print("• Создание админ-панели для управления шаблонами")
        print("• Настройка мониторинга и алертов")
        
    except Exception as e:
        print(f"\n❌ Ошибка во время демонстрации: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Запуск демонстрации
    asyncio.run(main())
