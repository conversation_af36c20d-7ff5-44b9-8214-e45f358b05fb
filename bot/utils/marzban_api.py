import time
import asyncio
from typing import Optional, Dict, Any, List
import aiohttp
import requests

from db.methods import get_marzban_profile_db
from utils.logging_config import get_logger, log_api_call, set_correlation_id
from utils.metrics import metrics, measure_time
import glv

logger = get_logger(__name__)


class MarzbanAPIError(Exception):
    """Исключение для ошибок Marzban API"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_body: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_body = response_body

PROTOCOLS = {
    "vmess": [
        {},
        ["VMess TCP"]
    ],
    "vless": [
        {
            "flow": ""
        },
        ["VLESS TCP REALITY"]
    ],
    "trojan": [
        {},
        ["Trojan Websocket TLS"]
    ],
    "shadowsocks": [
        {
            "method": "chacha20-ietf-poly1305"
        },
        ["Shadowsocks TCP"]
    ]
}

class MarzbanAPI:
    """
    Улучшенный клиент для работы с Marzban API.
    Включает логирование, метрики и обработку ошибок.
    """

    def __init__(self, host: str, username: str, password: str, timeout: int = 30):
        self.host = host.rstrip('/')
        self.username = username
        self.password = password
        self.timeout = timeout
        self.token: Optional[str] = None
        self.token_expires_at: Optional[float] = None

        logger.info(
            f"Initialized Marzban API client for {self.host}",
            extra={
                "marzban_host": self.host,
                "username": self.username,
                "event_type": "marzban_init"
            }
        )

    async def _send_request(
        self,
        method: str,
        path: str,
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Dict[str, Any]] = None,
        retry_count: int = 3
    ) -> Dict[str, Any]:
        """
        Отправляет HTTP запрос к Marzban API с retry механизмом.
        """
        url = f"{self.host}{path}"
        start_time = time.time()

        for attempt in range(1, retry_count + 1):
            try:
                timeout = aiohttp.ClientTimeout(total=self.timeout)

                async with aiohttp.ClientSession(timeout=timeout) as session:
                    logger.debug(
                        f"Sending {method} request to {path} (attempt {attempt})",
                        extra={
                            "method": method,
                            "path": path,
                            "attempt": attempt,
                            "event_type": "marzban_request"
                        }
                    )

                    async with session.request(
                        method,
                        url,
                        headers=headers,
                        json=data
                    ) as response:
                        duration = time.time() - start_time
                        response_text = await response.text()

                        # Логируем API вызов
                        log_api_call(
                            endpoint=path,
                            method=method,
                            status_code=response.status,
                            duration_ms=duration * 1000,
                            attempt=attempt
                        )

                        # Записываем метрику
                        metrics.record_marzban_api_call(
                            operation=path.split('/')[-1] or 'root',
                            status='success' if 200 <= response.status < 300 else 'error',
                            duration=duration
                        )

                        if 200 <= response.status < 300:
                            try:
                                body = await response.json()
                                logger.debug(
                                    f"Successful {method} {path} - {response.status}",
                                    extra={
                                        "method": method,
                                        "path": path,
                                        "status": response.status,
                                        "duration_ms": duration * 1000,
                                        "event_type": "marzban_success"
                                    }
                                )
                                return body
                            except Exception as json_error:
                                logger.warning(
                                    f"Failed to parse JSON response: {json_error}",
                                    extra={
                                        "response_text": response_text[:500],
                                        "event_type": "marzban_json_error"
                                    }
                                )
                                return {"raw_response": response_text}
                        else:
                            error_msg = f"HTTP {response.status}: {response_text}"

                            # Для некоторых ошибок не повторяем запрос
                            if response.status in [400, 401, 403, 404]:
                                raise MarzbanAPIError(
                                    error_msg,
                                    status_code=response.status,
                                    response_body=response_text
                                )

                            # Для серверных ошибок повторяем
                            if attempt < retry_count:
                                delay = 2 ** (attempt - 1)  # Exponential backoff
                                logger.warning(
                                    f"Request failed, retrying in {delay}s: {error_msg}",
                                    extra={
                                        "method": method,
                                        "path": path,
                                        "status": response.status,
                                        "attempt": attempt,
                                        "retry_delay": delay,
                                        "event_type": "marzban_retry"
                                    }
                                )
                                await asyncio.sleep(delay)
                                continue
                            else:
                                raise MarzbanAPIError(
                                    error_msg,
                                    status_code=response.status,
                                    response_body=response_text
                                )

            except aiohttp.ClientError as e:
                duration = time.time() - start_time

                # Записываем метрику ошибки
                metrics.record_marzban_api_call(
                    operation=path.split('/')[-1] or 'root',
                    status='network_error',
                    duration=duration
                )

                if attempt < retry_count:
                    delay = 2 ** (attempt - 1)
                    logger.warning(
                        f"Network error, retrying in {delay}s: {str(e)}",
                        extra={
                            "method": method,
                            "path": path,
                            "attempt": attempt,
                            "retry_delay": delay,
                            "error": str(e),
                            "event_type": "marzban_network_error"
                        }
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(
                        f"Network error after {retry_count} attempts: {str(e)}",
                        extra={
                            "method": method,
                            "path": path,
                            "attempts": retry_count,
                            "error": str(e),
                            "event_type": "marzban_network_failure"
                        }
                    )
                    raise MarzbanAPIError(f"Network error: {str(e)}")

        # Этот код не должен выполняться, но на всякий случай
        raise MarzbanAPIError("Unexpected error in request loop")

    @measure_time('get_token')
    async def get_token(self) -> str:
        """
        Получает токен авторизации от Marzban API.
        """
        # Проверяем, не истек ли токен
        if (self.token and self.token_expires_at and
            time.time() < self.token_expires_at - 60):  # Обновляем за минуту до истечения
            return self.token

        logger.info("Requesting new Marzban token")

        data = {
            "username": self.username,
            "password": self.password
        }

        try:
            # Используем синхронный requests для получения токена
            # так как это критическая операция
            start_time = time.time()
            response = requests.post(
                f"{self.host}/api/admin/token",
                data=data,
                timeout=self.timeout
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                token_data = response.json()
                self.token = token_data["access_token"]

                # Устанавливаем время истечения (обычно токены живут 24 часа)
                self.token_expires_at = time.time() + 23 * 60 * 60  # 23 часа

                logger.info(
                    "Successfully obtained Marzban token",
                    extra={
                        "duration_ms": duration * 1000,
                        "event_type": "marzban_token_success"
                    }
                )

                metrics.record_marzban_api_call(
                    operation="get_token",
                    status="success",
                    duration=duration
                )

                return self.token
            else:
                error_msg = f"Failed to get token: HTTP {response.status_code}"
                logger.error(
                    error_msg,
                    extra={
                        "status_code": response.status_code,
                        "response": response.text[:500],
                        "event_type": "marzban_token_error"
                    }
                )

                metrics.record_marzban_api_call(
                    operation="get_token",
                    status="error",
                    duration=duration
                )

                raise MarzbanAPIError(error_msg, response.status_code, response.text)

        except requests.RequestException as e:
            logger.error(
                f"Network error getting token: {str(e)}",
                extra={
                    "error": str(e),
                    "event_type": "marzban_token_network_error"
                }
            )
            raise MarzbanAPIError(f"Network error getting token: {str(e)}")

    async def _get_headers(self) -> Dict[str, str]:
        """Получает заголовки с актуальным токеном"""
        token = await self.get_token()
        return {
            'Authorization': f"Bearer {token}",
            'Content-Type': 'application/json'
        }

    @measure_time('get_user')
    async def get_user(self, username: str) -> Dict[str, Any]:
        """Получает информацию о пользователе"""
        headers = await self._get_headers()

        logger.debug(
            f"Getting user info for {username}",
            extra={
                "username": username,
                "event_type": "marzban_get_user"
            }
        )

        try:
            result = await self._send_request("GET", f"/api/user/{username}", headers=headers)

            logger.info(
                f"Successfully retrieved user {username}",
                extra={
                    "username": username,
                    "user_status": result.get('status'),
                    "event_type": "marzban_get_user_success"
                }
            )

            return result

        except MarzbanAPIError as e:
            if e.status_code == 404:
                logger.info(
                    f"User {username} not found",
                    extra={
                        "username": username,
                        "event_type": "marzban_user_not_found"
                    }
                )
            else:
                logger.error(
                    f"Failed to get user {username}: {str(e)}",
                    extra={
                        "username": username,
                        "error": str(e),
                        "status_code": e.status_code,
                        "event_type": "marzban_get_user_error"
                    }
                )
            raise

    @measure_time('get_users')
    async def get_users(self, offset: int = 0, limit: int = 100) -> Dict[str, Any]:
        """Получает список пользователей"""
        headers = await self._get_headers()

        logger.debug(
            f"Getting users list (offset={offset}, limit={limit})",
            extra={
                "offset": offset,
                "limit": limit,
                "event_type": "marzban_get_users"
            }
        )

        result = await self._send_request(
            "GET",
            f"/api/users?offset={offset}&limit={limit}",
            headers=headers
        )

        logger.info(
            f"Retrieved {len(result.get('users', []))} users",
            extra={
                "users_count": len(result.get('users', [])),
                "total": result.get('total', 0),
                "event_type": "marzban_get_users_success"
            }
        )

        return result

    @measure_time('add_user')
    async def add_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Создает нового пользователя"""
        headers = await self._get_headers()
        username = user_data.get('username', 'unknown')

        logger.info(
            f"Creating user {username}",
            extra={
                "username": username,
                "proxies": list(user_data.get('proxies', {}).keys()),
                "expire": user_data.get('expire'),
                "data_limit": user_data.get('data_limit'),
                "event_type": "marzban_add_user"
            }
        )

        try:
            result = await self._send_request("POST", "/api/user", headers=headers, data=user_data)

            logger.info(
                f"Successfully created user {username}",
                extra={
                    "username": username,
                    "subscription_url": result.get('subscription_url'),
                    "event_type": "marzban_add_user_success"
                }
            )

            # Записываем метрику создания подписки
            metrics.record_subscription_created(
                subscription_type="unknown",  # Можно улучшить определение типа
                payment_method="unknown"
            )

            return result

        except MarzbanAPIError as e:
            logger.error(
                f"Failed to create user {username}: {str(e)}",
                extra={
                    "username": username,
                    "error": str(e),
                    "status_code": e.status_code,
                    "event_type": "marzban_add_user_error"
                }
            )
            raise

    @measure_time('modify_user')
    async def modify_user(self, username: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Изменяет данные пользователя"""
        headers = await self._get_headers()

        logger.info(
            f"Modifying user {username}",
            extra={
                "username": username,
                "status": user_data.get('status'),
                "expire": user_data.get('expire'),
                "data_limit": user_data.get('data_limit'),
                "event_type": "marzban_modify_user"
            }
        )

        try:
            result = await self._send_request(
                "PUT",
                f"/api/user/{username}",
                headers=headers,
                data=user_data
            )

            logger.info(
                f"Successfully modified user {username}",
                extra={
                    "username": username,
                    "new_status": result.get('status'),
                    "event_type": "marzban_modify_user_success"
                }
            )

            return result

        except MarzbanAPIError as e:
            logger.error(
                f"Failed to modify user {username}: {str(e)}",
                extra={
                    "username": username,
                    "error": str(e),
                    "status_code": e.status_code,
                    "event_type": "marzban_modify_user_error"
                }
            )
            raise

    @measure_time('delete_user')
    async def delete_user(self, username: str) -> bool:
        """Удаляет пользователя"""
        headers = await self._get_headers()

        logger.info(
            f"Deleting user {username}",
            extra={
                "username": username,
                "event_type": "marzban_delete_user"
            }
        )

        try:
            await self._send_request("DELETE", f"/api/user/{username}", headers=headers)

            logger.info(
                f"Successfully deleted user {username}",
                extra={
                    "username": username,
                    "event_type": "marzban_delete_user_success"
                }
            )

            return True

        except MarzbanAPIError as e:
            logger.error(
                f"Failed to delete user {username}: {str(e)}",
                extra={
                    "username": username,
                    "error": str(e),
                    "status_code": e.status_code,
                    "event_type": "marzban_delete_user_error"
                }
            )
            raise



    @measure_time('get_system_stats')
    async def get_system_stats(self) -> Dict[str, Any]:
        """Получает системную статистику"""
        headers = await self._get_headers()

        logger.debug("Getting system stats")

        result = await self._send_request("GET", "/api/system", headers=headers)

        logger.debug(
            "Retrieved system stats",
            extra={
                "event_type": "marzban_get_system_stats_success"
            }
        )

        return result

    @measure_time('get_nodes')
    async def get_nodes(self) -> List[Dict[str, Any]]:
        """Получает список всех нод"""
        headers = await self._get_headers()

        logger.debug("Getting nodes list")

        try:
            result = await self._send_request("GET", "/api/nodes", headers=headers)

            # Marzban возвращает список нод
            if isinstance(result, list):
                nodes = result
            else:
                # Если API изменился и возвращает объект
                nodes = result.get('nodes', [])

            logger.debug(
                f"Retrieved {len(nodes)} nodes",
                extra={
                    "nodes_count": len(nodes),
                    "event_type": "marzban_get_nodes_success"
                }
            )

            return nodes

        except Exception as e:
            logger.error(
                f"Failed to get nodes: {e}",
                extra={
                    "error": str(e),
                    "event_type": "marzban_get_nodes_error"
                }
            )
            return []

    @measure_time('get_node_stats')
    async def get_node_stats(self, node_id: str) -> Optional[Dict[str, Any]]:
        """Получает статистику конкретной ноды"""
        headers = await self._get_headers()

        logger.debug(f"Getting stats for node {node_id}")

        try:
            result = await self._send_request("GET", f"/api/nodes/{node_id}/usage", headers=headers)

            logger.debug(
                f"Retrieved stats for node {node_id}",
                extra={
                    "node_id": node_id,
                    "event_type": "marzban_get_node_stats_success"
                }
            )

            return result

        except Exception as e:
            logger.error(
                f"Failed to get stats for node {node_id}: {e}",
                extra={
                    "node_id": node_id,
                    "error": str(e),
                    "event_type": "marzban_get_node_stats_error"
                }
            )
            return None

    @measure_time('get_node_status')
    async def get_node_status(self, node_id: str) -> Optional[Dict[str, Any]]:
        """Получает статус конкретной ноды"""
        headers = await self._get_headers()

        logger.debug(f"Getting status for node {node_id}")

        try:
            result = await self._send_request("GET", f"/api/nodes/{node_id}", headers=headers)

            logger.debug(
                f"Retrieved status for node {node_id}",
                extra={
                    "node_id": node_id,
                    "status": result.get('status', 'unknown'),
                    "event_type": "marzban_get_node_status_success"
                }
            )

            return result

        except Exception as e:
            logger.error(
                f"Failed to get status for node {node_id}: {e}",
                extra={
                    "node_id": node_id,
                    "error": str(e),
                    "event_type": "marzban_get_node_status_error"
                }
            )
            return None

    @measure_time('get_inbounds')
    async def get_inbounds(self) -> List[Dict[str, Any]]:
        """Получает список всех inbounds"""
        headers = await self._get_headers()

        logger.debug("Getting inbounds list")

        try:
            result = await self._send_request("GET", "/api/inbounds", headers=headers)

            # Marzban возвращает список inbounds
            if isinstance(result, list):
                inbounds = result
            else:
                # Если API изменился и возвращает объект
                inbounds = result.get('inbounds', [])

            logger.debug(
                f"Retrieved {len(inbounds)} inbounds",
                extra={
                    "inbounds_count": len(inbounds),
                    "event_type": "marzban_get_inbounds_success"
                }
            )

            return inbounds

        except Exception as e:
            logger.error(
                f"Failed to get inbounds: {e}",
                extra={
                    "error": str(e),
                    "event_type": "marzban_get_inbounds_error"
                }
            )
            return []

    @measure_time('get_core_stats')
    async def get_core_stats(self) -> Dict[str, Any]:
        """Получает статистику ядра системы"""
        headers = await self._get_headers()

        logger.debug("Getting core stats")

        try:
            result = await self._send_request("GET", "/api/core", headers=headers)

            logger.debug(
                "Retrieved core stats",
                extra={
                    "event_type": "marzban_get_core_stats_success"
                }
            )

            return result

        except Exception as e:
            logger.error(
                f"Failed to get core stats: {e}",
                extra={
                    "error": str(e),
                    "event_type": "marzban_get_core_stats_error"
                }
            )
            return {}


# Создаем экземпляр нового API клиента для обратной совместимости
class Marzban(MarzbanAPI):
    """Класс для обратной совместимости"""

    def __init__(self, ip: str, login: str, passwd: str):
        super().__init__(host=ip, username=login, password=passwd)
        # Для совместимости с существующим кодом
        self.ip = ip
        self.login = login
        self.passwd = passwd

def get_protocols() -> dict:
    proxies = {}
    inbounds = {}
    
    for proto in glv.config['PROTOCOLS']:
        l = proto.lower()
        if l not in PROTOCOLS:
            continue
        proxies[l] = PROTOCOLS[l][0]
        inbounds[l] = PROTOCOLS[l][1]
    return {
        "proxies": proxies,
        "inbounds": inbounds
    }

panel = Marzban(glv.config['PANEL_HOST'], glv.config['PANEL_USER'], glv.config['PANEL_PASS'])
ps = get_protocols()

async def ensure_token():
    """Обеспечивает наличие токена для панели"""
    try:
        await panel.get_token()
        logger.debug("Marzban token ensured")
    except Exception as e:
        logger.error(
            f"Failed to ensure Marzban token: {e}",
            extra={
                "error": str(e),
                "event_type": "marzban_token_ensure_error"
            }
        )
        # Не блокируем запуск приложения из-за проблем с панелью

async def check_if_user_exists(name: str) -> bool:
    """Проверяет существование пользователя в Marzban"""
    try:
        await ensure_token()
        await panel.get_user(name)

        logger.debug(
            f"User {name} exists in Marzban",
            extra={
                "username": name,
                "event_type": "marzban_user_exists_check"
            }
        )

        return True
    except MarzbanAPIError as e:
        if e.status_code == 404:
            logger.debug(
                f"User {name} does not exist in Marzban",
                extra={
                    "username": name,
                    "event_type": "marzban_user_not_exists"
                }
            )
            return False
        else:
            logger.error(
                f"Error checking if user {name} exists: {e}",
                extra={
                    "username": name,
                    "error": str(e),
                    "status_code": e.status_code,
                    "event_type": "marzban_user_exists_error"
                }
            )
            return False
    except Exception as e:
        logger.error(
            f"Unexpected error checking if user {name} exists: {e}",
            extra={
                "username": name,
                "error": str(e),
                "event_type": "marzban_user_exists_unexpected_error"
            }
        )
        return False

async def get_marzban_profile(tg_id: int):
    """Получает профиль пользователя из Marzban по Telegram ID"""
    try:
        result = await get_marzban_profile_db(tg_id)
        if not result:
            logger.warning(
                f"No VPN profile found for user {tg_id}",
                extra={
                    "tg_id": tg_id,
                    "event_type": "marzban_profile_not_found_db"
                }
            )
            return None

        res = await check_if_user_exists(result.vpn_id)
        if not res:
            logger.warning(
                f"VPN user {result.vpn_id} not found in Marzban for tg_id {tg_id}",
                extra={
                    "tg_id": tg_id,
                    "vpn_id": result.vpn_id,
                    "event_type": "marzban_profile_not_found_panel"
                }
            )
            return None

        profile = await panel.get_user(result.vpn_id)

        logger.info(
            f"Retrieved Marzban profile for user {tg_id}",
            extra={
                "tg_id": tg_id,
                "vpn_id": result.vpn_id,
                "status": profile.get('status'),
                "expire": profile.get('expire'),
                "event_type": "marzban_profile_retrieved"
            }
        )

        return profile

    except Exception as e:
        logger.error(
            f"Error getting Marzban profile for user {tg_id}: {e}",
            extra={
                "tg_id": tg_id,
                "error": str(e),
                "event_type": "marzban_profile_error"
            }
        )
        return None

async def generate_test_subscription(username: str):
    """Создает или продлевает тестовую подписку"""
    logger.info(
        f"Generating test subscription for {username}",
        extra={
            "username": username,
            "period_hours": glv.config['PERIOD_LIMIT'],
            "event_type": "marzban_test_subscription_start"
        }
    )

    try:
        res = await check_if_user_exists(username)

        if res:
            # Пользователь существует - продлеваем подписку
            user = await panel.get_user(username)
            user['status'] = 'active'

            current_time = time.time()
            if user['expire'] < current_time:
                # Подписка истекла - устанавливаем новый период
                user['expire'] = get_test_subscription(glv.config['PERIOD_LIMIT'])
                logger.info(
                    f"Renewing expired test subscription for {username}",
                    extra={
                        "username": username,
                        "old_expire": user['expire'],
                        "new_expire": user['expire'],
                        "event_type": "marzban_test_subscription_renew_expired"
                    }
                )
            else:
                # Подписка активна - добавляем время
                user['expire'] += get_test_subscription(glv.config['PERIOD_LIMIT'], True)
                logger.info(
                    f"Extending active test subscription for {username}",
                    extra={
                        "username": username,
                        "new_expire": user['expire'],
                        "event_type": "marzban_test_subscription_extend"
                    }
                )

            result = await panel.modify_user(username, user)

        else:
            # Создаем нового пользователя
            user = {
                'username': username,
                'proxies': ps["proxies"],
                'inbounds': ps["inbounds"],
                'expire': get_test_subscription(glv.config['PERIOD_LIMIT']),
                'data_limit': 0,
                'data_limit_reset_strategy': "no_reset",
            }

            logger.info(
                f"Creating new test subscription for {username}",
                extra={
                    "username": username,
                    "expire": user['expire'],
                    "proxies": list(user['proxies'].keys()),
                    "event_type": "marzban_test_subscription_create"
                }
            )

            result = await panel.add_user(user)

        logger.info(
            f"Successfully generated test subscription for {username}",
            extra={
                "username": username,
                "subscription_url": result.get('subscription_url'),
                "event_type": "marzban_test_subscription_success"
            }
        )

        return result

    except Exception as e:
        logger.error(
            f"Failed to generate test subscription for {username}: {e}",
            extra={
                "username": username,
                "error": str(e),
                "event_type": "marzban_test_subscription_error"
            }
        )
        raise

async def generate_marzban_subscription(username: str, good):
    """Создает или продлевает платную подписку"""
    logger.info(
        f"Generating subscription for {username}",
        extra={
            "username": username,
            "months": good.get('months', 'unknown'),
            "good_name": good.get('name', 'unknown'),
            "event_type": "marzban_subscription_start"
        }
    )

    try:
        res = await check_if_user_exists(username)

        if res:
            # Пользователь существует - продлеваем подписку
            user = await panel.get_user(username)
            user['status'] = 'active'

            current_time = time.time()
            if user['expire'] < current_time:
                # Подписка истекла - устанавливаем новый период
                user['expire'] = get_subscription_end_date(good['months'])
                logger.info(
                    f"Renewing expired subscription for {username}",
                    extra={
                        "username": username,
                        "months": good['months'],
                        "new_expire": user['expire'],
                        "event_type": "marzban_subscription_renew_expired"
                    }
                )
            else:
                # Подписка активна - добавляем время
                user['expire'] += get_subscription_end_date(good['months'], True)
                logger.info(
                    f"Extending active subscription for {username}",
                    extra={
                        "username": username,
                        "months": good['months'],
                        "new_expire": user['expire'],
                        "event_type": "marzban_subscription_extend"
                    }
                )

            result = await panel.modify_user(username, user)

            # Записываем метрику продления
            metrics.record_subscription_renewal(
                subscription_type=good.get('name', 'unknown'),
                renewal_type="extend" if user['expire'] >= current_time else "renew"
            )

        else:
            # Создаем нового пользователя
            user = {
                'username': username,
                'proxies': ps["proxies"],
                'inbounds': ps["inbounds"],
                'expire': get_subscription_end_date(good['months']),
                'data_limit': 0,
                'data_limit_reset_strategy': "no_reset",
            }

            logger.info(
                f"Creating new subscription for {username}",
                extra={
                    "username": username,
                    "months": good['months'],
                    "expire": user['expire'],
                    "proxies": list(user['proxies'].keys()),
                    "event_type": "marzban_subscription_create"
                }
            )

            result = await panel.add_user(user)

        logger.info(
            f"Successfully generated subscription for {username}",
            extra={
                "username": username,
                "subscription_url": result.get('subscription_url'),
                "months": good['months'],
                "event_type": "marzban_subscription_success"
            }
        )

        return result

    except Exception as e:
        logger.error(
            f"Failed to generate subscription for {username}: {e}",
            extra={
                "username": username,
                "error": str(e),
                "good": good,
                "event_type": "marzban_subscription_error"
            }
        )
        raise

def get_test_subscription(hours: int, additional= False) -> int:
    return (0 if additional else int(time.time())) + 60 * 60 * hours

def get_subscription_end_date(months: int, additional = False) -> int:
    return (0 if additional else int(time.time())) + 60 * 60 * 24 * 30 * months
