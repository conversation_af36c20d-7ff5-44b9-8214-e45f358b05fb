"""
Конфигурация структурированного логирования для VPN бота.
Обеспечивает JSON формат логов с correlation ID для трассировки.
"""

import json
import logging
import sys
import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from contextvars import ContextVar

# Context variable для correlation ID
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class StructuredFormatter(logging.Formatter):
    """
    Форматтер для структурированного логирования в JSON формате.
    Добавляет correlation_id, timestamp и другие метаданные.
    """
    
    def __init__(self, service_name: str = "vpn-bot"):
        super().__init__()
        self.service_name = service_name
    
    def format(self, record: logging.LogRecord) -> str:
        """Форматирует лог запись в JSON"""
        
        # Базовая структура лога
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "service": self.service_name,
            "logger": record.name,
            "message": record.getMessage(),
            "correlation_id": correlation_id.get(),
        }
        
        # Добавляем информацию о файле и строке
        if record.pathname:
            log_entry["source"] = {
                "file": record.pathname,
                "line": record.lineno,
                "function": record.funcName
            }
        
        # Добавляем exception информацию если есть
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info)
            }
        
        # Добавляем дополнительные поля из extra
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                'filename', 'module', 'lineno', 'funcName', 'created', 
                'msecs', 'relativeCreated', 'thread', 'threadName', 
                'processName', 'process', 'getMessage', 'exc_info', 
                'exc_text', 'stack_info'
            }:
                extra_fields[key] = value
        
        if extra_fields:
            log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False, default=str)


class SensitiveDataFilter(logging.Filter):
    """
    Фильтр для удаления чувствительных данных из логов.
    Маскирует пароли, токены и другую приватную информацию.
    """
    
    SENSITIVE_KEYS = {
        'password', 'token', 'secret', 'key', 'auth', 'credential',
        'bot_token', 'api_key', 'private_key', 'access_token'
    }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Фильтрует чувствительные данные"""
        
        # Маскируем чувствительные данные в сообщении
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = self._mask_sensitive_data(record.msg)
        
        # Маскируем чувствительные данные в extra полях
        for key, value in record.__dict__.items():
            if any(sensitive in key.lower() for sensitive in self.SENSITIVE_KEYS):
                if isinstance(value, str) and len(value) > 4:
                    record.__dict__[key] = value[:2] + "*" * (len(value) - 4) + value[-2:]
                else:
                    record.__dict__[key] = "***"
        
        return True
    
    def _mask_sensitive_data(self, message: str) -> str:
        """Маскирует чувствительные данные в сообщении"""
        # Простая маскировка для демонстрации
        # В реальном проекте можно использовать regex для более сложных паттернов
        for sensitive in self.SENSITIVE_KEYS:
            if sensitive in message.lower():
                # Заменяем потенциальные токены/пароли на звездочки
                import re
                pattern = rf'{sensitive}["\s]*[:=]["\s]*([^\s"]+)'
                message = re.sub(pattern, f'{sensitive}="***"', message, flags=re.IGNORECASE)
        
        return message


def setup_logging(
    service_name: str = "vpn-bot",
    log_level: str = "INFO",
    enable_json: bool = True,
    enable_console: bool = True
) -> None:
    """
    Настраивает структурированное логирование для приложения.
    
    Args:
        service_name: Имя сервиса для логов
        log_level: Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        enable_json: Включить JSON форматирование
        enable_console: Включить вывод в консоль
    """
    
    # Получаем root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Очищаем существующие handlers
    root_logger.handlers.clear()
    
    if enable_console:
        # Создаем console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        
        if enable_json:
            # Используем структурированный форматтер
            formatter = StructuredFormatter(service_name)
        else:
            # Используем обычный форматтер для разработки
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        console_handler.setFormatter(formatter)
        
        # Добавляем фильтр для чувствительных данных
        console_handler.addFilter(SensitiveDataFilter())
        
        root_logger.addHandler(console_handler)
    
    # Настраиваем логирование для aiogram (уменьшаем verbosity)
    logging.getLogger("aiogram").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Получает logger с заданным именем.
    
    Args:
        name: Имя logger'а (обычно __name__)
    
    Returns:
        Настроенный logger
    """
    return logging.getLogger(name)


def set_correlation_id(corr_id: Optional[str] = None) -> str:
    """
    Устанавливает correlation ID для текущего контекста.
    
    Args:
        corr_id: Correlation ID. Если None, генерируется новый UUID
    
    Returns:
        Установленный correlation ID
    """
    if corr_id is None:
        corr_id = str(uuid.uuid4())
    
    correlation_id.set(corr_id)
    return corr_id


def get_correlation_id() -> Optional[str]:
    """
    Получает текущий correlation ID.
    
    Returns:
        Текущий correlation ID или None
    """
    return correlation_id.get()


def log_function_call(func_name: str, **kwargs) -> None:
    """
    Логирует вызов функции с параметрами.
    
    Args:
        func_name: Имя функции
        **kwargs: Параметры функции
    """
    logger = get_logger(__name__)
    logger.info(
        f"Function call: {func_name}",
        extra={
            "function_name": func_name,
            "parameters": kwargs,
            "event_type": "function_call"
        }
    )


def log_user_action(user_id: int, action: str, **details) -> None:
    """
    Логирует действие пользователя.
    
    Args:
        user_id: ID пользователя
        action: Тип действия
        **details: Дополнительные детали
    """
    logger = get_logger(__name__)
    logger.info(
        f"User action: {action}",
        extra={
            "user_id": user_id,
            "action": action,
            "details": details,
            "event_type": "user_action"
        }
    )


def log_api_call(endpoint: str, method: str, status_code: int, duration_ms: float, **details) -> None:
    """
    Логирует API вызов.
    
    Args:
        endpoint: Endpoint API
        method: HTTP метод
        status_code: Код ответа
        duration_ms: Длительность в миллисекундах
        **details: Дополнительные детали
    """
    logger = get_logger(__name__)
    
    level = logging.INFO
    if status_code >= 400:
        level = logging.WARNING
    if status_code >= 500:
        level = logging.ERROR
    
    logger.log(
        level,
        f"API call: {method} {endpoint} - {status_code}",
        extra={
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "duration_ms": duration_ms,
            "details": details,
            "event_type": "api_call"
        }
    )


def log_payment_event(user_id: int, event_type: str, amount: float, currency: str, **details) -> None:
    """
    Логирует события платежей.
    
    Args:
        user_id: ID пользователя
        event_type: Тип события (created, succeeded, failed, etc.)
        amount: Сумма платежа
        currency: Валюта
        **details: Дополнительные детали
    """
    logger = get_logger(__name__)
    logger.info(
        f"Payment event: {event_type}",
        extra={
            "user_id": user_id,
            "event_type": "payment",
            "payment_event": event_type,
            "amount": amount,
            "currency": currency,
            "details": details
        }
    )
