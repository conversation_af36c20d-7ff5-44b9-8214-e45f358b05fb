"""
Сервис для работы с зашифрованными конфигурационными данными.
Обеспечивает безопасное хранение и получение API ключей, паролей и других чувствительных данных.
"""

import hashlib
import json
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, update, delete
from sqlalchemy.exc import IntegrityError

from db.models import EncryptedConfig, UserSavedPaymentData
from utils.encryption import encryption
from utils.logging_config import get_logger

logger = get_logger(__name__)


class ConfigEncryptionService:
    """
    Сервис для работы с зашифрованными конфигурационными данными.
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def store_config(self, key_name: str, value: str, description: Optional[str] = None) -> bool:
        """
        Сохраняет зашифрованное значение конфигурации.
        
        Args:
            key_name: Имя ключа конфигурации
            value: Значение для шифрования и сохранения
            description: Описание ключа (опционально)
            
        Returns:
            True если успешно сохранено, False иначе
        """
        try:
            encrypted_value = encryption.encrypt(value)
            
            # Проверяем, существует ли уже такой ключ
            existing = await self.session.execute(
                select(EncryptedConfig).where(EncryptedConfig.key_name == key_name)
            )
            existing_config = existing.scalar_one_or_none()
            
            if existing_config:
                # Обновляем существующий ключ
                await self.session.execute(
                    update(EncryptedConfig)
                    .where(EncryptedConfig.key_name == key_name)
                    .values(
                        encrypted_value=encrypted_value,
                        description=description
                    )
                )
                logger.info(f"Updated encrypted config key: {key_name}")
            else:
                # Создаем новый ключ
                await self.session.execute(
                    insert(EncryptedConfig).values(
                        key_name=key_name,
                        encrypted_value=encrypted_value,
                        description=description
                    )
                )
                logger.info(f"Created new encrypted config key: {key_name}")
            
            await self.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to store encrypted config {key_name}: {e}", exc_info=True)
            await self.session.rollback()
            return False
    
    async def get_config(self, key_name: str) -> Optional[str]:
        """
        Получает и дешифрует значение конфигурации.
        
        Args:
            key_name: Имя ключа конфигурации
            
        Returns:
            Расшифрованное значение или None если ключ не найден
        """
        try:
            result = await self.session.execute(
                select(EncryptedConfig).where(EncryptedConfig.key_name == key_name)
            )
            config = result.scalar_one_or_none()
            
            if not config:
                logger.warning(f"Encrypted config key not found: {key_name}")
                return None
            
            decrypted_value = encryption.decrypt(config.encrypted_value)
            return decrypted_value
            
        except Exception as e:
            logger.error(f"Failed to get encrypted config {key_name}: {e}", exc_info=True)
            return None
    
    async def delete_config(self, key_name: str) -> bool:
        """
        Удаляет зашифрованную конфигурацию.
        
        Args:
            key_name: Имя ключа конфигурации
            
        Returns:
            True если успешно удалено, False иначе
        """
        try:
            await self.session.execute(
                delete(EncryptedConfig).where(EncryptedConfig.key_name == key_name)
            )
            await self.session.commit()
            logger.info(f"Deleted encrypted config key: {key_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete encrypted config {key_name}: {e}", exc_info=True)
            await self.session.rollback()
            return False
    
    async def list_config_keys(self) -> list[str]:
        """
        Возвращает список всех ключей конфигурации.
        
        Returns:
            Список имен ключей
        """
        try:
            result = await self.session.execute(
                select(EncryptedConfig.key_name)
            )
            keys = [row[0] for row in result.fetchall()]
            return keys
            
        except Exception as e:
            logger.error(f"Failed to list config keys: {e}", exc_info=True)
            return []


class UserPaymentDataService:
    """
    Сервис для работы с зашифрованными данными платежных методов пользователей.
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    def _generate_data_hash(self, data: str) -> str:
        """
        Генерирует хеш для идентификации платежных данных.
        
        Args:
            data: Данные для хеширования
            
        Returns:
            SHA-256 хеш данных
        """
        return hashlib.sha256(data.encode()).hexdigest()
    
    async def save_payment_data(
        self, 
        tg_id: int, 
        payment_method: str, 
        payment_data: Dict[str, Any]
    ) -> bool:
        """
        Сохраняет зашифрованные данные платежного метода пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            payment_method: Тип платежного метода ('yookassa', 'cryptomus', etc.)
            payment_data: Данные платежного метода для шифрования
            
        Returns:
            True если успешно сохранено, False иначе
        """
        try:
            # Конвертируем данные в JSON для шифрования
            data_str = json.dumps(payment_data, sort_keys=True)
            data_hash = self._generate_data_hash(data_str)
            
            # Проверяем, не сохранены ли уже такие данные
            existing = await self.session.execute(
                select(UserSavedPaymentData).where(
                    UserSavedPaymentData.tg_id == tg_id,
                    UserSavedPaymentData.payment_method == payment_method,
                    UserSavedPaymentData.data_hash == data_hash
                )
            )
            
            if existing.scalar_one_or_none():
                logger.info(f"Payment data already exists for user {tg_id}, method {payment_method}")
                return True
            
            # Шифруем данные
            encrypted_data = encryption.encrypt(data_str)
            
            # Сохраняем в БД
            await self.session.execute(
                insert(UserSavedPaymentData).values(
                    tg_id=tg_id,
                    payment_method=payment_method,
                    encrypted_data=encrypted_data,
                    data_hash=data_hash,
                    is_active=True
                )
            )
            
            await self.session.commit()
            logger.info(f"Saved encrypted payment data for user {tg_id}, method {payment_method}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save payment data for user {tg_id}: {e}", exc_info=True)
            await self.session.rollback()
            return False
    
    async def get_user_payment_data(
        self, 
        tg_id: int, 
        payment_method: Optional[str] = None
    ) -> list[Dict[str, Any]]:
        """
        Получает зашифрованные данные платежных методов пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            payment_method: Тип платежного метода (опционально)
            
        Returns:
            Список расшифрованных данных платежных методов
        """
        try:
            query = select(UserSavedPaymentData).where(
                UserSavedPaymentData.tg_id == tg_id,
                UserSavedPaymentData.is_active == True
            )
            
            if payment_method:
                query = query.where(UserSavedPaymentData.payment_method == payment_method)
            
            result = await self.session.execute(query)
            payment_records = result.scalars().all()
            
            decrypted_data = []
            for record in payment_records:
                try:
                    decrypted_str = encryption.decrypt(record.encrypted_data)
                    # Парсим JSON данные
                    try:
                        parsed_data = json.loads(decrypted_str)
                    except json.JSONDecodeError:
                        # Если не JSON, оставляем как строку (для обратной совместимости)
                        parsed_data = decrypted_str

                    decrypted_data.append({
                        'id': record.id,
                        'payment_method': record.payment_method,
                        'data': parsed_data,
                        'created_at': record.created_at
                    })
                except Exception as e:
                    logger.error(f"Failed to decrypt payment data {record.id}: {e}")
                    continue
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"Failed to get payment data for user {tg_id}: {e}", exc_info=True)
            return []
    
    async def delete_payment_data(self, tg_id: int, data_id: int) -> bool:
        """
        Удаляет сохраненные данные платежного метода пользователя.
        
        Args:
            tg_id: Telegram ID пользователя
            data_id: ID записи с данными
            
        Returns:
            True если успешно удалено, False иначе
        """
        try:
            await self.session.execute(
                update(UserSavedPaymentData)
                .where(
                    UserSavedPaymentData.id == data_id,
                    UserSavedPaymentData.tg_id == tg_id
                )
                .values(is_active=False)
            )
            
            await self.session.commit()
            logger.info(f"Deleted payment data {data_id} for user {tg_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete payment data {data_id} for user {tg_id}: {e}", exc_info=True)
            await self.session.rollback()
            return False
