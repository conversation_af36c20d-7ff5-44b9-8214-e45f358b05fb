"""
Утилита для генерации уникальных идентификаторов.

Этот модуль предоставляет функции для генерации различных типов
уникальных идентификаторов для использования в системе.
"""

import uuid
import secrets
import string
import hashlib
from datetime import datetime
from typing import Optional


def generate_id(length: int = 32) -> str:
    """
    Генерирует уникальный идентификатор заданной длины.
    
    Args:
        length: Длина генерируемого ID (по умолчанию 32)
        
    Returns:
        Уникальный идентификатор
    """
    # Используем UUID4 как основу
    base_id = str(uuid.uuid4()).replace("-", "")
    
    if length <= len(base_id):
        return base_id[:length]
    
    # Если нужна большая длина, добавляем случайные символы
    additional_chars = ''.join(
        secrets.choice(string.ascii_lowercase + string.digits)
        for _ in range(length - len(base_id))
    )
    
    return base_id + additional_chars


def generate_short_id(length: int = 8) -> str:
    """
    Генерирует короткий уникальный идентификатор.
    
    Args:
        length: Длина ID (по умолчанию 8)
        
    Returns:
        Короткий уникальный идентификатор
    """
    alphabet = string.ascii_lowercase + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_payment_id(prefix: str = "pay") -> str:
    """
    Генерирует ID для платежа с префиксом.
    
    Args:
        prefix: Префикс для ID
        
    Returns:
        ID платежа
    """
    timestamp = int(datetime.utcnow().timestamp())
    random_part = generate_short_id(8)
    return f"{prefix}_{timestamp}_{random_part}"


def generate_subscription_id(user_id: int) -> str:
    """
    Генерирует ID для подписки на основе пользователя.
    
    Args:
        user_id: ID пользователя
        
    Returns:
        ID подписки
    """
    timestamp = int(datetime.utcnow().timestamp())
    user_hash = hashlib.md5(str(user_id).encode()).hexdigest()[:8]
    random_part = generate_short_id(6)
    return f"sub_{user_hash}_{timestamp}_{random_part}"


def generate_transaction_id(payment_type: str = "unknown") -> str:
    """
    Генерирует ID для транзакции.
    
    Args:
        payment_type: Тип платежа (yookassa, cryptomus, stars)
        
    Returns:
        ID транзакции
    """
    timestamp = int(datetime.utcnow().timestamp())
    random_part = generate_short_id(10)
    
    # Сокращаем тип платежа
    type_map = {
        "yookassa": "yoo",
        "cryptomus": "crypto",
        "telegram_stars": "stars",
        "unknown": "unk"
    }
    
    type_prefix = type_map.get(payment_type, "unk")
    return f"txn_{type_prefix}_{timestamp}_{random_part}"


def generate_node_id(country_code: str, node_number: int) -> str:
    """
    Генерирует ID для ноды Marzban.
    
    Args:
        country_code: Код страны (например, "ru", "us")
        node_number: Номер ноды в стране
        
    Returns:
        ID ноды
    """
    random_suffix = generate_short_id(6)
    return f"node_{country_code.lower()}_{node_number:02d}_{random_suffix}"


def generate_queue_id(queue_type: str = "general") -> str:
    """
    Генерирует ID для задачи в очереди.
    
    Args:
        queue_type: Тип очереди (auto_renewal, notification, etc.)
        
    Returns:
        ID задачи в очереди
    """
    timestamp = int(datetime.utcnow().timestamp())
    random_part = generate_short_id(8)
    
    # Сокращаем тип очереди
    type_map = {
        "auto_renewal": "ar",
        "notification": "notif",
        "payment": "pay",
        "general": "gen"
    }
    
    type_prefix = type_map.get(queue_type, "gen")
    return f"q_{type_prefix}_{timestamp}_{random_part}"


def generate_api_key(length: int = 64) -> str:
    """
    Генерирует API ключ.
    
    Args:
        length: Длина ключа
        
    Returns:
        API ключ
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_webhook_secret(length: int = 32) -> str:
    """
    Генерирует секрет для webhook.
    
    Args:
        length: Длина секрета
        
    Returns:
        Webhook секрет
    """
    return secrets.token_urlsafe(length)


def generate_session_id(user_id: int) -> str:
    """
    Генерирует ID сессии для пользователя.
    
    Args:
        user_id: ID пользователя
        
    Returns:
        ID сессии
    """
    timestamp = int(datetime.utcnow().timestamp())
    user_hash = hashlib.sha256(str(user_id).encode()).hexdigest()[:12]
    random_part = generate_short_id(8)
    return f"sess_{user_hash}_{timestamp}_{random_part}"


def generate_verification_code(length: int = 6) -> str:
    """
    Генерирует код верификации (только цифры).
    
    Args:
        length: Длина кода
        
    Returns:
        Код верификации
    """
    return ''.join(secrets.choice(string.digits) for _ in range(length))


def generate_referral_code(user_id: int, length: int = 8) -> str:
    """
    Генерирует реферальный код для пользователя.
    
    Args:
        user_id: ID пользователя
        length: Длина кода
        
    Returns:
        Реферальный код
    """
    # Используем хеш от user_id для стабильности
    user_hash = hashlib.md5(str(user_id).encode()).hexdigest()
    
    # Берем первые символы хеша и добавляем случайные
    base_length = min(4, length // 2)
    base = user_hash[:base_length].upper()
    
    remaining_length = length - len(base)
    if remaining_length > 0:
        alphabet = string.ascii_uppercase + string.digits
        additional = ''.join(secrets.choice(alphabet) for _ in range(remaining_length))
        return base + additional
    
    return base


def is_valid_id(id_string: str, expected_length: Optional[int] = None) -> bool:
    """
    Проверяет валидность ID.
    
    Args:
        id_string: Строка ID для проверки
        expected_length: Ожидаемая длина (опционально)
        
    Returns:
        True если ID валиден
    """
    if not id_string:
        return False
    
    # Проверяем длину
    if expected_length and len(id_string) != expected_length:
        return False
    
    # Проверяем, что содержит только допустимые символы
    allowed_chars = string.ascii_letters + string.digits + "_-"
    if not all(c in allowed_chars for c in id_string):
        return False
    
    return True


def extract_timestamp_from_id(id_string: str) -> Optional[datetime]:
    """
    Извлекает timestamp из ID, если он содержит его.
    
    Args:
        id_string: ID строка
        
    Returns:
        Datetime объект или None
    """
    try:
        # Ищем паттерн с timestamp
        parts = id_string.split("_")
        for part in parts:
            if part.isdigit() and len(part) == 10:  # Unix timestamp
                return datetime.fromtimestamp(int(part))
    except (ValueError, OSError):
        pass
    
    return None
