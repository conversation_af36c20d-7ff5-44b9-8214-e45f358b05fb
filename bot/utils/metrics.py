"""
Система метрик для мониторинга VPN бота через Prometheus.
Собирает метрики по пользователям, платежам, API вызовам и ошибкам.
"""

import time
import functools
from typing import Dict, Any, Optional, Callable
from datetime import datetime

try:
    from prometheus_client import (
        Counter, Histogram, Gauge, Info,
        start_http_server, CollectorRegistry,
        generate_latest, CONTENT_TYPE_LATEST,
        REGISTRY
    )
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

from utils.logging_config import get_logger


logger = get_logger(__name__)


class MetricsCollector:
    """
    Коллектор метрик для Prometheus.
    Если Prometheus недоступен, работает в режиме no-op.
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.enabled = PROMETHEUS_AVAILABLE
        self.registry = registry
        
        if not self.enabled:
            logger.warning("Prometheus client not available. Metrics collection disabled.")
            return
        
        # Метрики пользователей
        self.user_registrations = Counter(
            'vpn_bot_user_registrations_total',
            'Total number of user registrations',
            ['source'],
            registry=registry
        )
        
        self.active_users = Gauge(
            'vpn_bot_active_users',
            'Number of active users',
            ['period'],
            registry=registry
        )
        
        # Метрики подписок
        self.subscriptions_created = Counter(
            'vpn_bot_subscriptions_created_total',
            'Total number of subscriptions created',
            ['subscription_type', 'payment_method'],
            registry=registry
        )
        
        self.active_subscriptions = Gauge(
            'vpn_bot_active_subscriptions',
            'Number of active subscriptions',
            ['subscription_type'],
            registry=registry
        )
        
        self.subscription_renewals = Counter(
            'vpn_bot_subscription_renewals_total',
            'Total number of subscription renewals',
            ['subscription_type', 'renewal_type'],
            registry=registry
        )
        
        # Метрики платежей
        self.payment_attempts = Counter(
            'vpn_bot_payment_attempts_total',
            'Total payment attempts',
            ['provider', 'status', 'currency'],
            registry=registry
        )
        
        self.payment_amount = Counter(
            'vpn_bot_payment_amount_total',
            'Total payment amount',
            ['provider', 'currency'],
            registry=registry
        )
        
        self.payment_processing_time = Histogram(
            'vpn_bot_payment_processing_seconds',
            'Payment processing time',
            ['provider'],
            registry=registry
        )
        
        # Метрики API вызовов
        self.api_requests = Counter(
            'vpn_bot_api_requests_total',
            'Total API requests',
            ['endpoint', 'method', 'status'],
            registry=registry
        )
        
        self.api_request_duration = Histogram(
            'vpn_bot_api_request_duration_seconds',
            'API request duration',
            ['endpoint', 'method'],
            registry=registry
        )
        
        self.marzban_api_calls = Counter(
            'vpn_bot_marzban_api_calls_total',
            'Total Marzban API calls',
            ['operation', 'status'],
            registry=registry
        )
        
        self.marzban_api_duration = Histogram(
            'vpn_bot_marzban_api_duration_seconds',
            'Marzban API call duration',
            ['operation'],
            registry=registry
        )
        
        # Метрики ошибок
        self.errors_total = Counter(
            'vpn_bot_errors_total',
            'Total number of errors',
            ['error_type', 'component'],
            registry=registry
        )
        
        self.error_rate = Gauge(
            'vpn_bot_error_rate',
            'Error rate per minute',
            ['component'],
            registry=registry
        )
        
        # Метрики системы
        self.bot_uptime = Gauge(
            'vpn_bot_uptime_seconds',
            'Bot uptime in seconds',
            registry=registry
        )
        
        self.message_processing_time = Histogram(
            'vpn_bot_message_processing_seconds',
            'Message processing time',
            ['handler_type'],
            registry=registry
        )
        
        self.database_connections = Gauge(
            'vpn_bot_database_connections',
            'Number of database connections',
            ['state'],
            registry=registry
        )
        
        # Информационные метрики
        self.bot_info = Info(
            'vpn_bot_info',
            'Bot information',
            registry=registry
        )
        
        # Устанавливаем информацию о боте
        self.bot_info.info({
            'version': '1.0.0',
            'python_version': self._get_python_version(),
            'start_time': datetime.utcnow().isoformat()
        })
        
        # Время запуска для uptime
        self._start_time = time.time()
        
        logger.info("Metrics collector initialized successfully")
    
    def _get_python_version(self) -> str:
        """Получает версию Python"""
        import sys
        return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    def record_user_registration(self, source: str = "telegram") -> None:
        """Записывает регистрацию пользователя"""
        if not self.enabled:
            return
        self.user_registrations.labels(source=source).inc()
    
    def update_active_users(self, count: int, period: str = "daily") -> None:
        """Обновляет количество активных пользователей"""
        if not self.enabled:
            return
        self.active_users.labels(period=period).set(count)
    
    def record_subscription_created(
        self, 
        subscription_type: str, 
        payment_method: str
    ) -> None:
        """Записывает создание подписки"""
        if not self.enabled:
            return
        self.subscriptions_created.labels(
            subscription_type=subscription_type,
            payment_method=payment_method
        ).inc()
    
    def update_active_subscriptions(self, count: int, subscription_type: str) -> None:
        """Обновляет количество активных подписок"""
        if not self.enabled:
            return
        self.active_subscriptions.labels(subscription_type=subscription_type).set(count)
    
    def record_subscription_renewal(
        self, 
        subscription_type: str, 
        renewal_type: str = "manual"
    ) -> None:
        """Записывает продление подписки"""
        if not self.enabled:
            return
        self.subscription_renewals.labels(
            subscription_type=subscription_type,
            renewal_type=renewal_type
        ).inc()
    
    def record_payment_attempt(
        self, 
        provider: str, 
        status: str, 
        currency: str,
        amount: Optional[float] = None
    ) -> None:
        """Записывает попытку платежа"""
        if not self.enabled:
            return
        
        self.payment_attempts.labels(
            provider=provider,
            status=status,
            currency=currency
        ).inc()
        
        if amount is not None and status == "succeeded":
            self.payment_amount.labels(
                provider=provider,
                currency=currency
            ).inc(amount)
    
    def record_api_request(
        self, 
        endpoint: str, 
        method: str, 
        status: str,
        duration: float
    ) -> None:
        """Записывает API запрос"""
        if not self.enabled:
            return
        
        self.api_requests.labels(
            endpoint=endpoint,
            method=method,
            status=status
        ).inc()
        
        self.api_request_duration.labels(
            endpoint=endpoint,
            method=method
        ).observe(duration)
    
    def record_marzban_api_call(
        self, 
        operation: str, 
        status: str,
        duration: float
    ) -> None:
        """Записывает вызов Marzban API"""
        if not self.enabled:
            return
        
        self.marzban_api_calls.labels(
            operation=operation,
            status=status
        ).inc()
        
        self.marzban_api_duration.labels(operation=operation).observe(duration)
    
    def record_error(self, error_type: str, component: str) -> None:
        """Записывает ошибку"""
        if not self.enabled:
            return
        self.errors_total.labels(error_type=error_type, component=component).inc()
    
    def update_error_rate(self, rate: float, component: str) -> None:
        """Обновляет частоту ошибок"""
        if not self.enabled:
            return
        self.error_rate.labels(component=component).set(rate)
    
    def update_uptime(self) -> None:
        """Обновляет время работы бота"""
        if not self.enabled:
            return
        uptime = time.time() - self._start_time
        self.bot_uptime.set(uptime)
    
    def record_message_processing(self, handler_type: str, duration: float) -> None:
        """Записывает время обработки сообщения"""
        if not self.enabled:
            return
        self.message_processing_time.labels(handler_type=handler_type).observe(duration)
    
    def update_database_connections(self, active: int, idle: int) -> None:
        """Обновляет количество соединений с БД"""
        if not self.enabled:
            return
        self.database_connections.labels(state="active").set(active)
        self.database_connections.labels(state="idle").set(idle)
    
    def get_metrics(self) -> str:
        """Возвращает метрики в формате Prometheus"""
        if not self.enabled:
            return "# Prometheus metrics not available\n"

        try:
            # Обновляем uptime перед возвратом метрик
            self.update_uptime()

            # Если registry не задан, используем глобальный registry
            if self.registry is None:
                if PROMETHEUS_AVAILABLE:
                    metrics_bytes = generate_latest(REGISTRY)
                    return metrics_bytes.decode('utf-8')
                else:
                    return "# Prometheus not available\n"
            else:
                if PROMETHEUS_AVAILABLE:
                    metrics_bytes = generate_latest(self.registry)
                    return metrics_bytes.decode('utf-8')
                else:
                    return "# Prometheus not available\n"
        except Exception as e:
            logger.error(f"Failed to generate metrics: {e}", exc_info=True)
            return f"# Error generating metrics: {str(e)}\n"


def measure_time(metric_name: str, labels: Optional[Dict[str, str]] = None):
    """
    Декоратор для измерения времени выполнения функций.
    
    Args:
        metric_name: Имя метрики
        labels: Дополнительные лейблы
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Записываем успешное выполнение
                if hasattr(metrics, 'record_api_request'):
                    endpoint = labels.get('endpoint', func.__name__) if labels else func.__name__
                    metrics.record_api_request(
                        endpoint=endpoint,
                        method=labels.get('method', 'async') if labels else 'async',
                        status='success',
                        duration=duration
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # Записываем ошибку
                if hasattr(metrics, 'record_api_request'):
                    endpoint = labels.get('endpoint', func.__name__) if labels else func.__name__
                    metrics.record_api_request(
                        endpoint=endpoint,
                        method=labels.get('method', 'async') if labels else 'async',
                        status='error',
                        duration=duration
                    )
                
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Записываем успешное выполнение
                if hasattr(metrics, 'record_api_request'):
                    endpoint = labels.get('endpoint', func.__name__) if labels else func.__name__
                    metrics.record_api_request(
                        endpoint=endpoint,
                        method=labels.get('method', 'sync') if labels else 'sync',
                        status='success',
                        duration=duration
                    )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # Записываем ошибку
                if hasattr(metrics, 'record_api_request'):
                    endpoint = labels.get('endpoint', func.__name__) if labels else func.__name__
                    metrics.record_api_request(
                        endpoint=endpoint,
                        method=labels.get('method', 'sync') if labels else 'sync',
                        status='error',
                        duration=duration
                    )
                
                raise
        
        # Возвращаем соответствующий wrapper в зависимости от типа функции
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Глобальный экземпляр коллектора метрик
metrics = MetricsCollector()


def start_metrics_server(port: int = 8000) -> None:
    """
    Запускает HTTP сервер для метрик Prometheus.
    
    Args:
        port: Порт для сервера метрик
    """
    if not PROMETHEUS_AVAILABLE:
        logger.warning("Cannot start metrics server: prometheus_client not available")
        return
    
    try:
        start_http_server(port)
        logger.info(f"Metrics server started on port {port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")


def get_metrics_content() -> tuple[str, str]:
    """
    Возвращает содержимое метрик и content type для HTTP ответа.

    Returns:
        Tuple с содержимым метрик и content type
    """
    try:
        content = metrics.get_metrics()
        if PROMETHEUS_AVAILABLE:
            # Используем только тип контента без charset для aiohttp
            content_type = "text/plain"
        else:
            content_type = "text/plain"
        return content, content_type
    except Exception as e:
        logger.error(f"Failed to get metrics content: {e}", exc_info=True)
        error_msg = f"# Error generating metrics: {str(e)}\n"
        return error_msg, "text/plain"
