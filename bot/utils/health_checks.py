"""
Система health checks для мониторинга состояния VPN бота.
Проверяет доступность БД, Marzban API и других критических компонентов.
"""

import asyncio
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime

from sqlalchemy import text

# Используем существующий движок из db.methods
from db.methods import engine
from utils.marzban_api import MarzbanAPI
from utils.logging_config import get_logger


logger = get_logger(__name__)


class HealthStatus(Enum):
    """Статусы здоровья компонентов"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


@dataclass
class ComponentHealth:
    """Информация о здоровье компонента"""
    name: str
    status: HealthStatus
    message: str
    response_time_ms: int
    details: Optional[Dict[str, Any]] = None
    last_check: Optional[datetime] = None


class HealthChecker:
    """
    Основной класс для проверки здоровья системы.
    """
    
    def __init__(self):
        # Инициализируем MarzbanAPI с параметрами из конфигурации
        import glv
        self.marzban_api = MarzbanAPI(
            host=glv.config['PANEL_HOST'],
            username=glv.config['PANEL_USER'],
            password=glv.config['PANEL_PASS']
        )
        self._cache = {}
        self._cache_ttl = 30  # Кэш на 30 секунд
    
    async def check_all(self) -> Dict[str, ComponentHealth]:
        """
        Проверяет состояние всех компонентов системы.
        
        Returns:
            Словарь с результатами проверок
        """
        checks = {
            'database': self.check_database(),
            'marzban_api': self.check_marzban_api(),
            'bot_core': self.check_bot_core(),
            'memory': self.check_memory_usage(),
        }
        
        results = {}
        for name, check_coro in checks.items():
            try:
                results[name] = await check_coro
            except Exception as e:
                logger.error(f"Health check failed for {name}: {e}", exc_info=True)
                results[name] = ComponentHealth(
                    name=name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Check failed: {str(e)}",
                    response_time_ms=0,
                    last_check=datetime.now()
                )
        
        return results
    
    async def check_database(self) -> ComponentHealth:
        """Проверяет состояние базы данных"""
        start_time = time.time()

        try:
            # Используем существующий подход с engine.connect()
            async with engine.connect() as conn:
                # Простой запрос для проверки соединения
                result = await conn.execute(text("SELECT 1"))
                result.scalar()

                # Проверяем количество активных соединений
                try:
                    connections_result = await conn.execute(
                        text("SHOW STATUS LIKE 'Threads_connected'")
                    )
                    connections_row = connections_result.fetchone()
                    active_connections = int(connections_row[1]) if connections_row else 0
                except Exception:
                    # Если не удается получить статус соединений, используем 0
                    active_connections = 0

                response_time = int((time.time() - start_time) * 1000)

                # Определяем статус на основе времени ответа и количества соединений
                if response_time > 1000:  # Больше 1 секунды
                    status = HealthStatus.DEGRADED
                    message = f"Database slow response: {response_time}ms"
                elif active_connections > 100:  # Много активных соединений
                    status = HealthStatus.DEGRADED
                    message = f"High connection count: {active_connections}"
                else:
                    status = HealthStatus.HEALTHY
                    message = "Database connection successful"

                return ComponentHealth(
                    name="database",
                    status=status,
                    message=message,
                    response_time_ms=response_time,
                    details={
                        "active_connections": active_connections,
                        "response_time_ms": response_time
                    },
                    last_check=datetime.now()
                )

        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}",
                response_time_ms=response_time,
                details={"error": str(e)},
                last_check=datetime.now()
            )
    
    async def check_marzban_api(self) -> ComponentHealth:
        """Проверяет состояние Marzban API"""
        start_time = time.time()
        
        try:
            # Проверяем доступность API через получение токена
            await self.marzban_api.get_token()
            
            response_time = int((time.time() - start_time) * 1000)
            
            # Определяем статус на основе времени ответа
            if response_time > 5000:  # Больше 5 секунд
                status = HealthStatus.DEGRADED
                message = f"Marzban API slow response: {response_time}ms"
            else:
                status = HealthStatus.HEALTHY
                message = "Marzban API accessible"
            
            return ComponentHealth(
                name="marzban_api",
                status=status,
                message=message,
                response_time_ms=response_time,
                details={
                    "response_time_ms": response_time,
                    "api_host": self.marzban_api.host
                },
                last_check=datetime.now()
            )

        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            return ComponentHealth(
                name="marzban_api",
                status=HealthStatus.UNHEALTHY,
                message=f"Marzban API failed: {str(e)}",
                response_time_ms=response_time,
                details={
                    "error": str(e),
                    "api_host": getattr(self.marzban_api, 'host', 'unknown')
                },
                last_check=datetime.now()
            )
    
    async def check_bot_core(self) -> ComponentHealth:
        """Проверяет основные компоненты бота"""
        start_time = time.time()
        
        try:
            # Проверяем импорт основных модулей
            import glv

            # Проверяем, что bot и dispatcher инициализированы
            bot_initialized = hasattr(glv, 'bot') and glv.bot is not None
            dp_initialized = hasattr(glv, 'dp') and glv.dp is not None

            if not bot_initialized:
                raise Exception("Bot not initialized")

            if not dp_initialized:
                raise Exception("Dispatcher not initialized")

            response_time = int((time.time() - start_time) * 1000)

            return ComponentHealth(
                name="bot_core",
                status=HealthStatus.HEALTHY,
                message="Bot core components healthy",
                response_time_ms=response_time,
                details={
                    "bot_initialized": bot_initialized,
                    "dispatcher_initialized": dp_initialized,
                    "handlers_loaded": True
                },
                last_check=datetime.now()
            )

        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            return ComponentHealth(
                name="bot_core",
                status=HealthStatus.UNHEALTHY,
                message=f"Bot core check failed: {str(e)}",
                response_time_ms=response_time,
                details={"error": str(e)},
                last_check=datetime.now()
            )
    
    async def check_memory_usage(self) -> ComponentHealth:
        """Проверяет использование памяти"""
        start_time = time.time()
        
        try:
            import psutil
            
            # Получаем информацию о памяти
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_mb = memory.available // (1024 * 1024)
            
            response_time = int((time.time() - start_time) * 1000)
            
            # Определяем статус на основе использования памяти
            if memory_percent > 90:
                status = HealthStatus.UNHEALTHY
                message = f"Critical memory usage: {memory_percent}%"
            elif memory_percent > 80:
                status = HealthStatus.DEGRADED
                message = f"High memory usage: {memory_percent}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage normal: {memory_percent}%"
            
            return ComponentHealth(
                name="memory",
                status=status,
                message=message,
                response_time_ms=response_time,
                details={
                    "memory_percent": memory_percent,
                    "memory_available_mb": memory_available_mb,
                    "memory_total_mb": memory.total // (1024 * 1024)
                },
                last_check=datetime.now()
            )

        except ImportError:
            # psutil не установлен
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNKNOWN,
                message="Memory monitoring not available (psutil not installed)",
                response_time_ms=0,
                details={"error": "psutil not available"},
                last_check=datetime.now()
            )
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNHEALTHY,
                message=f"Memory check failed: {str(e)}",
                response_time_ms=response_time,
                details={"error": str(e)},
                last_check=datetime.now()
            )
    
    async def get_overall_status(self) -> HealthStatus:
        """
        Определяет общий статус системы на основе всех компонентов.
        
        Returns:
            Общий статус здоровья системы
        """
        results = await self.check_all()
        
        # Если есть критические компоненты в состоянии UNHEALTHY
        critical_components = ['database', 'marzban_api', 'bot_core']
        for component_name in critical_components:
            if component_name in results:
                if results[component_name].status == HealthStatus.UNHEALTHY:
                    return HealthStatus.UNHEALTHY
        
        # Если есть компоненты в состоянии DEGRADED
        for component in results.values():
            if component.status == HealthStatus.DEGRADED:
                return HealthStatus.DEGRADED
        
        # Если все компоненты здоровы
        return HealthStatus.HEALTHY
    
    async def is_ready(self) -> bool:
        """
        Проверяет готовность системы к обслуживанию запросов.
        
        Returns:
            True если система готова, False иначе
        """
        # Проверяем только критические компоненты
        db_health = await self.check_database()
        api_health = await self.check_marzban_api()
        bot_health = await self.check_bot_core()
        
        return (
            db_health.status == HealthStatus.HEALTHY and
            api_health.status == HealthStatus.HEALTHY and
            bot_health.status == HealthStatus.HEALTHY
        )
    
    async def is_alive(self) -> bool:
        """
        Простая проверка жизнеспособности приложения.
        
        Returns:
            True если приложение живо
        """
        try:
            # Простая проверка - можем ли мы выполнить базовые операции
            import glv
            return glv.bot is not None and glv.dp is not None
        except Exception:
            return False


# Глобальный экземпляр health checker
health_checker = HealthChecker()
