"""
Модуль для шифрования и дешифрования чувствительных данных.
Использует Fernet (симметричное шифрование) для защиты паролей, токенов и других секретных данных.
"""

import os
import base64
from typing import Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from utils.logging_config import get_logger

logger = get_logger(__name__)


class DataEncryption:
    """
    Класс для шифрования и дешифрования чувствительных данных.

    Использует Fernet (AES 128 в режиме CBC с HMAC SHA256 для аутентификации).
    Ключ шифрования генерируется из мастер-пароля с использованием PBKDF2.
    """

    # Специальный маркер для пустых строк
    EMPTY_STRING_MARKER = "__EMPTY_STRING__"
    
    def __init__(self, master_key: Optional[str] = None):
        """
        Инициализация системы шифрования.
        
        Args:
            master_key: Мастер-ключ для генерации ключа шифрования.
                       Если не указан, берется из переменной окружения ENCRYPTION_KEY.
        """
        self._master_key = master_key or self._get_master_key()
        self._salt = self._get_or_create_salt()
        self._fernet = self._create_fernet()
        
        logger.info("Encryption system initialized successfully")
    
    def _get_master_key(self) -> str:
        """
        Получает мастер-ключ из переменных окружения.
        
        Returns:
            Мастер-ключ для шифрования
            
        Raises:
            ValueError: Если мастер-ключ не найден
        """
        master_key = os.getenv('ENCRYPTION_KEY')
        if not master_key:
            # Для разработки генерируем временный ключ
            if os.getenv('ENVIRONMENT', 'development') == 'development':
                master_key = 'dev-encryption-key-change-in-production'
                logger.warning(
                    "Using default encryption key for development. "
                    "Set ENCRYPTION_KEY environment variable for production!"
                )
            else:
                raise ValueError(
                    "ENCRYPTION_KEY environment variable is required for production. "
                    "Generate a secure key and set it as environment variable."
                )
        
        return master_key
    
    def _get_or_create_salt(self) -> bytes:
        """
        Получает или создает соль для PBKDF2.
        
        Returns:
            Соль для генерации ключа
        """
        salt_env = os.getenv('ENCRYPTION_SALT')
        if salt_env:
            try:
                return base64.b64decode(salt_env.encode())
            except Exception as e:
                logger.warning(f"Invalid ENCRYPTION_SALT format: {e}")
        
        # Генерируем новую соль для разработки
        if os.getenv('ENVIRONMENT', 'development') == 'development':
            # Используем фиксированную соль для разработки для консистентности
            salt = b'dev-salt-16-bytes'
            logger.warning(
                "Using default salt for development. "
                "Set ENCRYPTION_SALT environment variable for production!"
            )
        else:
            # В продакшене должна быть установлена соль
            raise ValueError(
                "ENCRYPTION_SALT environment variable is required for production. "
                "Generate: python -c 'import os, base64; print(base64.b64encode(os.urandom(16)).decode())'"
            )
        
        return salt
    
    def _create_fernet(self) -> Fernet:
        """
        Создает объект Fernet для шифрования.
        
        Returns:
            Настроенный объект Fernet
        """
        # Генерируем ключ из мастер-пароля с помощью PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 32 байта для Fernet
            salt=self._salt,
            iterations=100000,  # Рекомендуемое количество итераций
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(self._master_key.encode()))
        return Fernet(key)
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """
        Шифрует данные и возвращает base64-строку.

        Args:
            data: Данные для шифрования (строка или байты)

        Returns:
            Зашифрованные данные в формате base64

        Raises:
            ValueError: Если данные None
            Exception: При ошибке шифрования
        """
        if data is None:
            raise ValueError("Data to encrypt cannot be None")
        
        try:
            # Обрабатываем пустую строку
            if isinstance(data, str) and data == "":
                data_to_encrypt = self.EMPTY_STRING_MARKER
            else:
                data_to_encrypt = data

            # Конвертируем в байты если нужно
            if isinstance(data_to_encrypt, str):
                data_bytes = data_to_encrypt.encode('utf-8')
            else:
                data_bytes = data_to_encrypt

            # Шифруем данные
            encrypted_data = self._fernet.encrypt(data_bytes)

            # Возвращаем в формате base64 для хранения в БД
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}", exc_info=True)
            raise Exception(f"Failed to encrypt data: {str(e)}")
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        Дешифрует base64-строку и возвращает исходные данные.

        Args:
            encrypted_data: Зашифрованные данные в формате base64

        Returns:
            Расшифрованные данные в виде строки

        Raises:
            ValueError: Если зашифрованные данные None или некорректные
            Exception: При ошибке дешифрования
        """
        if encrypted_data is None:
            raise ValueError("Encrypted data cannot be None")
        if encrypted_data == "":
            return ""  # Возвращаем пустую строку для пустых данных
        
        try:
            # Декодируем из base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))

            # Дешифруем данные
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)

            # Конвертируем в строку
            decrypted_str = decrypted_bytes.decode('utf-8')

            # Проверяем, была ли это пустая строка
            if decrypted_str == self.EMPTY_STRING_MARKER:
                return ""

            return decrypted_str
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}", exc_info=True)
            raise Exception(f"Failed to decrypt data: {str(e)}")
    
    def is_encrypted(self, data: str) -> bool:
        """
        Проверяет, являются ли данные зашифрованными.
        
        Args:
            data: Данные для проверки
            
        Returns:
            True если данные зашифрованы, False иначе
        """
        if not data:
            return False
        
        try:
            # Пытаемся дешифровать данные
            self.decrypt(data)
            return True
        except:
            return False
    
    def encrypt_if_needed(self, data: str) -> str:
        """
        Шифрует данные только если они еще не зашифрованы.
        
        Args:
            data: Данные для шифрования
            
        Returns:
            Зашифрованные данные или исходные данные если уже зашифрованы
        """
        if not data:
            return data
        
        if self.is_encrypted(data):
            return data
        
        return self.encrypt(data)
    
    def decrypt_if_needed(self, data: str) -> str:
        """
        Дешифрует данные только если они зашифрованы.
        
        Args:
            data: Данные для дешифрования
            
        Returns:
            Расшифрованные данные или исходные данные если не зашифрованы
        """
        if not data:
            return data
        
        if self.is_encrypted(data):
            return self.decrypt(data)
        
        return data


# Глобальный экземпляр для использования в приложении
encryption = DataEncryption()


def generate_encryption_key() -> str:
    """
    Генерирует новый ключ шифрования для использования в продакшене.
    
    Returns:
        Сгенерированный ключ в формате base64
    """
    key = Fernet.generate_key()
    return base64.b64encode(key).decode()


def generate_salt() -> str:
    """
    Генерирует новую соль для PBKDF2.
    
    Returns:
        Сгенерированная соль в формате base64
    """
    salt = os.urandom(16)
    return base64.b64encode(salt).decode()


if __name__ == "__main__":
    # Утилита для генерации ключей
    print("Generated encryption key:", generate_encryption_key())
    print("Generated salt:", generate_salt())
