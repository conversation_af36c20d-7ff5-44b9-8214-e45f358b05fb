from sqlalchemy import Column, BigInteger, String, Boolean, Text, DateTime, Index
from sqlalchemy.sql import func

from db.declarative_base import Base

# Импортируем новые модели для стран и нод
from .models_countries_nodes import (
    Country, MarzbanNode, MarzbanInbound,
    UserNodePreference, NodeStatistics,
    NodeStatus, InboundProtocol, SecurityType, NetworkType
)

class VPNUsers(Base):
    __tablename__ = "vpnusers"

    id = Column(BigInteger, primary_key=True, unique=True, autoincrement=True)
    tg_id = Column(BigInteger, nullable=False)
    vpn_id = Column(String(64), default="", nullable=False)
    test = Column(Boolean, default=False, nullable=False)
    # Timestamps будут добавлены через миграции
    # created_at = Column(DateTime(timezone=True), server_default=func.now())
    # updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Индексы будут добавлены через миграции
    # __table_args__ = (
    #     Index('idx_vpnusers_tg_id', 'tg_id'),
    #     Index('idx_vpnusers_vpn_id', 'vpn_id'),
    #     Index('idx_vpnusers_tg_id_test', 'tg_id', 'test'),
    #     Index('idx_vpnusers_created_at', 'created_at'),
    # )

class CPayments(Base):
    __tablename__ = "crypto_payments"

    id = Column(BigInteger, primary_key=True, unique=True, autoincrement=True)
    tg_id = Column(BigInteger, nullable=False)
    lang = Column(String(64))
    payment_uuid = Column(String(64))
    order_id = Column(String(64), nullable=False)
    chat_id = Column(BigInteger)
    callback = Column(String(64))
    # Timestamps и индексы будут добавлены через миграции
    # created_at = Column(DateTime(timezone=True), server_default=func.now())
    # updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # __table_args__ = (
    #     Index('idx_crypto_payments_order_id', 'order_id'),
    #     Index('idx_crypto_payments_tg_id', 'tg_id'),
    #     Index('idx_crypto_payments_tg_id_callback', 'tg_id', 'callback'),
    #     Index('idx_crypto_payments_created_at', 'created_at'),
    # )

class YPayments(Base):
    __tablename__ = "yookassa_payments"

    id = Column(BigInteger, primary_key=True, unique=True, autoincrement=True)
    tg_id = Column(BigInteger, nullable=False)
    lang = Column(String(64))
    payment_id = Column(String(64), nullable=False)
    chat_id = Column(BigInteger)
    callback = Column(String(64))
    # Timestamps и индексы будут добавлены через миграции
    # created_at = Column(DateTime(timezone=True), server_default=func.now())
    # updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # __table_args__ = (
    #     Index('idx_ypayments_payment_id', 'payment_id'),
    #     Index('idx_ypayments_tg_id', 'tg_id'),
    #     Index('idx_ypayments_tg_id_callback', 'tg_id', 'callback'),
    #     Index('idx_ypayments_created_at', 'created_at'),
    # )


class EncryptedConfig(Base):
    """
    Модель для хранения зашифрованных конфигурационных данных.
    Используется для безопасного хранения API ключей, паролей и других чувствительных данных.
    """
    __tablename__ = "encrypted_config"

    id = Column(BigInteger, primary_key=True, unique=True, autoincrement=True)
    key_name = Column(String(128), unique=True, nullable=False, index=True)
    encrypted_value = Column(Text, nullable=False)
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


class UserSavedPaymentData(Base):
    """
    Модель для хранения зашифрованных данных сохраненных платежных методов пользователей.
    """
    __tablename__ = "user_saved_payment_data"

    id = Column(BigInteger, primary_key=True, unique=True, autoincrement=True)
    tg_id = Column(BigInteger, nullable=False, index=True)
    payment_method = Column(String(64), nullable=False)  # 'yookassa', 'cryptomus', etc.
    encrypted_data = Column(Text, nullable=False)  # Зашифрованные данные карты/кошелька
    data_hash = Column(String(128), nullable=False)  # Хеш для идентификации данных
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())