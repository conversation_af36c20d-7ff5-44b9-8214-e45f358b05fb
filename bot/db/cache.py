"""
Система кэширования для оптимизации производительности БД.
"""

import asyncio
import json
import hashlib
from typing import Any, Optional, Dict, Callable, TypeVar, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from functools import wraps

from utils.logging_config import get_logger

logger = get_logger(__name__)

T = TypeVar('T')


@dataclass
class CacheEntry:
    """Запись в кэше."""
    value: Any
    created_at: datetime
    expires_at: Optional[datetime] = None
    hit_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.now)


class MemoryCache:
    """Простой in-memory кэш с TTL и LRU выселением."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        Инициализирует кэш.
        
        Args:
            max_size: Максимальный размер кэша
            default_ttl: TTL по умолчанию в секундах
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = asyncio.Lock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
    
    def _generate_key(self, key: Union[str, tuple, dict]) -> str:
        """Генерирует ключ кэша."""
        if isinstance(key, str):
            return key
        elif isinstance(key, (tuple, list)):
            return hashlib.md5(str(key).encode()).hexdigest()
        elif isinstance(key, dict):
            sorted_items = sorted(key.items())
            return hashlib.md5(str(sorted_items).encode()).hexdigest()
        else:
            return hashlib.md5(str(key).encode()).hexdigest()
    
    async def get(self, key: Union[str, tuple, dict]) -> Optional[Any]:
        """Получает значение из кэша."""
        cache_key = self._generate_key(key)
        
        async with self._lock:
            entry = self._cache.get(cache_key)
            
            if entry is None:
                self._stats['misses'] += 1
                return None
            
            # Проверяем истечение срока
            if entry.expires_at and datetime.now() > entry.expires_at:
                del self._cache[cache_key]
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # Обновляем статистику доступа
            entry.hit_count += 1
            entry.last_accessed = datetime.now()
            self._stats['hits'] += 1
            
            return entry.value
    
    async def set(self, key: Union[str, tuple, dict], value: Any, ttl: Optional[int] = None) -> None:
        """Сохраняет значение в кэш."""
        cache_key = self._generate_key(key)
        ttl = ttl or self.default_ttl
        
        expires_at = datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
        
        async with self._lock:
            # Проверяем размер кэша и выселяем старые записи
            if len(self._cache) >= self.max_size and cache_key not in self._cache:
                await self._evict_lru()
            
            self._cache[cache_key] = CacheEntry(
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at
            )
    
    async def delete(self, key: Union[str, tuple, dict]) -> bool:
        """Удаляет значение из кэша."""
        cache_key = self._generate_key(key)
        
        async with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
                return True
            return False
    
    async def clear(self) -> None:
        """Очищает весь кэш."""
        async with self._lock:
            self._cache.clear()
            self._stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'expired': 0
            }
    
    async def _evict_lru(self) -> None:
        """Выселяет наименее используемую запись."""
        if not self._cache:
            return
        
        # Находим запись с наименьшим last_accessed
        lru_key = min(
            self._cache.keys(),
            key=lambda k: self._cache[k].last_accessed
        )
        
        del self._cache[lru_key]
        self._stats['evictions'] += 1
    
    async def cleanup_expired(self) -> int:
        """Очищает истекшие записи."""
        now = datetime.now()
        expired_keys = []
        
        async with self._lock:
            for key, entry in self._cache.items():
                if entry.expires_at and now > entry.expires_at:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self._stats['expired'] += 1
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику кэша."""
        total_requests = self._stats['hits'] + self._stats['misses']
        hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'hit_rate': round(hit_rate, 2),
            'stats': self._stats.copy(),
            'memory_usage_estimate': len(self._cache) * 100  # Примерная оценка в байтах
        }


class DatabaseCache:
    """Кэш для результатов запросов к БД."""
    
    def __init__(self):
        self.user_cache = MemoryCache(max_size=500, default_ttl=300)  # 5 минут
        self.payment_cache = MemoryCache(max_size=200, default_ttl=600)  # 10 минут
        self.stats_cache = MemoryCache(max_size=50, default_ttl=60)  # 1 минута

        # Задача очистки будет запущена при первом использовании
        self._cleanup_task = None
        self._cleanup_started = False
    
    def _start_cleanup_task(self):
        """Запускает задачу периодической очистки."""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(60)  # Очистка каждую минуту
                    
                    user_expired = await self.user_cache.cleanup_expired()
                    payment_expired = await self.payment_cache.cleanup_expired()
                    stats_expired = await self.stats_cache.cleanup_expired()
                    
                    total_expired = user_expired + payment_expired + stats_expired
                    
                    if total_expired > 0:
                        logger.debug(f"Cleaned up {total_expired} expired cache entries")
                        
                except Exception as e:
                    logger.error(f"Error in cache cleanup: {e}")
        
        try:
            self._cleanup_task = asyncio.create_task(cleanup_loop())
        except RuntimeError:
            # Нет активного event loop, задача будет запущена позже
            pass
    
    def _ensure_cleanup_started(self):
        """Обеспечивает запуск задачи очистки."""
        if not self._cleanup_started and self._cleanup_task is None:
            try:
                self._start_cleanup_task()
                self._cleanup_started = True
            except RuntimeError:
                # Event loop еще не запущен
                pass

    async def get_user(self, tg_id: int) -> Optional[Any]:
        """Получает пользователя из кэша."""
        self._ensure_cleanup_started()
        return await self.user_cache.get(f"user:{tg_id}")
    
    async def set_user(self, tg_id: int, user_data: Any, ttl: Optional[int] = None) -> None:
        """Сохраняет пользователя в кэш."""
        await self.user_cache.set(f"user:{tg_id}", user_data, ttl)
    
    async def invalidate_user(self, tg_id: int) -> None:
        """Инвалидирует кэш пользователя."""
        await self.user_cache.delete(f"user:{tg_id}")
    
    async def get_user_by_vpn_id(self, vpn_id: str) -> Optional[Any]:
        """Получает пользователя по VPN ID из кэша."""
        return await self.user_cache.get(f"user_vpn:{vpn_id}")
    
    async def set_user_by_vpn_id(self, vpn_id: str, user_data: Any, ttl: Optional[int] = None) -> None:
        """Сохраняет пользователя по VPN ID в кэш."""
        await self.user_cache.set(f"user_vpn:{vpn_id}", user_data, ttl)
    
    async def get_payment(self, payment_id: str, payment_type: str = "yookassa") -> Optional[Any]:
        """Получает платеж из кэша."""
        return await self.payment_cache.get(f"payment:{payment_type}:{payment_id}")
    
    async def set_payment(self, payment_id: str, payment_data: Any, payment_type: str = "yookassa", ttl: Optional[int] = None) -> None:
        """Сохраняет платеж в кэш."""
        await self.payment_cache.set(f"payment:{payment_type}:{payment_id}", payment_data, ttl)
    
    async def get_user_stats(self) -> Optional[Any]:
        """Получает статистику пользователей из кэша."""
        return await self.stats_cache.get("user_stats")
    
    async def set_user_stats(self, stats_data: Any, ttl: Optional[int] = None) -> None:
        """Сохраняет статистику пользователей в кэш."""
        await self.stats_cache.set("user_stats", stats_data, ttl)
    
    async def get_payment_stats(self) -> Optional[Any]:
        """Получает статистику платежей из кэша."""
        return await self.stats_cache.get("payment_stats")
    
    async def set_payment_stats(self, stats_data: Any, ttl: Optional[int] = None) -> None:
        """Сохраняет статистику платежей в кэш."""
        await self.stats_cache.set("payment_stats", stats_data, ttl)
    
    async def clear_all(self) -> None:
        """Очищает все кэши."""
        await self.user_cache.clear()
        await self.payment_cache.clear()
        await self.stats_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Возвращает статистику всех кэшей."""
        return {
            'user_cache': self.user_cache.get_stats(),
            'payment_cache': self.payment_cache.get_stats(),
            'stats_cache': self.stats_cache.get_stats()
        }
    
    async def stop(self) -> None:
        """Останавливает задачи кэша."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass


def cached(cache_key_func: Callable = None, ttl: int = 300):
    """
    Декоратор для кэширования результатов функций.
    
    Args:
        cache_key_func: Функция для генерации ключа кэша
        ttl: Время жизни кэша в секундах
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Генерируем ключ кэша
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # Пытаемся получить из кэша
            cached_result = await db_cache.user_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Выполняем функцию и кэшируем результат
            result = await func(*args, **kwargs)
            await db_cache.user_cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


# Глобальный экземпляр кэша БД
db_cache = DatabaseCache()
