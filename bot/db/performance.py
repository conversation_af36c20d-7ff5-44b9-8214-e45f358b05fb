"""
Модуль для анализа и мониторинга производительности БД.
"""

import time
import asyncio
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from sqlalchemy import text, inspect
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.engine import Engine
from sqlalchemy.pool import Pool

from .base import get_session, engine
from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class QueryPerformanceMetrics:
    """Метрики производительности запроса."""
    query: str
    execution_time: float
    timestamp: datetime
    session_id: Optional[str] = None
    error: Optional[str] = None


@dataclass
class ConnectionPoolMetrics:
    """Метрики пула соединений."""
    pool_size: int
    checked_in: int
    checked_out: int
    overflow: int
    invalid: int
    timestamp: datetime = field(default_factory=datetime.now)


class DatabasePerformanceAnalyzer:
    """Анализатор производительности БД."""
    
    def __init__(self):
        self.query_metrics: List[QueryPerformanceMetrics] = []
        self.slow_query_threshold = 1.0  # секунды
        self.max_metrics_history = 1000
    
    @asynccontextmanager
    async def measure_query(self, query_description: str):
        """
        Контекстный менеджер для измерения времени выполнения запроса.
        
        Args:
            query_description: Описание запроса
        """
        start_time = time.time()
        error = None
        
        try:
            yield
        except Exception as e:
            error = str(e)
            raise
        finally:
            execution_time = time.time() - start_time
            
            # Записываем метрики
            metric = QueryPerformanceMetrics(
                query=query_description,
                execution_time=execution_time,
                timestamp=datetime.now(),
                error=error
            )
            
            self.query_metrics.append(metric)
            
            # Ограничиваем историю
            if len(self.query_metrics) > self.max_metrics_history:
                self.query_metrics = self.query_metrics[-self.max_metrics_history:]
            
            # Логируем медленные запросы
            if execution_time > self.slow_query_threshold:
                logger.warning(
                    f"Slow query detected: {query_description}",
                    extra={
                        "execution_time": execution_time,
                        "query": query_description,
                        "event_type": "slow_query"
                    }
                )
    
    async def analyze_table_structure(self) -> Dict[str, Any]:
        """Анализирует структуру таблиц и индексы."""
        try:
            async with get_session() as session:
                # Получаем информацию о таблицах
                tables_info = {}
                
                # Анализируем таблицу vpnusers
                vpnusers_info = await self._analyze_table(session, "vpnusers")
                tables_info["vpnusers"] = vpnusers_info
                
                # Анализируем таблицу ypayments
                ypayments_info = await self._analyze_table(session, "ypayments")
                tables_info["ypayments"] = ypayments_info
                
                # Анализируем таблицу crypto_payments
                crypto_payments_info = await self._analyze_table(session, "crypto_payments")
                tables_info["crypto_payments"] = crypto_payments_info
                
                return tables_info
                
        except Exception as e:
            logger.error(f"Error analyzing table structure: {e}")
            return {}
    
    async def _analyze_table(self, session: AsyncSession, table_name: str) -> Dict[str, Any]:
        """Анализирует конкретную таблицу."""
        try:
            # Получаем информацию о столбцах
            columns_query = text(f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = '{table_name}'
                ORDER BY ordinal_position
            """)
            
            columns_result = await session.execute(columns_query)
            columns = [dict(row._mapping) for row in columns_result]
            
            # Получаем информацию об индексах (MySQL)
            indexes_query = text(f"""
                SELECT DISTINCT INDEX_NAME as indexname,
                       CONCAT('INDEX ', INDEX_NAME, ' ON ', TABLE_NAME, ' (', GROUP_CONCAT(COLUMN_NAME), ')') as indexdef
                FROM information_schema.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{table_name}'
                GROUP BY INDEX_NAME
            """)
            
            indexes_result = await session.execute(indexes_query)
            indexes = [dict(row._mapping) for row in indexes_result]
            
            # Получаем статистику таблицы (MySQL)
            stats_query = text(f"""
                SELECT
                    TABLE_SCHEMA as schemaname,
                    TABLE_NAME as tablename,
                    COLUMN_NAME as attname,
                    CARDINALITY as n_distinct,
                    NULL as correlation
                FROM information_schema.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{table_name}'
            """)
            
            stats_result = await session.execute(stats_query)
            stats = [dict(row._mapping) for row in stats_result]
            
            # Получаем размер таблицы (MySQL)
            size_query = text(f"""
                SELECT
                    CONCAT(ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2), ' MB') as total_size,
                    CONCAT(ROUND(DATA_LENGTH / 1024 / 1024, 2), ' MB') as table_size
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{table_name}'
            """)
            
            size_result = await session.execute(size_query)
            size_info = dict(size_result.fetchone()._mapping)
            
            return {
                "columns": columns,
                "indexes": indexes,
                "statistics": stats,
                "size": size_info
            }
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {e}")
            return {}
    
    async def get_connection_pool_metrics(self) -> Optional[ConnectionPoolMetrics]:
        """Получает метрики пула соединений."""
        try:
            if not engine:
                return None
            
            pool = engine.pool
            
            return ConnectionPoolMetrics(
                pool_size=pool.size(),
                checked_in=pool.checkedin(),
                checked_out=pool.checkedout(),
                overflow=pool.overflow(),
                invalid=pool.invalid()
            )
            
        except Exception as e:
            logger.error(f"Error getting connection pool metrics: {e}")
            return None
    
    async def analyze_query_patterns(self) -> Dict[str, Any]:
        """Анализирует паттерны запросов."""
        if not self.query_metrics:
            return {}
        
        # Группируем запросы по типам
        query_groups = {}
        total_time = 0
        slow_queries = []
        
        for metric in self.query_metrics:
            query_type = self._get_query_type(metric.query)
            
            if query_type not in query_groups:
                query_groups[query_type] = {
                    "count": 0,
                    "total_time": 0,
                    "avg_time": 0,
                    "max_time": 0,
                    "min_time": float('inf')
                }
            
            group = query_groups[query_type]
            group["count"] += 1
            group["total_time"] += metric.execution_time
            group["max_time"] = max(group["max_time"], metric.execution_time)
            group["min_time"] = min(group["min_time"], metric.execution_time)
            
            total_time += metric.execution_time
            
            if metric.execution_time > self.slow_query_threshold:
                slow_queries.append(metric)
        
        # Вычисляем средние значения
        for group in query_groups.values():
            group["avg_time"] = group["total_time"] / group["count"]
            if group["min_time"] == float('inf'):
                group["min_time"] = 0
        
        return {
            "total_queries": len(self.query_metrics),
            "total_time": total_time,
            "avg_time": total_time / len(self.query_metrics) if self.query_metrics else 0,
            "slow_queries_count": len(slow_queries),
            "query_groups": query_groups,
            "slow_queries": [
                {
                    "query": q.query,
                    "execution_time": q.execution_time,
                    "timestamp": q.timestamp.isoformat()
                }
                for q in slow_queries[-10:]  # Последние 10 медленных запросов
            ]
        }
    
    def _get_query_type(self, query: str) -> str:
        """Определяет тип запроса."""
        query_lower = query.lower().strip()
        
        if "get_by_telegram_id" in query_lower:
            return "user_lookup_by_tg_id"
        elif "get_by_vpn_id" in query_lower:
            return "user_lookup_by_vpn_id"
        elif "create_user" in query_lower:
            return "user_creation"
        elif "yookassa_payment" in query_lower:
            return "yookassa_payment"
        elif "cryptomus_payment" in query_lower:
            return "cryptomus_payment"
        elif "select" in query_lower:
            return "select"
        elif "insert" in query_lower:
            return "insert"
        elif "update" in query_lower:
            return "update"
        elif "delete" in query_lower:
            return "delete"
        else:
            return "other"
    
    async def suggest_optimizations(self) -> List[str]:
        """Предлагает оптимизации на основе анализа."""
        suggestions = []
        
        # Анализируем структуру таблиц
        tables_info = await self.analyze_table_structure()
        
        # Проверяем индексы для vpnusers
        if "vpnusers" in tables_info:
            vpnusers_indexes = [idx["indexname"] for idx in tables_info["vpnusers"].get("indexes", [])]
            
            if not any("tg_id" in idx for idx in vpnusers_indexes):
                suggestions.append("Создать индекс на vpnusers.tg_id для ускорения поиска пользователей")
            
            if not any("vpn_id" in idx for idx in vpnusers_indexes):
                suggestions.append("Создать индекс на vpnusers.vpn_id для ускорения поиска по VPN ID")
        
        # Проверяем индексы для ypayments
        if "ypayments" in tables_info:
            ypayments_indexes = [idx["indexname"] for idx in tables_info["ypayments"].get("indexes", [])]
            
            if not any("payment_id" in idx for idx in ypayments_indexes):
                suggestions.append("Создать индекс на ypayments.payment_id для ускорения поиска платежей")
        
        # Проверяем индексы для crypto_payments
        if "crypto_payments" in tables_info:
            crypto_indexes = [idx["indexname"] for idx in tables_info["crypto_payments"].get("indexes", [])]
            
            if not any("order_id" in idx for idx in crypto_indexes):
                suggestions.append("Создать индекс на crypto_payments.order_id для ускорения поиска платежей")
        
        # Анализируем паттерны запросов
        query_analysis = await self.analyze_query_patterns()
        
        if query_analysis.get("slow_queries_count", 0) > 0:
            suggestions.append(f"Оптимизировать {query_analysis['slow_queries_count']} медленных запросов")
        
        # Проверяем пул соединений
        pool_metrics = await self.get_connection_pool_metrics()
        if pool_metrics and pool_metrics.overflow > 0:
            suggestions.append("Увеличить размер пула соединений - обнаружен overflow")
        
        return suggestions
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Возвращает отчет о производительности."""
        return {
            "timestamp": datetime.now().isoformat(),
            "metrics_count": len(self.query_metrics),
            "slow_query_threshold": self.slow_query_threshold,
            "recent_queries": [
                {
                    "query": m.query,
                    "execution_time": m.execution_time,
                    "timestamp": m.timestamp.isoformat(),
                    "error": m.error
                }
                for m in self.query_metrics[-10:]  # Последние 10 запросов
            ]
        }


# Глобальный экземпляр анализатора
performance_analyzer = DatabasePerformanceAnalyzer()
