"""
Модели базы данных для системы уведомлений.

Этот модуль содержит SQLAlchemy модели для управления уведомлениями,
включая шаблоны, расписание отправки и логи доставки.
"""

from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import (
    Column, String, Text, DateTime, Boolean, Integer, 
    ForeignKey, JSON, Enum as SQLEnum, Index
)
from sqlalchemy.orm import relationship

from db.declarative_base import Base
from schemas.notification_enums import (
    NotificationType, NotificationStatus, DeliveryStatus,
    NotificationPriority, NotificationChannel, ABTestVariant
)
from utils.id_generator import generate_id


class NotificationTemplate(Base):
    """
    Шаблон уведомления.
    
    Содержит шаблоны сообщений с поддержкой переменных Jinja2
    и настройки для A/B тестирования.
    """
    __tablename__ = "notification_templates"
    
    id = Column(String(32), primary_key=True, default=generate_id)
    name = Column(String(100), nullable=False, comment="Название шаблона")
    description = Column(Text, comment="Описание шаблона")
    
    # Тип и настройки уведомления
    notification_type = Column(SQLEnum(NotificationType), nullable=False, index=True)
    priority = Column(SQLEnum(NotificationPriority), nullable=False, default=NotificationPriority.NORMAL)
    channel = Column(SQLEnum(NotificationChannel), nullable=False, default=NotificationChannel.TELEGRAM)
    
    # Шаблоны сообщений
    subject_template = Column(Text, comment="Шаблон заголовка (для email)")
    body_template = Column(Text, nullable=False, comment="Шаблон тела сообщения")
    
    # Метаданные шаблона
    variables = Column(JSON, comment="Список доступных переменных с описанием")
    default_values = Column(JSON, comment="Значения переменных по умолчанию")
    
    # A/B тестирование
    ab_test_variant = Column(SQLEnum(ABTestVariant), default=ABTestVariant.CONTROL)
    ab_test_weight = Column(Integer, default=100, comment="Вес варианта в A/B тесте (0-100)")
    
    # Настройки отправки
    delay_seconds = Column(Integer, default=0, comment="Задержка перед отправкой в секундах")
    retry_attempts = Column(Integer, default=3, comment="Количество попыток отправки")
    
    # Статус и метаданные
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(100), comment="Кто создал шаблон")
    
    # Связи (закомментированы для упрощения)
    # schedules = relationship("NotificationSchedule", back_populates="template")
    # logs = relationship("NotificationLog", back_populates="template")
    
    # Индексы
    __table_args__ = (
        Index('ix_notification_templates_type_active', 'notification_type', 'is_active'),
        Index('ix_notification_templates_ab_test', 'notification_type', 'ab_test_variant', 'is_active'),
    )
    
    def __repr__(self):
        return f"<NotificationTemplate(id={self.id}, name={self.name}, type={self.notification_type})>"


class NotificationSchedule(Base):
    """
    Расписание отправки уведомлений.
    
    Содержит запланированные уведомления с контекстными данными
    для подстановки в шаблоны.
    """
    __tablename__ = "notification_schedules"
    
    id = Column(String(32), primary_key=True, default=generate_id)
    
    # Связи с пользователем и шаблоном
    user_id = Column(Integer, ForeignKey("vpnusers.id"), nullable=False, index=True)
    template_id = Column(String(32), ForeignKey("notification_templates.id"), nullable=False)
    
    # Параметры отправки
    scheduled_at = Column(DateTime, nullable=False, index=True)
    notification_type = Column(SQLEnum(NotificationType), nullable=False, index=True)
    priority = Column(SQLEnum(NotificationPriority), nullable=False, default=NotificationPriority.NORMAL)
    
    # Контекстные данные для шаблона
    context_data = Column(JSON, comment="Данные для подстановки в шаблон")
    
    # Статус обработки
    status = Column(SQLEnum(NotificationStatus), default=NotificationStatus.PENDING, index=True)
    attempts = Column(Integer, default=0, comment="Количество попыток отправки")
    max_attempts = Column(Integer, default=3, comment="Максимальное количество попыток")
    
    # Временные метки
    created_at = Column(DateTime, default=datetime.utcnow)
    sent_at = Column(DateTime, comment="Время отправки")
    failed_at = Column(DateTime, comment="Время последней неудачной попытки")
    
    # Информация об ошибках
    error_message = Column(Text, comment="Сообщение об ошибке")
    error_code = Column(String(50), comment="Код ошибки")
    
    # Дополнительные настройки
    expires_at = Column(DateTime, comment="Время истечения актуальности уведомления")
    
    # Связи (закомментированы для упрощения)
    # user = relationship("VPNUsers", back_populates="notification_schedules")
    # template = relationship("NotificationTemplate", back_populates="schedules")
    # logs = relationship("NotificationLog", back_populates="schedule")
    
    # Индексы
    __table_args__ = (
        Index('ix_notification_schedules_user_type', 'user_id', 'notification_type'),
        Index('ix_notification_schedules_scheduled_status', 'scheduled_at', 'status'),
        Index('ix_notification_schedules_pending', 'status', 'scheduled_at'),
    )
    
    def __repr__(self):
        return f"<NotificationSchedule(id={self.id}, user_id={self.user_id}, type={self.notification_type}, status={self.status})>"


class NotificationLog(Base):
    """
    Лог отправленных уведомлений.
    
    Содержит историю всех отправленных уведомлений с детальной
    информацией о доставке и метриками.
    """
    __tablename__ = "notification_logs"
    
    id = Column(String(32), primary_key=True, default=generate_id)
    
    # Связи
    user_id = Column(Integer, ForeignKey("vpnusers.id"), nullable=False, index=True)
    template_id = Column(String(32), ForeignKey("notification_templates.id"), nullable=False)
    schedule_id = Column(String(32), ForeignKey("notification_schedules.id"), nullable=True)
    
    # Информация об уведомлении
    notification_type = Column(SQLEnum(NotificationType), nullable=False, index=True)
    channel = Column(SQLEnum(NotificationChannel), nullable=False)
    priority = Column(SQLEnum(NotificationPriority), nullable=False)
    
    # Статус доставки
    delivery_status = Column(SQLEnum(DeliveryStatus), nullable=False, index=True)
    
    # Временные метки
    sent_at = Column(DateTime, default=datetime.utcnow, index=True)
    delivered_at = Column(DateTime, comment="Время подтверждения доставки")
    
    # Telegram специфичные поля
    telegram_message_id = Column(Integer, comment="ID сообщения в Telegram")
    telegram_chat_id = Column(String(50), comment="ID чата в Telegram")
    
    # Контент сообщения
    rendered_subject = Column(Text, comment="Отрендеренный заголовок")
    rendered_body = Column(Text, comment="Отрендеренное тело сообщения")
    context_data = Column(JSON, comment="Контекстные данные на момент отправки")
    
    # A/B тестирование
    ab_test_variant = Column(SQLEnum(ABTestVariant), comment="Вариант A/B теста")
    
    # Метрики
    processing_time_ms = Column(Integer, comment="Время обработки в миллисекундах")
    message_size_bytes = Column(Integer, comment="Размер сообщения в байтах")
    
    # Информация об ошибках
    error_message = Column(Text, comment="Сообщение об ошибке доставки")
    error_code = Column(String(50), comment="Код ошибки доставки")
    
    # Связи (закомментированы для упрощения)
    # user = relationship("VPNUsers", back_populates="notification_logs")
    # template = relationship("NotificationTemplate", back_populates="logs")
    # schedule = relationship("NotificationSchedule", back_populates="logs")
    
    # Индексы
    __table_args__ = (
        Index('ix_notification_logs_user_sent', 'user_id', 'sent_at'),
        Index('ix_notification_logs_type_status', 'notification_type', 'delivery_status'),
        Index('ix_notification_logs_channel_sent', 'channel', 'sent_at'),
        Index('ix_notification_logs_ab_test', 'notification_type', 'ab_test_variant', 'sent_at'),
    )
    
    def __repr__(self):
        return f"<NotificationLog(id={self.id}, user_id={self.user_id}, type={self.notification_type}, status={self.delivery_status})>"


class NotificationPreference(Base):
    """
    Пользовательские настройки уведомлений.
    
    Позволяет пользователям управлять типами уведомлений,
    которые они хотят получать.
    """
    __tablename__ = "notification_preferences"
    
    id = Column(String(32), primary_key=True, default=generate_id)
    user_id = Column(Integer, ForeignKey("vpnusers.id"), nullable=False, index=True)
    
    # Настройки по типам уведомлений
    notification_type = Column(SQLEnum(NotificationType), nullable=False)
    is_enabled = Column(Boolean, default=True, comment="Включены ли уведомления этого типа")
    
    # Настройки каналов
    preferred_channel = Column(SQLEnum(NotificationChannel), default=NotificationChannel.TELEGRAM)
    
    # Настройки времени
    quiet_hours_start = Column(Integer, comment="Начало тихих часов (час в UTC)")
    quiet_hours_end = Column(Integer, comment="Конец тихих часов (час в UTC)")
    timezone = Column(String(50), comment="Часовой пояс пользователя")
    
    # Метаданные
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Связи (закомментированы для упрощения)
    # user = relationship("VPNUsers", back_populates="notification_preferences")
    
    # Индексы
    __table_args__ = (
        Index('ix_notification_preferences_user_type', 'user_id', 'notification_type'),
        # Уникальность: один пользователь - один тип уведомления
        Index('uq_notification_preferences_user_type', 'user_id', 'notification_type', unique=True),
    )
    
    def __repr__(self):
        return f"<NotificationPreference(user_id={self.user_id}, type={self.notification_type}, enabled={self.is_enabled})>"
