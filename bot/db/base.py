from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from utils.logging_config import get_logger

logger = get_logger(__name__)

# Импортируем конфигурацию
import glv

# Создаем движок и фабрику сессий
engine = None
async_session_factory = None

def init_db():
    """Инициализация базы данных с оптимизированными настройками"""
    global engine, async_session_factory

    if engine is None:
        # Оптимизированные настройки для production
        engine_kwargs = {
            'echo': False,  # Отключаем логирование SQL в production
            'echo_pool': False,  # Отключаем логирование пула
            'pool_size': 10,  # Базовый размер пула
            'max_overflow': 20,  # Максимальное количество дополнительных соединений
            'pool_timeout': 30,  # Таймаут получения соединения из пула
            'pool_recycle': 3600,  # Переиспользование соединений каждый час
            'pool_pre_ping': True,  # Проверка соединений перед использованием
        }

        # В режиме разработки включаем логирование
        if glv.config.get('DEBUG', False):
            engine_kwargs['echo'] = True
            engine_kwargs['echo_pool'] = True

        engine = create_async_engine(glv.config['DB_URL'], **engine_kwargs)

        async_session_factory = sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,  # Автоматический flush перед запросами
            autocommit=False
        )

        logger.info(
            "Database engine initialized",
            extra={
                "pool_size": engine_kwargs['pool_size'],
                "max_overflow": engine_kwargs['max_overflow'],
                "pool_timeout": engine_kwargs['pool_timeout'],
                "event_type": "db_engine_init"
            }
        )

@asynccontextmanager
async def get_session():
    """
    Контекстный менеджер для получения асинхронной сессии базы данных.
    Совместим с существующей архитектурой проекта.
    """
    if async_session_factory is None:
        init_db()

    async with async_session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_pool_status():
    """
    Получить статус пула соединений.

    Returns:
        Словарь с информацией о пуле соединений
    """
    if engine is None:
        return {"status": "not_initialized"}

    try:
        pool = engine.pool
        pool_info = {
            "status": "active",
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "total_connections": pool.size() + pool.overflow()
        }

        # invalid() может не существовать в AsyncAdaptedQueuePool
        try:
            pool_info["invalid"] = pool.invalid()
        except AttributeError:
            pool_info["invalid"] = 0

        return pool_info
    except Exception as e:
        logger.error(f"Error getting pool status: {e}")
        return {"status": "error", "error": str(e)}


async def check_db_connection():
    """
    Проверить соединение с БД.

    Returns:
        True если соединение работает
    """
    try:
        async with get_session() as session:
            from sqlalchemy import text
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


def get_engine_info():
    """
    Получить информацию о движке БД.

    Returns:
        Словарь с информацией о движке
    """
    if engine is None:
        return {"status": "not_initialized"}

    engine_info = {
        "status": "initialized",
        "url": str(engine.url).replace(engine.url.password or "", "***"),
        "dialect": engine.dialect.name,
        "driver": engine.dialect.driver,
        "pool_class": engine.pool.__class__.__name__,
        "echo": engine.echo
    }

    # echo_pool может не существовать в AsyncEngine
    try:
        engine_info["echo_pool"] = engine.echo_pool
    except AttributeError:
        engine_info["echo_pool"] = False

    return engine_info