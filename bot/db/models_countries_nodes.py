"""
Модели для управления странами, нодами и inbounds в Marzban.
Основано на актуальной архитектуре Marzban API и документации.
"""

from sqlalchemy import Column, BigInteger, String, Boolean, Text, DateTime, Integer, Float, JSON, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum

from db.declarative_base import Base


class NodeStatus(str, Enum):
    """Статусы нод Marzban"""
    CONNECTED = "connected"      # Нода подключена и активна
    DISCONNECTED = "disconnected"  # Нода отключена
    CONNECTING = "connecting"    # Нода в процессе подключения
    ERROR = "error"             # Ошибка подключения
    MAINTENANCE = "maintenance"  # Техническое обслуживание


class InboundProtocol(str, Enum):
    """Поддерживаемые протоколы inbounds"""
    VLESS = "vless"
    VMESS = "vmess"
    TROJAN = "trojan"
    SHADOWSOCKS = "shadowsocks"


class SecurityType(str, Enum):
    """Типы безопасности для inbounds"""
    REALITY = "reality"
    TLS = "tls"
    NONE = "none"


class NetworkType(str, Enum):
    """Типы сети для inbounds"""
    TCP = "tcp"
    WS = "ws"
    GRPC = "grpc"
    HTTP = "http"


class Country(Base):
    """
    Модель стран для VPN серверов.
    Содержит информацию о доступных странах и их характеристиках.
    """
    __tablename__ = "countries"

    id = Column(String(10), primary_key=True)  # ISO код: "DE", "US", "NL"
    name_en = Column(String(100), nullable=False)  # "Germany"
    name_ru = Column(String(100), nullable=False)  # "Германия"
    iso_code = Column(String(3), nullable=False)  # "DE"
    flag = Column(String(10), nullable=True)  # "🇩🇪"

    # Приоритет и активность
    priority = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    
    # Характеристики для пользователей
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=100)  # Приоритет показа (меньше = выше)
    
    # Метаданные
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Связи
    nodes = relationship("MarzbanNode", back_populates="country", cascade="all, delete-orphan")


class MarzbanNode(Base):
    """
    Модель нод Marzban.
    Представляет физические серверы, подключенные к основной панели Marzban.
    """
    __tablename__ = "marzban_nodes"

    id = Column(String(32), primary_key=True)  # "de-fra-01", "us-ny-01"
    name = Column(String(128), nullable=False)  # "Germany Frankfurt #1"
    
    # Связь со страной
    country_id = Column(String(8), ForeignKey('countries.id'), nullable=False)
    city = Column(String(64))  # "Frankfurt", "New York"
    
    # Конфигурация Marzban Node (из документации)
    node_name = Column(String(64), nullable=False)  # Имя ноды в Marzban панели
    node_address = Column(String(256), nullable=False)  # IP/домен ноды
    service_port = Column(Integer, default=62050)  # SERVICE_PORT для подключения к панели
    api_port = Column(Integer, default=62051)  # XRAY_API_PORT для управления Xray
    
    # SSL конфигурация (из документации Marzban Node)
    ssl_cert_path = Column(String(256))  # Путь к ssl_client_cert.pem
    use_ssl = Column(Boolean, default=True)
    
    # Статус и мониторинг
    status = Column(String(16), default=NodeStatus.DISCONNECTED)
    last_check = Column(DateTime)
    last_online = Column(DateTime)
    
    # Статистика использования
    current_users = Column(Integer, default=0)
    max_users = Column(Integer, default=1000)
    usage_ratio = Column(Float, default=1.0)  # Коэффициент использования из Marzban
    
    # Производительность
    cpu_usage = Column(Float, default=0.0)  # Процент использования CPU
    memory_usage = Column(Float, default=0.0)  # Процент использования памяти
    network_upload = Column(BigInteger, default=0)  # Байт отправлено
    network_download = Column(BigInteger, default=0)  # Байт получено
    
    # Настройки
    is_active = Column(Boolean, default=True)
    auto_add_to_inbounds = Column(Boolean, default=True)  # Из документации Marzban Node
    
    # Метаданные
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Связи
    country = relationship("Country", back_populates="nodes")
    inbounds = relationship("MarzbanInbound", back_populates="primary_node")


class MarzbanInbound(Base):
    """
    Модель inbounds Marzban.
    Представляет настроенные inbounds из Core Settings панели Marzban.
    """
    __tablename__ = "marzban_inbounds"

    id = Column(String(32), primary_key=True)  # "vless-reality-de-01"
    tag = Column(String(64), unique=True, nullable=False)  # "VLESS TCP REALITY DE"
    
    # Основные настройки протокола
    protocol = Column(String(16), nullable=False)  # vless, vmess, trojan, shadowsocks
    port = Column(Integer, nullable=False)
    listen_address = Column(String(64), default="0.0.0.0")
    
    # Настройки сети и безопасности
    network_type = Column(String(16), default=NetworkType.TCP)  # tcp, ws, grpc
    security_type = Column(String(16), default=SecurityType.NONE)  # reality, tls, none
    
    # Связь с основной нодой (из Host Settings)
    primary_node_id = Column(String(32), ForeignKey('marzban_nodes.id'))
    
    # Резервные ноды (JSON массив node_id)
    backup_node_ids = Column(JSON, default=list)
    
    # Настройки протокола (JSON)
    protocol_settings = Column(JSON, default=dict)  # Специфичные настройки протокола
    stream_settings = Column(JSON, default=dict)  # streamSettings из конфигурации
    
    # TLS/Reality настройки
    tls_settings = Column(JSON, default=dict)  # Настройки TLS
    reality_settings = Column(JSON, default=dict)  # Настройки Reality
    
    # Статус и мониторинг
    is_active = Column(Boolean, default=True)
    current_connections = Column(Integer, default=0)
    total_traffic = Column(BigInteger, default=0)
    
    # Метаданные
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Связи
    primary_node = relationship("MarzbanNode", back_populates="inbounds")


class UserNodePreference(Base):
    """
    Модель предпочтений пользователей по выбору нод и протоколов.
    """
    __tablename__ = "user_node_preferences"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    tg_id = Column(BigInteger, nullable=False, index=True)  # Telegram ID пользователя
    
    # Предпочтения по странам (JSON массив ISO кодов)
    preferred_countries = Column(JSON, default=list)  # ["DE", "NL", "US"]
    
    # Предпочтения по протоколам (JSON массив)
    preferred_protocols = Column(JSON, default=list)  # ["vless", "vmess"]
    
    # Настройки автовыбора
    auto_select_optimal = Column(Boolean, default=True)
    prefer_low_latency = Column(Boolean, default=True)
    prefer_low_load = Column(Boolean, default=True)
    
    # Последний выбор
    last_selected_country = Column(String(8), ForeignKey('countries.id'))
    last_selected_node = Column(String(32), ForeignKey('marzban_nodes.id'))
    
    # Метаданные
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


class NodeStatistics(Base):
    """
    Модель для хранения статистики нод по времени.
    Используется для анализа производительности и выбора оптимальных нод.
    """
    __tablename__ = "node_statistics"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    node_id = Column(String(32), ForeignKey('marzban_nodes.id'), nullable=False)
    
    # Статистика на момент времени
    timestamp = Column(DateTime, server_default=func.now())
    users_count = Column(Integer, default=0)
    cpu_usage = Column(Float, default=0.0)
    memory_usage = Column(Float, default=0.0)
    network_upload_speed = Column(BigInteger, default=0)  # Байт/сек
    network_download_speed = Column(BigInteger, default=0)  # Байт/сек
    
    # Производительность
    response_time_ms = Column(Integer, default=0)  # Время отклика API
    is_online = Column(Boolean, default=True)
    
    # Индексы для быстрого поиска
    __table_args__ = (
        {'mysql_engine': 'InnoDB'}
    )
