"""
Методы для работы с зашифрованными данными в базе данных.
Отдельный модуль для избежания циклических импортов.
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from db.models import EncryptedConfig, UserSavedPaymentData
from utils.config_encryption import ConfigEncryptionService, UserPaymentDataService
from db.methods import engine


async def get_config_encryption_service() -> ConfigEncryptionService:
    """
    Создает сервис для работы с зашифрованными конфигурациями.
    
    Returns:
        Экземпляр ConfigEncryptionService
    """
    session = AsyncSession(engine)
    return ConfigEncryptionService(session)


async def get_user_payment_service() -> UserPaymentDataService:
    """
    Создает сервис для работы с зашифрованными данными пользователей.
    
    Returns:
        Экземпляр UserPaymentDataService
    """
    session = AsyncSession(engine)
    return UserPaymentDataService(session)


async def store_encrypted_config(key_name: str, value: str, description: Optional[str] = None) -> bool:
    """
    Сохраняет зашифрованную конфигурацию.
    
    Args:
        key_name: Имя ключа конфигурации
        value: Значение для шифрования
        description: Описание ключа
        
    Returns:
        True если успешно сохранено
    """
    service = await get_config_encryption_service()
    try:
        return await service.store_config(key_name, value, description)
    finally:
        await service.session.close()


async def get_encrypted_config(key_name: str) -> Optional[str]:
    """
    Получает расшифрованную конфигурацию.
    
    Args:
        key_name: Имя ключа конфигурации
        
    Returns:
        Расшифрованное значение или None
    """
    service = await get_config_encryption_service()
    try:
        return await service.get_config(key_name)
    finally:
        await service.session.close()


async def save_user_payment_data(tg_id: int, payment_method: str, payment_data: dict) -> bool:
    """
    Сохраняет зашифрованные данные платежного метода пользователя.
    
    Args:
        tg_id: Telegram ID пользователя
        payment_method: Тип платежного метода
        payment_data: Данные для шифрования
        
    Returns:
        True если успешно сохранено
    """
    service = await get_user_payment_service()
    try:
        return await service.save_payment_data(tg_id, payment_method, payment_data)
    finally:
        await service.session.close()


async def get_user_payment_data(tg_id: int, payment_method: Optional[str] = None) -> list:
    """
    Получает зашифрованные данные платежных методов пользователя.
    
    Args:
        tg_id: Telegram ID пользователя
        payment_method: Тип платежного метода (опционально)
        
    Returns:
        Список расшифрованных данных
    """
    service = await get_user_payment_service()
    try:
        return await service.get_user_payment_data(tg_id, payment_method)
    finally:
        await service.session.close()


async def delete_user_payment_data(tg_id: int, data_id: int) -> bool:
    """
    Удаляет сохраненные данные платежного метода пользователя.
    
    Args:
        tg_id: Telegram ID пользователя
        data_id: ID записи с данными
        
    Returns:
        True если успешно удалено
    """
    service = await get_user_payment_service()
    try:
        return await service.delete_payment_data(tg_id, data_id)
    finally:
        await service.session.close()


async def list_encrypted_configs() -> list[str]:
    """
    Возвращает список всех ключей зашифрованных конфигураций.
    
    Returns:
        Список имен ключей
    """
    service = await get_config_encryption_service()
    try:
        return await service.list_config_keys()
    finally:
        await service.session.close()


async def delete_encrypted_config(key_name: str) -> bool:
    """
    Удаляет зашифрованную конфигурацию.
    
    Args:
        key_name: Имя ключа конфигурации
        
    Returns:
        True если успешно удалено
    """
    service = await get_config_encryption_service()
    try:
        return await service.delete_config(key_name)
    finally:
        await service.session.close()
