"""
Система миграций для VPN бота.
Управление изменениями схемы БД с версионированием.
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from sqlalchemy import text, MetaData, Table, Index
from sqlalchemy.ext.asyncio import AsyncSession

from .base import get_session, engine
from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class Migration:
    """Класс для представления миграции."""
    version: str
    name: str
    description: str
    up_sql: str
    down_sql: str
    created_at: datetime


class MigrationManager:
    """Менеджер миграций БД."""
    
    def __init__(self):
        self.migrations_table = "schema_migrations"
        self.migrations: List[Migration] = []
        self._load_migrations()
    
    def _load_migrations(self):
        """Загружает все миграции."""
        # Миграция 001: Создание индексов для производительности
        self.migrations.append(Migration(
            version="001",
            name="add_performance_indexes",
            description="Добавление индексов для часто используемых полей",
            up_sql="""
                -- Создаем индексы только если они не существуют
                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vpnusers' AND INDEX_NAME = 'idx_vpnusers_tg_id') = 0,
                    'CREATE INDEX idx_vpnusers_tg_id ON vpnusers(tg_id)',
                    'SELECT "Index idx_vpnusers_tg_id already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vpnusers' AND INDEX_NAME = 'idx_vpnusers_vpn_id') = 0,
                    'CREATE INDEX idx_vpnusers_vpn_id ON vpnusers(vpn_id)',
                    'SELECT "Index idx_vpnusers_vpn_id already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vpnusers' AND INDEX_NAME = 'idx_vpnusers_tg_id_test') = 0,
                    'CREATE INDEX idx_vpnusers_tg_id_test ON vpnusers(tg_id, test)',
                    'SELECT "Index idx_vpnusers_tg_id_test already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'yookassa_payments' AND INDEX_NAME = 'idx_yookassa_payments_payment_id') = 0,
                    'CREATE INDEX idx_yookassa_payments_payment_id ON yookassa_payments(payment_id)',
                    'SELECT "Index idx_yookassa_payments_payment_id already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'yookassa_payments' AND INDEX_NAME = 'idx_yookassa_payments_tg_id') = 0,
                    'CREATE INDEX idx_yookassa_payments_tg_id ON yookassa_payments(tg_id)',
                    'SELECT "Index idx_yookassa_payments_tg_id already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crypto_payments' AND INDEX_NAME = 'idx_crypto_payments_order_id') = 0,
                    'CREATE INDEX idx_crypto_payments_order_id ON crypto_payments(order_id)',
                    'SELECT "Index idx_crypto_payments_order_id already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crypto_payments' AND INDEX_NAME = 'idx_crypto_payments_tg_id') = 0,
                    'CREATE INDEX idx_crypto_payments_tg_id ON crypto_payments(tg_id)',
                    'SELECT "Index idx_crypto_payments_tg_id already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'yookassa_payments' AND INDEX_NAME = 'idx_yookassa_payments_tg_id_callback') = 0,
                    'CREATE INDEX idx_yookassa_payments_tg_id_callback ON yookassa_payments(tg_id, callback)',
                    'SELECT "Index idx_yookassa_payments_tg_id_callback already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crypto_payments' AND INDEX_NAME = 'idx_crypto_payments_tg_id_callback') = 0,
                    'CREATE INDEX idx_crypto_payments_tg_id_callback ON crypto_payments(tg_id, callback)',
                    'SELECT "Index idx_crypto_payments_tg_id_callback already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            """,
            down_sql="""
                -- Удаление индексов
                DROP INDEX IF EXISTS idx_vpnusers_tg_id;
                DROP INDEX IF EXISTS idx_vpnusers_vpn_id;
                DROP INDEX IF EXISTS idx_vpnusers_tg_id_test;
                DROP INDEX IF EXISTS idx_yookassa_payments_payment_id;
                DROP INDEX IF EXISTS idx_yookassa_payments_tg_id;
                DROP INDEX IF EXISTS idx_crypto_payments_order_id;
                DROP INDEX IF EXISTS idx_crypto_payments_tg_id;
                DROP INDEX IF EXISTS idx_yookassa_payments_tg_id_callback;
                DROP INDEX IF EXISTS idx_crypto_payments_tg_id_callback;
            """,
            created_at=datetime.now()
        ))
        
        # Миграция 002: Добавление timestamps
        self.migrations.append(Migration(
            version="002",
            name="add_timestamps",
            description="Добавление полей created_at и updated_at",
            up_sql="""
                -- Добавляем timestamps в vpnusers только если столбцы не существуют
                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vpnusers' AND COLUMN_NAME = 'created_at') = 0,
                    'ALTER TABLE vpnusers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'SELECT "Column vpnusers.created_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vpnusers' AND COLUMN_NAME = 'updated_at') = 0,
                    'ALTER TABLE vpnusers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
                    'SELECT "Column vpnusers.updated_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                -- Добавляем timestamps в yookassa_payments только если столбцы не существуют
                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'yookassa_payments' AND COLUMN_NAME = 'created_at') = 0,
                    'ALTER TABLE yookassa_payments ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'SELECT "Column yookassa_payments.created_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'yookassa_payments' AND COLUMN_NAME = 'updated_at') = 0,
                    'ALTER TABLE yookassa_payments ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
                    'SELECT "Column yookassa_payments.updated_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                -- Добавляем timestamps в crypto_payments только если столбцы не существуют
                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crypto_payments' AND COLUMN_NAME = 'created_at') = 0,
                    'ALTER TABLE crypto_payments ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'SELECT "Column crypto_payments.created_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crypto_payments' AND COLUMN_NAME = 'updated_at') = 0,
                    'ALTER TABLE crypto_payments ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
                    'SELECT "Column crypto_payments.updated_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                -- Создаем индексы для timestamps только если они не существуют
                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'vpnusers' AND INDEX_NAME = 'idx_vpnusers_created_at') = 0,
                    'CREATE INDEX idx_vpnusers_created_at ON vpnusers(created_at)',
                    'SELECT "Index idx_vpnusers_created_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'yookassa_payments' AND INDEX_NAME = 'idx_yookassa_payments_created_at') = 0,
                    'CREATE INDEX idx_yookassa_payments_created_at ON yookassa_payments(created_at)',
                    'SELECT "Index idx_yookassa_payments_created_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;

                SET @sql = (SELECT IF(
                    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crypto_payments' AND INDEX_NAME = 'idx_crypto_payments_created_at') = 0,
                    'CREATE INDEX idx_crypto_payments_created_at ON crypto_payments(created_at)',
                    'SELECT "Index idx_crypto_payments_created_at already exists"'
                ));
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            """,
            down_sql="""
                -- Удаляем индексы
                DROP INDEX idx_vpnusers_created_at;
                DROP INDEX idx_yookassa_payments_created_at;
                DROP INDEX idx_crypto_payments_created_at;

                -- Удаляем столбцы
                ALTER TABLE vpnusers DROP COLUMN created_at, DROP COLUMN updated_at;
                ALTER TABLE yookassa_payments DROP COLUMN created_at, DROP COLUMN updated_at;
                ALTER TABLE crypto_payments DROP COLUMN created_at, DROP COLUMN updated_at;
            """,
            created_at=datetime.now()
        ))
        
        # Миграция 003: Оптимизация для статистики
        self.migrations.append(Migration(
            version="003",
            name="add_statistics_optimizations",
            description="Добавление индексов и представлений для статистики",
            up_sql="""
                -- Представление для статистики пользователей (MySQL)
                CREATE VIEW user_statistics AS
                SELECT
                    COUNT(*) as total_users,
                    SUM(CASE WHEN test = true THEN 1 ELSE 0 END) as test_users,
                    SUM(CASE WHEN test = false THEN 1 ELSE 0 END) as regular_users,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as users_today,
                    SUM(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as users_week,
                    SUM(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as users_month
                FROM vpnusers;

                -- Представление для статистики платежей (MySQL)
                CREATE VIEW payment_statistics AS
                SELECT
                    'yookassa' as payment_type,
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as payments_today,
                    SUM(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as payments_week,
                    SUM(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as payments_month
                FROM yookassa_payments
                UNION ALL
                SELECT
                    'cryptomus' as payment_type,
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as payments_today,
                    SUM(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as payments_week,
                    SUM(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as payments_month
                FROM crypto_payments;
            """,
            down_sql="""
                -- Удаляем представления
                DROP VIEW user_statistics;
                DROP VIEW payment_statistics;
            """,
            created_at=datetime.now()
        ))

        # Миграция 004: Создание таблиц стран и нод
        self.migrations.append(Migration(
            version="004",
            name="create_countries_nodes_tables",
            description="Создает таблицы для управления странами, нодами и inbounds",
            up_sql="""
                -- Создание таблицы стран
                CREATE TABLE IF NOT EXISTS countries (
                    id VARCHAR(8) PRIMARY KEY,
                    name VARCHAR(128) NOT NULL,
                    name_ru VARCHAR(128) NOT NULL,
                    flag VARCHAR(8) NOT NULL,
                    continent VARCHAR(32),
                    region VARCHAR(64),
                    is_active BOOLEAN DEFAULT TRUE,
                    priority INTEGER DEFAULT 100,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                );

                -- Создание таблицы нод Marzban
                CREATE TABLE IF NOT EXISTS marzban_nodes (
                    id VARCHAR(32) PRIMARY KEY,
                    name VARCHAR(128) NOT NULL,
                    country_id VARCHAR(8) NOT NULL,
                    city VARCHAR(64),
                    node_name VARCHAR(64) NOT NULL,
                    node_address VARCHAR(256) NOT NULL,
                    service_port INTEGER DEFAULT 62050,
                    api_port INTEGER DEFAULT 62051,
                    ssl_cert_path VARCHAR(256),
                    use_ssl BOOLEAN DEFAULT TRUE,
                    status VARCHAR(16) DEFAULT 'disconnected',
                    last_check TIMESTAMP NULL,
                    last_online TIMESTAMP NULL,
                    current_users INTEGER DEFAULT 0,
                    max_users INTEGER DEFAULT 1000,
                    usage_ratio FLOAT DEFAULT 1.0,
                    cpu_usage FLOAT DEFAULT 0.0,
                    memory_usage FLOAT DEFAULT 0.0,
                    network_upload BIGINT DEFAULT 0,
                    network_download BIGINT DEFAULT 0,
                    is_active BOOLEAN DEFAULT TRUE,
                    auto_add_to_inbounds BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE
                );

                -- Создание таблицы inbounds Marzban
                CREATE TABLE IF NOT EXISTS marzban_inbounds (
                    id VARCHAR(32) PRIMARY KEY,
                    tag VARCHAR(64) UNIQUE NOT NULL,
                    protocol VARCHAR(16) NOT NULL,
                    port INTEGER NOT NULL,
                    listen_address VARCHAR(64) DEFAULT '0.0.0.0',
                    network_type VARCHAR(16) DEFAULT 'tcp',
                    security_type VARCHAR(16) DEFAULT 'none',
                    primary_node_id VARCHAR(32),
                    backup_node_ids JSON,
                    protocol_settings JSON,
                    stream_settings JSON,
                    tls_settings JSON,
                    reality_settings JSON,
                    is_active BOOLEAN DEFAULT TRUE,
                    current_connections INTEGER DEFAULT 0,
                    total_traffic BIGINT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (primary_node_id) REFERENCES marzban_nodes(id) ON DELETE SET NULL
                );

                -- Создание таблицы предпочтений пользователей
                CREATE TABLE IF NOT EXISTS user_node_preferences (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT,
                    tg_id BIGINT NOT NULL,
                    preferred_countries JSON,
                    preferred_protocols JSON,
                    auto_select_optimal BOOLEAN DEFAULT TRUE,
                    prefer_low_latency BOOLEAN DEFAULT TRUE,
                    prefer_low_load BOOLEAN DEFAULT TRUE,
                    last_selected_country VARCHAR(8),
                    last_selected_node VARCHAR(32),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_user_node_preferences_tg_id (tg_id),
                    FOREIGN KEY (last_selected_country) REFERENCES countries(id) ON DELETE SET NULL,
                    FOREIGN KEY (last_selected_node) REFERENCES marzban_nodes(id) ON DELETE SET NULL
                );

                -- Создание таблицы статистики нод
                CREATE TABLE IF NOT EXISTS node_statistics (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT,
                    node_id VARCHAR(32) NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    users_count INTEGER DEFAULT 0,
                    cpu_usage FLOAT DEFAULT 0.0,
                    memory_usage FLOAT DEFAULT 0.0,
                    network_upload_speed BIGINT DEFAULT 0,
                    network_download_speed BIGINT DEFAULT 0,
                    response_time_ms INTEGER DEFAULT 0,
                    is_online BOOLEAN DEFAULT TRUE,
                    INDEX idx_node_statistics_node_id (node_id),
                    INDEX idx_node_statistics_timestamp (timestamp),
                    FOREIGN KEY (node_id) REFERENCES marzban_nodes(id) ON DELETE CASCADE
                );

                -- Создание индексов для оптимизации
                CREATE INDEX idx_countries_is_active ON countries(is_active);
                CREATE INDEX idx_countries_priority ON countries(priority);
                CREATE INDEX idx_marzban_nodes_country_id ON marzban_nodes(country_id);
                CREATE INDEX idx_marzban_nodes_status ON marzban_nodes(status);
                CREATE INDEX idx_marzban_nodes_is_active ON marzban_nodes(is_active);
                CREATE INDEX idx_marzban_inbounds_protocol ON marzban_inbounds(protocol);
                CREATE INDEX idx_marzban_inbounds_primary_node_id ON marzban_inbounds(primary_node_id);
                CREATE INDEX idx_marzban_inbounds_is_active ON marzban_inbounds(is_active);
            """,
            down_sql="""
                -- Удаление таблиц в обратном порядке (из-за внешних ключей)
                DROP TABLE IF EXISTS node_statistics;
                DROP TABLE IF EXISTS user_node_preferences;
                DROP TABLE IF EXISTS marzban_inbounds;
                DROP TABLE IF EXISTS marzban_nodes;
                DROP TABLE IF EXISTS countries;
            """,
            created_at=datetime.now()
        ))

    async def create_migrations_table(self):
        """Создает таблицу для отслеживания миграций."""
        try:
            async with get_session() as session:
                create_table_sql = f"""
                    CREATE TABLE IF NOT EXISTS {self.migrations_table} (
                        version VARCHAR(10) PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        execution_time_ms INTEGER
                    );
                """
                
                await session.execute(text(create_table_sql))
                await session.commit()
                
                logger.info("Migrations table created successfully")
                
        except Exception as e:
            logger.error(f"Error creating migrations table: {e}")
            raise
    
    async def get_applied_migrations(self) -> List[str]:
        """Получает список примененных миграций."""
        try:
            async with get_session() as session:
                query = text(f"SELECT version FROM {self.migrations_table} ORDER BY version")
                result = await session.execute(query)
                return [row[0] for row in result.fetchall()]
                
        except Exception as e:
            # Если таблица не существует, возвращаем пустой список
            if "does not exist" in str(e).lower():
                return []
            logger.error(f"Error getting applied migrations: {e}")
            raise
    
    async def apply_migration(self, migration: Migration) -> bool:
        """Применяет миграцию."""
        try:
            start_time = datetime.now()
            
            async with get_session() as session:
                # Выполняем SQL миграции
                for sql_statement in migration.up_sql.split(';'):
                    sql_statement = sql_statement.strip()
                    if sql_statement:
                        await session.execute(text(sql_statement))
                
                # Записываем информацию о миграции
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                
                insert_sql = text(f"""
                    INSERT INTO {self.migrations_table} 
                    (version, name, description, execution_time_ms) 
                    VALUES (:version, :name, :description, :execution_time)
                """)
                
                await session.execute(insert_sql, {
                    "version": migration.version,
                    "name": migration.name,
                    "description": migration.description,
                    "execution_time": execution_time
                })
                
                await session.commit()
                
                logger.info(
                    f"Migration {migration.version} applied successfully",
                    extra={
                        "migration_version": migration.version,
                        "migration_name": migration.name,
                        "execution_time_ms": execution_time,
                        "event_type": "migration_applied"
                    }
                )
                
                return True
                
        except Exception as e:
            logger.error(f"Error applying migration {migration.version}: {e}")
            raise
    
    async def rollback_migration(self, migration: Migration) -> bool:
        """Откатывает миграцию."""
        try:
            start_time = datetime.now()
            
            async with get_session() as session:
                # Выполняем SQL отката
                for sql_statement in migration.down_sql.split(';'):
                    sql_statement = sql_statement.strip()
                    if sql_statement:
                        await session.execute(text(sql_statement))
                
                # Удаляем запись о миграции
                delete_sql = text(f"DELETE FROM {self.migrations_table} WHERE version = :version")
                await session.execute(delete_sql, {"version": migration.version})
                
                await session.commit()
                
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                
                logger.info(
                    f"Migration {migration.version} rolled back successfully",
                    extra={
                        "migration_version": migration.version,
                        "migration_name": migration.name,
                        "execution_time_ms": execution_time,
                        "event_type": "migration_rolled_back"
                    }
                )
                
                return True
                
        except Exception as e:
            logger.error(f"Error rolling back migration {migration.version}: {e}")
            raise
    
    async def migrate_up(self, target_version: Optional[str] = None) -> List[str]:
        """Применяет все неприменённые миграции до указанной версии."""
        await self.create_migrations_table()
        
        applied_migrations = await self.get_applied_migrations()
        applied_versions = []
        
        for migration in self.migrations:
            if migration.version in applied_migrations:
                continue
            
            if target_version and migration.version > target_version:
                break
            
            await self.apply_migration(migration)
            applied_versions.append(migration.version)
        
        return applied_versions
    
    async def migrate_down(self, target_version: str) -> List[str]:
        """Откатывает миграции до указанной версии."""
        applied_migrations = await self.get_applied_migrations()
        rolled_back_versions = []
        
        # Откатываем в обратном порядке
        for migration in reversed(self.migrations):
            if migration.version not in applied_migrations:
                continue
            
            if migration.version <= target_version:
                break
            
            await self.rollback_migration(migration)
            rolled_back_versions.append(migration.version)
        
        return rolled_back_versions
    
    async def get_migration_status(self) -> Dict[str, Any]:
        """Получает статус миграций."""
        try:
            # Создаем таблицу миграций если её нет
            await self.create_migrations_table()

            applied_migrations = await self.get_applied_migrations()

            status = {
                "total_migrations": len(self.migrations),
                "applied_migrations": len(applied_migrations),
                "pending_migrations": len(self.migrations) - len(applied_migrations),
                "migrations": []
            }

            for migration in self.migrations:
                is_applied = migration.version in applied_migrations
                status["migrations"].append({
                    "version": migration.version,
                    "name": migration.name,
                    "description": migration.description,
                    "applied": is_applied,
                    "created_at": migration.created_at.isoformat()
                })

            return status

        except Exception as e:
            logger.error(f"Error getting migration status: {e}")
            return {"error": str(e)}


# Глобальный экземпляр менеджера миграций
migration_manager = MigrationManager()
