"""
Перечисления для системы уведомлений.

Этот модуль содержит все Enum'ы, используемые в системе уведомлений,
включая типы уведомлений, статусы доставки и статусы обработки.
"""

from enum import Enum


class NotificationType(str, Enum):
    """Типы уведомлений в системе."""
    
    # Уведомления об истечении подписки
    SUBSCRIPTION_EXPIRING_7_DAYS = "subscription_expiring_7_days"
    SUBSCRIPTION_EXPIRING_3_DAYS = "subscription_expiring_3_days"
    SUBSCRIPTION_EXPIRING_1_DAY = "subscription_expiring_1_day"
    SUBSCRIPTION_EXPIRED = "subscription_expired"
    
    # Уведомления о состоянии нод
    NODE_UNAVAILABLE = "node_unavailable"
    NODE_RESTORED = "node_restored"
    NODE_MAINTENANCE = "node_maintenance"
    
    # Уведомления о платежах
    PAYMENT_FAILED = "payment_failed"
    PAYMENT_SUCCESS = "payment_success"
    SUBSCRIPTION_RENEWED = "subscription_renewed"
    REFUND_PROCESSED = "refund_processed"
    
    # Системные уведомления
    WELCOME_MESSAGE = "welcome_message"
    ACCOUNT_SUSPENDED = "account_suspended"
    ACCOUNT_RESTORED = "account_restored"
    SECURITY_ALERT = "security_alert"
    
    # Промо уведомления
    SPECIAL_OFFER = "special_offer"
    DISCOUNT_AVAILABLE = "discount_available"
    NEW_FEATURE_ANNOUNCEMENT = "new_feature_announcement"


class NotificationStatus(str, Enum):
    """Статусы обработки уведомлений."""
    
    PENDING = "pending"      # Ожидает отправки
    PROCESSING = "processing"  # В процессе отправки
    SENT = "sent"           # Успешно отправлено
    FAILED = "failed"       # Ошибка отправки
    CANCELLED = "cancelled"  # Отменено
    EXPIRED = "expired"     # Истек срок отправки


class DeliveryStatus(str, Enum):
    """Статусы доставки уведомлений."""
    
    DELIVERED = "delivered"           # Доставлено успешно
    FAILED = "failed"                # Ошибка доставки
    BLOCKED_BY_USER = "blocked_by_user"  # Заблокировано пользователем
    RATE_LIMITED = "rate_limited"     # Ограничение по частоте
    INVALID_RECIPIENT = "invalid_recipient"  # Неверный получатель
    TIMEOUT = "timeout"              # Таймаут доставки


class NotificationPriority(str, Enum):
    """Приоритеты уведомлений."""
    
    LOW = "low"          # Низкий приоритет (промо, новости)
    NORMAL = "normal"    # Обычный приоритет (истечение подписки)
    HIGH = "high"        # Высокий приоритет (проблемы с нодами)
    URGENT = "urgent"    # Критический приоритет (безопасность)


class NotificationChannel(str, Enum):
    """Каналы доставки уведомлений."""
    
    TELEGRAM = "telegram"    # Telegram бот
    EMAIL = "email"         # Email (для будущего использования)
    PUSH = "push"           # Push уведомления (для будущего использования)
    SMS = "sms"             # SMS (для будущего использования)


class TemplateVariableType(str, Enum):
    """Типы переменных в шаблонах."""
    
    STRING = "string"       # Строковое значение
    INTEGER = "integer"     # Целое число
    FLOAT = "float"         # Число с плавающей точкой
    BOOLEAN = "boolean"     # Логическое значение
    DATE = "date"           # Дата
    DATETIME = "datetime"   # Дата и время
    URL = "url"             # URL ссылка
    CURRENCY = "currency"   # Денежная сумма


class ABTestVariant(str, Enum):
    """Варианты A/B тестирования."""
    
    CONTROL = "control"     # Контрольная группа (A)
    VARIANT_B = "variant_b" # Вариант B
    VARIANT_C = "variant_c" # Вариант C (для многовариантного тестирования)


# Маппинг типов уведомлений к их приоритетам
NOTIFICATION_PRIORITY_MAP = {
    # Критические уведомления
    NotificationType.SECURITY_ALERT: NotificationPriority.URGENT,
    NotificationType.ACCOUNT_SUSPENDED: NotificationPriority.URGENT,
    
    # Высокий приоритет
    NotificationType.NODE_UNAVAILABLE: NotificationPriority.HIGH,
    NotificationType.PAYMENT_FAILED: NotificationPriority.HIGH,
    NotificationType.SUBSCRIPTION_EXPIRED: NotificationPriority.HIGH,
    
    # Обычный приоритет
    NotificationType.SUBSCRIPTION_EXPIRING_1_DAY: NotificationPriority.NORMAL,
    NotificationType.SUBSCRIPTION_EXPIRING_3_DAYS: NotificationPriority.NORMAL,
    NotificationType.SUBSCRIPTION_EXPIRING_7_DAYS: NotificationPriority.NORMAL,
    NotificationType.SUBSCRIPTION_RENEWED: NotificationPriority.NORMAL,
    NotificationType.NODE_RESTORED: NotificationPriority.NORMAL,
    NotificationType.PAYMENT_SUCCESS: NotificationPriority.NORMAL,
    NotificationType.WELCOME_MESSAGE: NotificationPriority.NORMAL,
    
    # Низкий приоритет
    NotificationType.SPECIAL_OFFER: NotificationPriority.LOW,
    NotificationType.DISCOUNT_AVAILABLE: NotificationPriority.LOW,
    NotificationType.NEW_FEATURE_ANNOUNCEMENT: NotificationPriority.LOW,
}


def get_notification_priority(notification_type: NotificationType) -> NotificationPriority:
    """
    Получает приоритет для типа уведомления.
    
    Args:
        notification_type: Тип уведомления
        
    Returns:
        Приоритет уведомления
    """
    return NOTIFICATION_PRIORITY_MAP.get(notification_type, NotificationPriority.NORMAL)


# Маппинг типов уведомлений к каналам доставки по умолчанию
DEFAULT_NOTIFICATION_CHANNELS = {
    NotificationType.SECURITY_ALERT: [NotificationChannel.TELEGRAM],
    NotificationType.SUBSCRIPTION_EXPIRING_1_DAY: [NotificationChannel.TELEGRAM],
    NotificationType.SUBSCRIPTION_EXPIRING_3_DAYS: [NotificationChannel.TELEGRAM],
    NotificationType.SUBSCRIPTION_EXPIRING_7_DAYS: [NotificationChannel.TELEGRAM],
    NotificationType.NODE_UNAVAILABLE: [NotificationChannel.TELEGRAM],
    NotificationType.PAYMENT_FAILED: [NotificationChannel.TELEGRAM],
    # Добавить остальные типы по мере необходимости
}


def get_default_channels(notification_type: NotificationType) -> list[NotificationChannel]:
    """
    Получает каналы доставки по умолчанию для типа уведомления.
    
    Args:
        notification_type: Тип уведомления
        
    Returns:
        Список каналов доставки
    """
    return DEFAULT_NOTIFICATION_CHANNELS.get(notification_type, [NotificationChannel.TELEGRAM])
