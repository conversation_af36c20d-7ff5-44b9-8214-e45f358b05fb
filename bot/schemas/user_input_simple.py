"""
Упрощенные схемы валидации для пользовательского ввода в Telegram боте.
Совместимы с Pydantic v2.
"""

from typing import Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
import re


class ValidationError(Exception):
    """Исключение для ошибок валидации пользовательского ввода."""
    pass


class PaymentMethod(str, Enum):
    """Доступные методы оплаты."""
    YOOKASSA = "kassa"
    CRYPTOMUS = "crypto"
    TELEGRAM_STARS = "stars"


class TelegramUserData(BaseModel):
    """Модель данных пользователя Telegram."""
    id: int = Field(..., description="ID пользователя Telegram")
    first_name: str = Field(..., description="Имя пользователя")
    last_name: Optional[str] = Field(None, description="Фамилия пользователя")
    username: Optional[str] = Field(None, description="Username пользователя")
    language_code: Optional[str] = Field(None, description="Код языка")
    is_bot: bool = Field(False, description="Является ли ботом")


class CallbackData(BaseModel):
    """Базовая модель для callback данных."""
    action: str = Field(..., description="Действие")
    data: Optional[str] = Field(None, description="Дополнительные данные")


class PaymentCallbackData(CallbackData):
    """Модель для callback данных платежей."""
    action: str = Field(..., description="Тип платежа")
    product_callback: str = Field(..., description="Callback товара")
    
    @property
    def payment_method(self) -> PaymentMethod:
        """Возвращает метод оплаты на основе action."""
        if self.action.startswith("pay_kassa"):
            return PaymentMethod.YOOKASSA
        elif self.action.startswith("pay_crypto"):
            return PaymentMethod.CRYPTOMUS
        elif self.action.startswith("pay_stars"):
            return PaymentMethod.TELEGRAM_STARS
        else:
            raise ValueError(f"Unknown payment action: {self.action}")


class UserCommand(BaseModel):
    """Модель для команд пользователя."""
    command: str = Field(..., description="Команда")
    args: Optional[str] = Field(None, description="Аргументы команды")
    user: TelegramUserData = Field(..., description="Данные пользователя")
    chat_id: int = Field(..., description="ID чата")


class MessageText(BaseModel):
    """Модель для текстовых сообщений."""
    text: str = Field(..., description="Текст сообщения")
    user: TelegramUserData = Field(..., description="Данные пользователя")
    chat_id: int = Field(..., description="ID чата")


def validate_user_data(user_data: dict) -> TelegramUserData:
    """
    Валидирует данные пользователя Telegram.
    
    Args:
        user_data: Словарь с данными пользователя
        
    Returns:
        Валидированный объект TelegramUserData
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        user = TelegramUserData(**user_data)
        
        # Дополнительная валидация
        if user.id <= 0:
            raise ValueError('User ID must be positive')
        
        if user.id > 2**63 - 1:
            raise ValueError('User ID exceeds maximum value')
        
        if not user.first_name or len(user.first_name.strip()) == 0:
            raise ValueError('First name cannot be empty')
        
        if len(user.first_name) > 64:
            raise ValueError('First name too long')
        
        if user.username and not re.match(r'^[a-zA-Z0-9_]{5,32}$', user.username):
            raise ValueError('Invalid username format')
        
        if user.language_code and not re.match(r'^[a-z]{2}(-[A-Z]{2})?$', user.language_code):
            raise ValueError('Invalid language code format')
        
        return user
        
    except Exception as e:
        raise ValidationError(f"User data validation failed: {str(e)}")


def parse_callback_data(callback_data: str) -> Union[CallbackData, PaymentCallbackData]:
    """
    Парсит и валидирует callback данные.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        Валидированный объект CallbackData или PaymentCallbackData
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        if not callback_data:
            raise ValueError("Callback data is empty")
        
        if len(callback_data) > 64:
            raise ValueError("Callback data too long")
        
        # Проверяем на безопасные символы
        if not re.match(r'^[a-zA-Z0-9_.-]+$', callback_data):
            raise ValueError('Callback data contains invalid characters')
        
        # Проверяем, является ли это платежным callback
        if callback_data.startswith('pay_'):
            parts = callback_data.split('_', 2)
            if len(parts) < 3:
                raise ValueError("Payment callback requires product data")
            
            action = f"{parts[0]}_{parts[1]}"
            product_callback = parts[2]
            
            # Валидируем product callback
            if not re.match(r'^[a-z_]+$', product_callback):
                raise ValueError('Invalid product callback format')
            
            return PaymentCallbackData(
                action=action,
                data=product_callback,
                product_callback=product_callback
            )
        
        # Обычный callback
        parts = callback_data.split('_', 1)
        action = parts[0]
        data = parts[1] if len(parts) > 1 else None
        
        if not re.match(r'^[a-zA-Z0-9_-]+$', action):
            raise ValueError('Action contains invalid characters')
        
        return CallbackData(action=action, data=data)
        
    except Exception as e:
        raise ValidationError(f"Invalid callback data: {str(e)}")


def validate_user_input(text: str, user_data: dict, chat_id: int) -> Union[UserCommand, MessageText]:
    """
    Валидирует пользовательский ввод.
    
    Args:
        text: Текст сообщения
        user_data: Данные пользователя
        chat_id: ID чата
        
    Returns:
        Валидированный объект UserCommand или MessageText
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        # Валидируем данные пользователя
        user = validate_user_data(user_data)
        
        # Валидируем chat_id
        if abs(chat_id) > 2**63 - 1:
            raise ValueError('Chat ID exceeds maximum value')
        
        # Валидируем текст
        if not text or len(text.strip()) == 0:
            raise ValueError('Message text cannot be empty')
        
        if len(text) > 4096:
            raise ValueError('Message text too long')
        
        # Базовая проверка на безопасность
        if re.search(r'[<>"\']', text):
            # Логируем подозрительный ввод, но не блокируем полностью
            pass
        
        # Проверяем, является ли это командой
        if text.startswith('/'):
            parts = text.split(' ', 1)
            command = parts[0]
            args = parts[1] if len(parts) > 1 else None
            
            # Валидируем команду
            if not re.match(r'^/[a-zA-Z0-9_]+$', command):
                raise ValueError('Invalid command format')
            
            # Валидируем аргументы
            if args and len(args) > 256:
                raise ValueError('Command arguments too long')
            
            return UserCommand(
                command=command.lower(),
                args=args.strip() if args else None,
                user=user,
                chat_id=chat_id
            )
        
        # Обычное текстовое сообщение
        return MessageText(
            text=text.strip(),
            user=user,
            chat_id=chat_id
        )
        
    except Exception as e:
        raise ValidationError(f"User input validation failed: {str(e)}")


def validate_product_callback(callback: str) -> bool:
    """
    Проверяет, валиден ли callback товара.
    
    Args:
        callback: Callback товара
        
    Returns:
        True если валиден
    """
    if not callback:
        return False
    
    # Проверяем формат callback товара
    return bool(re.match(r'^[a-z_]+$', callback))


def is_payment_callback(callback_data: str) -> bool:
    """
    Проверяет, является ли callback данные платежными.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        True если это платежный callback
    """
    return callback_data.startswith('pay_') if callback_data else False


def extract_payment_method(callback_data: str) -> Optional[PaymentMethod]:
    """
    Извлекает метод оплаты из callback данных.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        PaymentMethod или None
    """
    try:
        if callback_data.startswith('pay_kassa_'):
            return PaymentMethod.YOOKASSA
        elif callback_data.startswith('pay_crypto_'):
            return PaymentMethod.CRYPTOMUS
        elif callback_data.startswith('pay_stars_'):
            return PaymentMethod.TELEGRAM_STARS
        return None
    except:
        return None


def extract_product_callback(callback_data: str) -> Optional[str]:
    """
    Извлекает callback товара из платежных callback данных.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        Callback товара или None
    """
    try:
        if not is_payment_callback(callback_data):
            return None
        
        parts = callback_data.split('_', 2)
        if len(parts) >= 3:
            return parts[2]
        return None
    except:
        return None


# Функции для быстрой валидации

def is_valid_user_data(user_data: dict) -> bool:
    """Проверяет, валидны ли данные пользователя."""
    try:
        validate_user_data(user_data)
        return True
    except ValidationError:
        return False


def is_valid_callback_data(callback_data: str) -> bool:
    """Проверяет, валидны ли callback данные."""
    try:
        parse_callback_data(callback_data)
        return True
    except ValidationError:
        return False


def is_safe_text_input(text: str) -> bool:
    """Проверяет, безопасен ли текстовый ввод."""
    if not text:
        return False
    
    # Проверяем длину
    if len(text) > 4096:
        return False
    
    # Проверяем на потенциально опасные паттерны
    dangerous_patterns = [
        r'<script',
        r'javascript:',
        r'data:',
        r'vbscript:',
        r'onload=',
        r'onerror=',
        r'onclick='
    ]
    
    text_lower = text.lower()
    for pattern in dangerous_patterns:
        if re.search(pattern, text_lower):
            return False
    
    return True
