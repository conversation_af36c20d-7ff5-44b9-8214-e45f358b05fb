"""
Схемы валидации для callback данных выбора стран и нод.
Обеспечивает безопасную обработку пользовательского ввода.
"""

import re
from typing import Optional, Union, Literal
from pydantic import BaseModel, Field, validator

from .user_input_simple import ValidationError


class CountryCallbackData(BaseModel):
    """Модель для callback данных стран."""
    action: str = Field(..., description="Действие")
    country_id: Optional[str] = Field(None, description="ID страны")
    page: Optional[int] = Field(None, description="Номер страницы")
    
    @validator('action')
    def validate_action(cls, v):
        """Валидация действия."""
        allowed_actions = [
            'select_countries', 'toggle_country', 'country_info', 'add_favorite',
            'remove_favorite', 'auto_select_country', 'countries_page',
            'back_to_countries', 'back_to_countries_menu', 'save_preferences',
            'clear_preferences', 'my_preferences', 'add_country_preference',
            'remove_pref', 'clear_all_preferences', 'nodes_statistics',
            'back_to_main', 'continent_header', 'preferences_header'
        ]
        
        if v not in allowed_actions:
            raise ValueError(f'Invalid action: {v}')
        return v
    
    @validator('country_id')
    def validate_country_id(cls, v):
        """Валидация ID страны."""
        if v is not None:
            if not re.match(r'^[A-Z]{2}$', v):
                raise ValueError('Country ID must be 2 uppercase letters')
        return v
    
    @validator('page')
    def validate_page(cls, v):
        """Валидация номера страницы."""
        if v is not None:
            if v < 0 or v > 100:
                raise ValueError('Page number must be between 0 and 100')
        return v


class NodeCallbackData(BaseModel):
    """Модель для callback данных нод."""
    action: str = Field(..., description="Действие")
    node_id: Optional[str] = Field(None, description="ID ноды")
    country_id: Optional[str] = Field(None, description="ID страны")
    
    @validator('action')
    def validate_action(cls, v):
        """Валидация действия."""
        allowed_actions = [
            'select_node', 'best_node', 'auto_select_node', 'refresh_nodes',
            'detailed_stats', 'speed_test', 'country_stats'
        ]
        
        if v not in allowed_actions:
            raise ValueError(f'Invalid node action: {v}')
        return v
    
    @validator('node_id')
    def validate_node_id(cls, v):
        """Валидация ID ноды."""
        if v is not None:
            if not re.match(r'^[a-z0-9-]{3,50}$', v):
                raise ValueError('Invalid node ID format')
        return v
    
    @validator('country_id')
    def validate_country_id(cls, v):
        """Валидация ID страны."""
        if v is not None:
            if not re.match(r'^[A-Z]{2}$', v):
                raise ValueError('Country ID must be 2 uppercase letters')
        return v


class ProtocolCallbackData(BaseModel):
    """Модель для callback данных протоколов."""
    action: str = Field(..., description="Действие")
    protocol: Optional[str] = Field(None, description="Протокол")
    
    @validator('action')
    def validate_action(cls, v):
        """Валидация действия."""
        allowed_actions = [
            'protocol_preferences', 'toggle_protocol', 'save_protocol_preferences',
            'reset_protocol_preferences', 'protocols_header'
        ]
        
        if v not in allowed_actions:
            raise ValueError(f'Invalid protocol action: {v}')
        return v
    
    @validator('protocol')
    def validate_protocol(cls, v):
        """Валидация протокола."""
        if v is not None:
            allowed_protocols = ['vless', 'vmess', 'trojan', 'shadowsocks']
            if v not in allowed_protocols:
                raise ValueError(f'Invalid protocol: {v}')
        return v


class ConfirmationCallbackData(BaseModel):
    """Модель для callback данных подтверждения."""
    action: Literal['confirm', 'cancel'] = Field(..., description="Действие подтверждения")
    operation: str = Field(..., description="Операция для подтверждения")
    target: str = Field(..., description="Цель операции")
    
    @validator('operation')
    def validate_operation(cls, v):
        """Валидация операции."""
        allowed_operations = [
            'clear_preferences', 'remove_country', 'reset_protocols',
            'delete_node', 'change_country', 'clear', 'remove'
        ]
        
        if v not in allowed_operations:
            raise ValueError(f'Invalid operation: {v}')
        return v
    
    @validator('target')
    def validate_target(cls, v):
        """Валидация цели."""
        if not re.match(r'^[a-zA-Z0-9_-]{1,50}$', v):
            raise ValueError('Invalid target format')
        return v


def parse_country_callback(callback_data: str) -> Union[CountryCallbackData, NodeCallbackData, 
                                                      ProtocolCallbackData, ConfirmationCallbackData]:
    """
    Парсит и валидирует callback данные для стран и нод.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        Валидированный объект callback данных
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        if not callback_data:
            raise ValueError("Callback data is empty")
        
        if len(callback_data) > 64:
            raise ValueError("Callback data too long")
        
        # Проверяем на безопасные символы
        if not re.match(r'^[a-zA-Z0-9_.-]+$', callback_data):
            raise ValueError('Callback data contains invalid characters')
        
        parts = callback_data.split('_')
        if len(parts) < 1:
            raise ValueError("Invalid callback format")
        
        action = parts[0]
        
        # Определяем тип callback по первому слову
        if action in ['confirm', 'cancel']:
            # Callback подтверждения: confirm_operation_target или cancel_operation_target
            if len(parts) < 3:
                raise ValueError("Confirmation callback requires operation and target")

            return ConfirmationCallbackData(
                action=action,
                operation=parts[1],
                target='_'.join(parts[2:])
            )
        
        elif action in ['toggle', 'save', 'reset'] and len(parts) >= 2 and parts[1] == 'protocol':
            # Callback протоколов: toggle_protocol_vless, save_protocol_preferences
            protocol = parts[2] if len(parts) > 2 else None
            
            return ProtocolCallbackData(
                action='_'.join(parts[:2]) if len(parts) >= 2 else action,
                protocol=protocol
            )
        
        elif action in ['select', 'best', 'auto', 'refresh', 'detailed', 'speed'] and len(parts) >= 2:
            # Callback нод: select_node_id, best_node_country, auto_select_node_country
            if parts[1] in ['node', 'nodes']:
                # select_node_id, refresh_nodes_country
                if len(parts) >= 3:
                    if action == 'select' and parts[1] == 'node':
                        return NodeCallbackData(
                            action='select_node',
                            node_id=parts[2]
                        )
                    elif action == 'refresh' and parts[1] == 'nodes':
                        return NodeCallbackData(
                            action='refresh_nodes',
                            country_id=parts[2]
                        )
                    elif action == 'auto' and parts[1] == 'select' and len(parts) >= 4:
                        # auto_select_node_country
                        return NodeCallbackData(
                            action='auto_select_node',
                            country_id=parts[3]
                        )
                
                return NodeCallbackData(
                    action=f"{action}_{parts[1]}",
                    node_id=parts[2] if len(parts) > 2 else None
                )
            
            elif parts[1] in ['country', 'countries']:
                # toggle_country_id, countries_page_1
                if action == 'toggle' and parts[1] == 'country':
                    return CountryCallbackData(
                        action='toggle_country',
                        country_id=parts[2] if len(parts) > 2 else None
                    )
                elif action == 'countries' and parts[1] == 'page':
                    return CountryCallbackData(
                        action='countries_page',
                        page=int(parts[2]) if len(parts) > 2 and parts[2].isdigit() else 0
                    )
                
                return CountryCallbackData(
                    action=f"{action}_{parts[1]}",
                    country_id=parts[2] if len(parts) > 2 else None
                )
            
            else:
                # best_node_country, speed_test_country, country_stats_country
                return NodeCallbackData(
                    action='_'.join(parts[:2]),
                    country_id=parts[2] if len(parts) > 2 else None
                )
        
        else:
            # Простые callback стран
            country_actions = [
                'select_countries', 'auto_select_country', 'my_preferences',
                'nodes_statistics', 'back_to_main', 'back_to_countries',
                'back_to_countries_menu', 'save_preferences', 'clear_preferences',
                'add_country_preference', 'clear_all_preferences', 'protocol_preferences',
                'continent_header', 'preferences_header'
            ]

            full_action = '_'.join(parts)

            if full_action in country_actions:
                return CountryCallbackData(action=full_action)

            # Специальные случаи для toggle_country_XX
            if action == 'toggle' and len(parts) >= 3 and parts[1] == 'country':
                return CountryCallbackData(
                    action='toggle_country',
                    country_id=parts[2]
                )

            # Специальные случаи для countries_page_X
            if action == 'countries' and len(parts) >= 3 and parts[1] == 'page':
                try:
                    page_num = int(parts[2])
                    return CountryCallbackData(
                        action='countries_page',
                        page=page_num
                    )
                except ValueError:
                    pass

            # Callback с параметрами: country_info_DE, add_favorite_US, remove_pref_FR
            if len(parts) >= 2:
                base_action = '_'.join(parts[:-1])
                param = parts[-1]

                if base_action in ['country_info', 'add_favorite', 'remove_favorite', 'remove_pref']:
                    return CountryCallbackData(
                        action=base_action,
                        country_id=param
                    )

        # Если не удалось определить тип, возвращаем как простой country callback
        return CountryCallbackData(action=callback_data)
        
    except ValueError as e:
        raise ValidationError(f"Country callback validation failed: {str(e)}")
    except Exception as e:
        raise ValidationError(f"Unexpected error parsing country callback: {str(e)}")


def is_country_callback(callback_data: str) -> bool:
    """
    Проверяет, является ли callback данные связанными со странами/нодами.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        True если это callback стран/нод
    """
    if not callback_data:
        return False
    
    country_keywords = [
        'country', 'countries', 'node', 'nodes', 'select', 'toggle',
        'auto_select', 'preferences', 'protocol', 'favorite', 'stats',
        'speed_test', 'confirm', 'cancel'
    ]
    
    return any(keyword in callback_data for keyword in country_keywords)


def extract_country_id(callback_data: str) -> Optional[str]:
    """
    Извлекает ID страны из callback данных.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        ID страны или None
    """
    try:
        parsed = parse_country_callback(callback_data)
        if hasattr(parsed, 'country_id'):
            return parsed.country_id
        return None
    except:
        return None


def extract_node_id(callback_data: str) -> Optional[str]:
    """
    Извлекает ID ноды из callback данных.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        ID ноды или None
    """
    try:
        parsed = parse_country_callback(callback_data)
        if hasattr(parsed, 'node_id'):
            return parsed.node_id
        return None
    except:
        return None


# Функции для быстрой валидации

def is_valid_country_callback(callback_data: str) -> bool:
    """Проверяет, валиден ли callback стран."""
    try:
        parse_country_callback(callback_data)
        return True
    except ValidationError:
        return False


def validate_country_id_format(country_id: str) -> bool:
    """Проверяет формат ID страны."""
    return bool(re.match(r'^[A-Z]{2}$', country_id)) if country_id else False


def validate_node_id_format(node_id: str) -> bool:
    """Проверяет формат ID ноды."""
    return bool(re.match(r'^[a-z0-9-]{3,50}$', node_id)) if node_id else False
