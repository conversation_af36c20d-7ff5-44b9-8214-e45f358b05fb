"""
Схемы валидации для webhook данных от платежных систем.
"""

from typing import Optional, Dict, Any, Literal
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum


class WebhookValidationError(Exception):
    """Исключение для ошибок валидации webhook данных."""
    pass


class YooKassaPaymentStatus(str, Enum):
    """Статусы платежей YooKassa."""
    PENDING = "pending"
    WAITING_FOR_CAPTURE = "waiting_for_capture"
    SUCCEEDED = "succeeded"
    CANCELED = "canceled"


class YooKassaAmount(BaseModel):
    """Модель суммы платежа YooKassa."""
    value: str = Field(..., description="Сумма платежа")
    currency: str = Field(..., description="Валюта платежа")
    
    @field_validator('value')
    @classmethod
    def validate_amount(cls, v):
        try:
            amount = Decimal(v)
            if amount <= 0:
                raise ValueError('Amount must be positive')
            return v
        except (ValueError, TypeError):
            raise ValueError('Invalid amount format')

    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v):
        allowed_currencies = ['RUB', 'USD', 'EUR']
        if v not in allowed_currencies:
            raise ValueError(f'Currency must be one of {allowed_currencies}')
        return v


class YooKassaConfirmation(BaseModel):
    """Модель подтверждения платежа YooKassa."""
    type: str = Field(..., description="Тип подтверждения")
    confirmation_url: Optional[str] = Field(None, description="URL для подтверждения")
    return_url: Optional[str] = Field(None, description="URL возврата")


class YooKassaPaymentMethod(BaseModel):
    """Модель метода платежа YooKassa."""
    type: str = Field(..., description="Тип платежного метода")
    id: str = Field(..., description="Идентификатор метода")
    saved: bool = Field(False, description="Сохранен ли метод")
    title: Optional[str] = Field(None, description="Название метода")


class YooKassaPaymentObject(BaseModel):
    """Модель объекта платежа YooKassa."""
    id: str = Field(..., description="Идентификатор платежа")
    status: YooKassaPaymentStatus = Field(..., description="Статус платежа")
    amount: YooKassaAmount = Field(..., description="Сумма платежа")
    description: Optional[str] = Field(None, description="Описание платежа")
    recipient: Optional[Dict[str, Any]] = Field(None, description="Получатель")
    payment_method: Optional[YooKassaPaymentMethod] = Field(None, description="Метод платежа")
    captured_at: Optional[str] = Field(None, description="Время захвата")
    created_at: str = Field(..., description="Время создания")
    expires_at: Optional[str] = Field(None, description="Время истечения")
    confirmation: Optional[YooKassaConfirmation] = Field(None, description="Подтверждение")
    test: bool = Field(False, description="Тестовый платеж")
    refunded_amount: Optional[YooKassaAmount] = Field(None, description="Возвращенная сумма")
    paid: bool = Field(False, description="Оплачен ли")
    refundable: bool = Field(True, description="Можно ли вернуть")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Метаданные")
    
    @field_validator('id')
    @classmethod
    def validate_payment_id(cls, v):
        if not v or len(v) < 10:
            raise ValueError('Invalid payment ID')
        return v

    @field_validator('created_at', 'captured_at', 'expires_at', mode='before')
    @classmethod
    def validate_datetime(cls, v):
        if v is None:
            return v
        try:
            # Проверяем формат ISO datetime
            datetime.fromisoformat(v.replace('Z', '+00:00'))
            return v
        except (ValueError, AttributeError):
            raise ValueError('Invalid datetime format')


class YooKassaWebhook(BaseModel):
    """Модель webhook уведомления от YooKassa."""
    type: Literal["notification"] = Field(..., description="Тип уведомления")
    event: str = Field(..., description="Событие")
    object: YooKassaPaymentObject = Field(..., description="Объект платежа")
    
    @field_validator('event')
    @classmethod
    def validate_event(cls, v):
        allowed_events = [
            'payment.succeeded',
            'payment.canceled',
            'payment.waiting_for_capture',
            'refund.succeeded'
        ]
        if v not in allowed_events:
            raise ValueError(f'Event must be one of {allowed_events}')
        return v


class CryptomusPaymentStatus(str, Enum):
    """Статусы платежей Cryptomus."""
    PAID = "paid"
    PAID_OVER = "paid_over"
    FAIL = "fail"
    CANCEL = "cancel"
    SYSTEM_FAIL = "system_fail"
    REFUND_PROCESS = "refund_process"
    REFUND_FAIL = "refund_fail"
    REFUND_PAID = "refund_paid"
    LOCKED = "locked"


class CryptomusWebhook(BaseModel):
    """Модель webhook уведомления от Cryptomus."""
    uuid: str = Field(..., description="UUID платежа")
    order_id: str = Field(..., description="ID заказа")
    amount: str = Field(..., description="Сумма платежа")
    payment_amount: Optional[str] = Field(None, description="Сумма к оплате")
    payer_amount: Optional[str] = Field(None, description="Сумма плательщика")
    discount_percent: Optional[str] = Field(None, description="Процент скидки")
    currency: str = Field(..., description="Валюта")
    payer_currency: Optional[str] = Field(None, description="Валюта плательщика")
    network: Optional[str] = Field(None, description="Сеть")
    address: Optional[str] = Field(None, description="Адрес")
    from_address: Optional[str] = Field(None, description="Адрес отправителя")
    txid: Optional[str] = Field(None, description="ID транзакции")
    payment_status: CryptomusPaymentStatus = Field(..., alias="status", description="Статус платежа")
    url: Optional[str] = Field(None, description="URL платежа")
    expired_at: Optional[int] = Field(None, description="Время истечения")
    status: CryptomusPaymentStatus = Field(..., description="Статус платежа")
    is_final: bool = Field(False, description="Финальный статус")
    additional_data: Optional[str] = Field(None, description="Дополнительные данные")
    currencies: Optional[Dict[str, Any]] = Field(None, description="Валюты")
    merchant_amount: Optional[str] = Field(None, description="Сумма мерчанта")
    sign: str = Field(..., description="Подпись")
    
    @validator('uuid', 'order_id')
    def validate_ids(cls, v):
        if not v or len(v) < 5:
            raise ValueError('Invalid ID format')
        return v
    
    @validator('amount', 'payment_amount', 'payer_amount', 'merchant_amount', pre=True)
    def validate_amounts(cls, v):
        if v is None:
            return v
        try:
            amount = Decimal(str(v))
            if amount < 0:
                raise ValueError('Amount cannot be negative')
            return str(amount)
        except (ValueError, TypeError):
            raise ValueError('Invalid amount format')
    
    @validator('currency', 'payer_currency')
    def validate_currency(cls, v):
        if v is None:
            return v
        # Основные криптовалюты и фиатные валюты
        allowed_currencies = [
            'USD', 'EUR', 'RUB', 'BTC', 'ETH', 'USDT', 'USDC', 
            'LTC', 'BCH', 'XRP', 'ADA', 'DOT', 'LINK', 'UNI'
        ]
        if v.upper() not in allowed_currencies:
            # Логируем неизвестную валюту, но не блокируем
            pass
        return v.upper()
    
    @validator('sign')
    def validate_sign(cls, v):
        if not v or len(v) != 32:  # MD5 hash length
            raise ValueError('Invalid signature format')
        return v
    
    @root_validator
    def validate_webhook_data(cls, values):
        """Дополнительная валидация целостности данных."""
        status = values.get('status')
        payment_status = values.get('payment_status')
        
        # Проверяем соответствие статусов
        if status and payment_status and status != payment_status:
            raise ValueError('Status fields mismatch')
        
        # Для успешных платежей должны быть указаны суммы
        if status in [CryptomusPaymentStatus.PAID, CryptomusPaymentStatus.PAID_OVER]:
            if not values.get('amount'):
                raise ValueError('Amount is required for paid status')
        
        return values


class TelegramStarsWebhook(BaseModel):
    """Модель для обработки платежей Telegram Stars."""
    invoice_payload: str = Field(..., description="Payload инвойса")
    currency: Literal["XTR"] = Field(..., description="Валюта (всегда XTR)")
    total_amount: int = Field(..., description="Общая сумма в звездах")
    telegram_payment_charge_id: str = Field(..., description="ID платежа Telegram")
    provider_payment_charge_id: str = Field(..., description="ID платежа провайдера")
    
    @validator('total_amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be positive')
        if v > 2500:  # Максимум для Telegram Stars
            raise ValueError('Amount exceeds maximum limit')
        return v
    
    @validator('invoice_payload')
    def validate_payload(cls, v):
        if not v or len(v) < 3:
            raise ValueError('Invalid invoice payload')
        return v


# Утилитарные функции для валидации

def validate_yookassa_webhook(data: Dict[str, Any]) -> YooKassaWebhook:
    """
    Валидирует webhook данные от YooKassa.
    
    Args:
        data: Словарь с данными webhook
        
    Returns:
        Валидированный объект YooKassaWebhook
        
    Raises:
        WebhookValidationError: При ошибке валидации
    """
    try:
        return YooKassaWebhook(**data)
    except Exception as e:
        raise WebhookValidationError(f"YooKassa webhook validation failed: {str(e)}")


def validate_cryptomus_webhook(data: Dict[str, Any]) -> CryptomusWebhook:
    """
    Валидирует webhook данные от Cryptomus.
    
    Args:
        data: Словарь с данными webhook
        
    Returns:
        Валидированный объект CryptomusWebhook
        
    Raises:
        WebhookValidationError: При ошибке валидации
    """
    try:
        return CryptomusWebhook(**data)
    except Exception as e:
        raise WebhookValidationError(f"Cryptomus webhook validation failed: {str(e)}")


def validate_telegram_stars_payment(data: Dict[str, Any]) -> TelegramStarsWebhook:
    """
    Валидирует данные платежа Telegram Stars.
    
    Args:
        data: Словарь с данными платежа
        
    Returns:
        Валидированный объект TelegramStarsWebhook
        
    Raises:
        WebhookValidationError: При ошибке валидации
    """
    try:
        return TelegramStarsWebhook(**data)
    except Exception as e:
        raise WebhookValidationError(f"Telegram Stars payment validation failed: {str(e)}")
