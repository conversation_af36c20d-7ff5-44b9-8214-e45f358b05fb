"""
Модуль схем валидации для VPN бота.
Содержит Pydantic модели для валидации входящих данных.
"""

from .webhooks_simple import (
    YooKassaWebhook,
    YooKassaPaymentObject,
    CryptomusWebhook,
    WebhookValidationError,
    validate_yookassa_webhook,
    validate_cryptomus_webhook
)

from .user_input_simple import (
    CallbackData,
    PaymentCallbackData,
    UserCommand,
    TelegramUserData,
    ValidationError,
    parse_callback_data,
    validate_user_input
)

__all__ = [
    # Webhook schemas
    'YooKassaWebhook',
    'YooKassaPaymentObject',
    'CryptomusWebhook',
    'WebhookValidationError',
    'validate_yookassa_webhook',
    'validate_cryptomus_webhook',

    # User input schemas
    'CallbackData',
    'PaymentCallbackData',
    'UserCommand',
    'TelegramUserData',
    'ValidationError',
    'parse_callback_data',
    'validate_user_input'
]
