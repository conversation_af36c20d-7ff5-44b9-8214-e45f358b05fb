"""
Схемы валидации для платежных данных и товаров.
"""

from typing import Dict, List, Optional, Union
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from enum import Enum


class Currency(str, Enum):
    """Поддерживаемые валюты."""
    RUB = "RUB"
    USD = "USD"
    EUR = "EUR"
    XTR = "XTR"  # Telegram Stars


class PaymentStatus(str, Enum):
    """Статусы платежей."""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELED = "canceled"
    REFUNDED = "refunded"


class ProductPrice(BaseModel):
    """Модель цены товара в разных валютах."""
    ru: Union[int, float] = Field(..., description="Цена в рублях")
    en: Union[int, float] = Field(..., description="Цена в долларах")
    stars: int = Field(..., description="Цена в Telegram Stars")
    
    @validator('ru', 'en')
    def validate_fiat_price(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        if v > 1000000:  # Максимальная цена
            raise ValueError('Price exceeds maximum limit')
        return float(v)
    
    @validator('stars')
    def validate_stars_price(cls, v):
        if v <= 0:
            raise ValueError('Stars price must be positive')
        if v > 2500:  # Максимум для Telegram Stars
            raise ValueError('Stars price exceeds maximum limit')
        return v


class Product(BaseModel):
    """Модель товара (подписки)."""
    title: str = Field(..., description="Название товара")
    price: ProductPrice = Field(..., description="Цены в разных валютах")
    callback: str = Field(..., description="Callback для идентификации")
    months: int = Field(..., description="Количество месяцев подписки")
    description: Optional[str] = Field(None, description="Описание товара")
    
    @validator('title')
    def validate_title(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Product title cannot be empty')
        if len(v) > 100:
            raise ValueError('Product title too long')
        return v.strip()
    
    @validator('callback')
    def validate_callback(cls, v):
        if not v:
            raise ValueError('Product callback is required')
        # Callback должен содержать только безопасные символы
        import re
        if not re.match(r'^[a-z_]+$', v):
            raise ValueError('Invalid callback format')
        return v
    
    @validator('months')
    def validate_months(cls, v):
        if v <= 0:
            raise ValueError('Months must be positive')
        if v > 120:  # Максимум 10 лет
            raise ValueError('Months exceeds maximum limit')
        return v
    
    @validator('description')
    def validate_description(cls, v):
        if v is not None:
            if len(v) > 500:
                raise ValueError('Description too long')
            return v.strip() if v.strip() else None
        return v


class PaymentData(BaseModel):
    """Базовая модель платежных данных."""
    user_id: int = Field(..., description="ID пользователя")
    product_callback: str = Field(..., description="Callback товара")
    amount: Decimal = Field(..., description="Сумма платежа")
    currency: Currency = Field(..., description="Валюта платежа")
    chat_id: int = Field(..., description="ID чата")
    language_code: str = Field(default="en", description="Код языка")
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if v <= 0:
            raise ValueError('User ID must be positive')
        return v
    
    @validator('product_callback')
    def validate_product_callback(cls, v):
        if not v:
            raise ValueError('Product callback is required')
        import re
        if not re.match(r'^[a-z_]+$', v):
            raise ValueError('Invalid product callback format')
        return v
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be positive')
        return v
    
    @validator('chat_id')
    def validate_chat_id(cls, v):
        if abs(v) > 2**63 - 1:
            raise ValueError('Chat ID exceeds maximum value')
        return v
    
    @validator('language_code')
    def validate_language_code(cls, v):
        import re
        if not re.match(r'^[a-z]{2}(-[A-Z]{2})?$', v):
            raise ValueError('Invalid language code format')
        return v


class YooKassaPaymentData(PaymentData):
    """Модель данных для создания платежа YooKassa."""
    currency: Currency = Field(default=Currency.RUB, description="Валюта (только RUB)")
    return_url: Optional[str] = Field(None, description="URL возврата")
    save_payment_method: bool = Field(False, description="Сохранить метод оплаты")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v != Currency.RUB:
            raise ValueError('YooKassa supports only RUB currency')
        return v
    
    @validator('return_url')
    def validate_return_url(cls, v):
        if v is not None:
            import re
            if not re.match(r'^https?://.+', v):
                raise ValueError('Invalid return URL format')
        return v


class CryptomusPaymentData(PaymentData):
    """Модель данных для создания платежа Cryptomus."""
    currency: Currency = Field(default=Currency.USD, description="Валюта (только USD)")
    lifetime: int = Field(default=1800, description="Время жизни платежа в секундах")
    callback_url: Optional[str] = Field(None, description="URL для webhook")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v != Currency.USD:
            raise ValueError('Cryptomus supports only USD currency')
        return v
    
    @validator('lifetime')
    def validate_lifetime(cls, v):
        if v < 300:  # Минимум 5 минут
            raise ValueError('Lifetime too short')
        if v > 86400:  # Максимум 24 часа
            raise ValueError('Lifetime too long')
        return v
    
    @validator('callback_url')
    def validate_callback_url(cls, v):
        if v is not None:
            import re
            if not re.match(r'^https://.+', v):
                raise ValueError('Callback URL must use HTTPS')
        return v


class TelegramStarsPaymentData(PaymentData):
    """Модель данных для создания платежа Telegram Stars."""
    currency: Currency = Field(default=Currency.XTR, description="Валюта (только XTR)")
    title: str = Field(..., description="Заголовок инвойса")
    description: str = Field(..., description="Описание инвойса")
    payload: str = Field(..., description="Payload инвойса")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v != Currency.XTR:
            raise ValueError('Telegram Stars supports only XTR currency')
        return v
    
    @validator('amount')
    def validate_stars_amount(cls, v):
        if v <= 0:
            raise ValueError('Stars amount must be positive')
        if v > 2500:
            raise ValueError('Stars amount exceeds maximum limit')
        return v
    
    @validator('title', 'description')
    def validate_text_fields(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Field cannot be empty')
        if len(v) > 100:
            raise ValueError('Field too long')
        return v.strip()
    
    @validator('payload')
    def validate_payload(cls, v):
        if not v:
            raise ValueError('Payload is required')
        if len(v) > 128:
            raise ValueError('Payload too long')
        return v


class PaymentResult(BaseModel):
    """Модель результата создания платежа."""
    payment_id: str = Field(..., description="ID платежа")
    payment_url: Optional[str] = Field(None, description="URL для оплаты")
    amount: Decimal = Field(..., description="Сумма платежа")
    currency: Currency = Field(..., description="Валюта")
    status: PaymentStatus = Field(..., description="Статус платежа")
    expires_at: Optional[str] = Field(None, description="Время истечения")
    
    @validator('payment_id')
    def validate_payment_id(cls, v):
        if not v or len(v) < 5:
            raise ValueError('Invalid payment ID')
        return v
    
    @validator('payment_url')
    def validate_payment_url(cls, v):
        if v is not None:
            import re
            if not re.match(r'^https?://.+', v):
                raise ValueError('Invalid payment URL format')
        return v


class ProductCatalog(BaseModel):
    """Модель каталога товаров."""
    products: List[Product] = Field(..., description="Список товаров")
    
    @validator('products')
    def validate_products(cls, v):
        if not v:
            raise ValueError('Product catalog cannot be empty')
        
        # Проверяем уникальность callback'ов
        callbacks = [product.callback for product in v]
        if len(callbacks) != len(set(callbacks)):
            raise ValueError('Product callbacks must be unique')
        
        return v
    
    def get_product(self, callback: str) -> Optional[Product]:
        """Получает товар по callback."""
        for product in self.products:
            if product.callback == callback:
                return product
        return None
    
    def get_callbacks(self) -> List[str]:
        """Возвращает список всех callback'ов."""
        return [product.callback for product in self.products]


# Утилитарные функции

def validate_product_catalog(data: List[Dict]) -> ProductCatalog:
    """
    Валидирует каталог товаров из JSON данных.
    
    Args:
        data: Список словарей с данными товаров
        
    Returns:
        Валидированный каталог товаров
        
    Raises:
        ValueError: При ошибке валидации
    """
    try:
        products = [Product(**item) for item in data]
        return ProductCatalog(products=products)
    except Exception as e:
        raise ValueError(f"Product catalog validation failed: {str(e)}")


def create_payment_data(
    user_id: int,
    product_callback: str,
    payment_method: str,
    chat_id: int,
    language_code: str = "en"
) -> Union[YooKassaPaymentData, CryptomusPaymentData, TelegramStarsPaymentData]:
    """
    Создает данные для платежа на основе метода оплаты.
    
    Args:
        user_id: ID пользователя
        product_callback: Callback товара
        payment_method: Метод оплаты
        chat_id: ID чата
        language_code: Код языка
        
    Returns:
        Объект данных для платежа
        
    Raises:
        ValueError: При неизвестном методе оплаты
    """
    # Здесь должна быть логика получения товара и его цены
    # Для примера используем фиксированные значения
    
    base_data = {
        "user_id": user_id,
        "product_callback": product_callback,
        "chat_id": chat_id,
        "language_code": language_code
    }
    
    if payment_method == "yookassa":
        return YooKassaPaymentData(
            **base_data,
            amount=Decimal("100.00"),
            currency=Currency.RUB
        )
    elif payment_method == "cryptomus":
        return CryptomusPaymentData(
            **base_data,
            amount=Decimal("1.00"),
            currency=Currency.USD
        )
    elif payment_method == "telegram_stars":
        return TelegramStarsPaymentData(
            **base_data,
            amount=Decimal("50"),
            currency=Currency.XTR,
            title="VPN Subscription",
            description="Monthly VPN subscription",
            payload=product_callback
        )
    else:
        raise ValueError(f"Unknown payment method: {payment_method}")
