"""
Упрощенные схемы валидации для webhook данных от платежных систем.
Совместимы с Pydantic v2.
"""

from typing import Optional, Dict, Any, Literal
from decimal import Decimal
from pydantic import BaseModel, Field
from enum import Enum


class WebhookValidationError(Exception):
    """Исключение для ошибок валидации webhook данных."""
    pass


class YooKassaPaymentStatus(str, Enum):
    """Статусы платежей YooKassa."""
    PENDING = "pending"
    WAITING_FOR_CAPTURE = "waiting_for_capture"
    SUCCEEDED = "succeeded"
    CANCELED = "canceled"


class YooKassaAmount(BaseModel):
    """Модель суммы платежа YooKassa."""
    value: str = Field(..., description="Сумма платежа")
    currency: str = Field(..., description="Валюта платежа")


class YooKassaPaymentObject(BaseModel):
    """Модель объекта платежа YooKassa."""
    id: str = Field(..., description="Идентификатор платежа")
    status: str = Field(..., description="Статус платежа")
    amount: YooKassaAmount = Field(..., description="Сумма платежа")
    description: Optional[str] = Field(None, description="Описание платежа")
    created_at: str = Field(..., description="Время создания")
    paid: bool = Field(False, description="Оплачен ли")
    test: bool = Field(False, description="Тестовый платеж")


class YooKassaWebhook(BaseModel):
    """Модель webhook уведомления от YooKassa."""
    type: str = Field(..., description="Тип уведомления")
    event: str = Field(..., description="Событие")
    object: YooKassaPaymentObject = Field(..., description="Объект платежа")


class CryptomusPaymentStatus(str, Enum):
    """Статусы платежей Cryptomus."""
    PAID = "paid"
    PAID_OVER = "paid_over"
    FAIL = "fail"
    CANCEL = "cancel"
    SYSTEM_FAIL = "system_fail"


class CryptomusWebhook(BaseModel):
    """Модель webhook уведомления от Cryptomus."""
    uuid: str = Field(..., description="UUID платежа")
    order_id: str = Field(..., description="ID заказа")
    amount: str = Field(..., description="Сумма платежа")
    currency: str = Field(..., description="Валюта")
    status: str = Field(..., description="Статус платежа")
    sign: str = Field(..., description="Подпись")


class TelegramStarsWebhook(BaseModel):
    """Модель для обработки платежей Telegram Stars."""
    invoice_payload: str = Field(..., description="Payload инвойса")
    currency: str = Field(..., description="Валюта")
    total_amount: int = Field(..., description="Общая сумма в звездах")
    telegram_payment_charge_id: str = Field(..., description="ID платежа Telegram")
    provider_payment_charge_id: str = Field(..., description="ID платежа провайдера")


def validate_yookassa_webhook(data: Dict[str, Any]) -> YooKassaWebhook:
    """
    Валидирует webhook данные от YooKassa.
    
    Args:
        data: Словарь с данными webhook
        
    Returns:
        Валидированный объект YooKassaWebhook
        
    Raises:
        WebhookValidationError: При ошибке валидации
    """
    try:
        webhook = YooKassaWebhook(**data)
        
        # Дополнительная валидация
        if webhook.type != "notification":
            raise ValueError("Invalid notification type")
        
        allowed_events = [
            'payment.succeeded',
            'payment.canceled',
            'payment.waiting_for_capture',
            'refund.succeeded'
        ]
        if webhook.event not in allowed_events:
            raise ValueError(f'Event must be one of {allowed_events}')
        
        # Валидация ID платежа
        if not webhook.object.id or len(webhook.object.id) < 10:
            raise ValueError('Invalid payment ID')
        
        # Валидация суммы
        try:
            amount = Decimal(webhook.object.amount.value)
            if amount <= 0:
                raise ValueError('Amount must be positive')
        except (ValueError, TypeError):
            raise ValueError('Invalid amount format')
        
        # Валидация валюты
        allowed_currencies = ['RUB', 'USD', 'EUR']
        if webhook.object.amount.currency not in allowed_currencies:
            raise ValueError(f'Currency must be one of {allowed_currencies}')
        
        return webhook
        
    except Exception as e:
        raise WebhookValidationError(f"YooKassa webhook validation failed: {str(e)}")


def validate_cryptomus_webhook(data: Dict[str, Any]) -> CryptomusWebhook:
    """
    Валидирует webhook данные от Cryptomus.
    
    Args:
        data: Словарь с данными webhook
        
    Returns:
        Валидированный объект CryptomusWebhook
        
    Raises:
        WebhookValidationError: При ошибке валидации
    """
    try:
        webhook = CryptomusWebhook(**data)
        
        # Дополнительная валидация
        if not webhook.uuid or len(webhook.uuid) < 5:
            raise ValueError('Invalid UUID format')
        
        if not webhook.order_id or len(webhook.order_id) < 5:
            raise ValueError('Invalid order ID format')
        
        # Валидация суммы
        try:
            amount = Decimal(webhook.amount)
            if amount < 0:
                raise ValueError('Amount cannot be negative')
        except (ValueError, TypeError):
            raise ValueError('Invalid amount format')
        
        # Валидация статуса
        allowed_statuses = [
            'paid', 'paid_over', 'fail', 'cancel', 'system_fail',
            'refund_process', 'refund_fail', 'refund_paid', 'locked'
        ]
        if webhook.status not in allowed_statuses:
            raise ValueError(f'Status must be one of {allowed_statuses}')
        
        # Валидация подписи
        if not webhook.sign or len(webhook.sign) != 32:
            raise ValueError('Invalid signature format')
        
        return webhook
        
    except Exception as e:
        raise WebhookValidationError(f"Cryptomus webhook validation failed: {str(e)}")


def validate_telegram_stars_payment(data: Dict[str, Any]) -> TelegramStarsWebhook:
    """
    Валидирует данные платежа Telegram Stars.
    
    Args:
        data: Словарь с данными платежа
        
    Returns:
        Валидированный объект TelegramStarsWebhook
        
    Raises:
        WebhookValidationError: При ошибке валидации
    """
    try:
        webhook = TelegramStarsWebhook(**data)
        
        # Дополнительная валидация
        if webhook.currency != "XTR":
            raise ValueError('Currency must be XTR for Telegram Stars')
        
        if webhook.total_amount <= 0:
            raise ValueError('Amount must be positive')
        
        if webhook.total_amount > 2500:
            raise ValueError('Amount exceeds maximum limit')
        
        if not webhook.invoice_payload or len(webhook.invoice_payload) < 3:
            raise ValueError('Invalid invoice payload')
        
        return webhook
        
    except Exception as e:
        raise WebhookValidationError(f"Telegram Stars payment validation failed: {str(e)}")


# Функции для быстрой валидации без создания объектов

def is_valid_yookassa_webhook(data: Dict[str, Any]) -> bool:
    """Проверяет, валидны ли данные YooKassa webhook."""
    try:
        validate_yookassa_webhook(data)
        return True
    except WebhookValidationError:
        return False


def is_valid_cryptomus_webhook(data: Dict[str, Any]) -> bool:
    """Проверяет, валидны ли данные Cryptomus webhook."""
    try:
        validate_cryptomus_webhook(data)
        return True
    except WebhookValidationError:
        return False


def is_valid_telegram_stars_payment(data: Dict[str, Any]) -> bool:
    """Проверяет, валидны ли данные Telegram Stars платежа."""
    try:
        validate_telegram_stars_payment(data)
        return True
    except WebhookValidationError:
        return False
