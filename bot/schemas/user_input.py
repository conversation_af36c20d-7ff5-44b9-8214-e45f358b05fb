"""
Схемы валидации для пользовательского ввода в Telegram боте.
"""

from typing import Optional, Literal, Union
from pydantic import BaseModel, Field, validator
from enum import Enum
import re


class ValidationError(Exception):
    """Исключение для ошибок валидации пользовательского ввода."""
    pass


class PaymentMethod(str, Enum):
    """Доступные методы оплаты."""
    YOOKASSA = "kassa"
    CRYPTOMUS = "crypto"
    TELEGRAM_STARS = "stars"


class CallbackAction(str, Enum):
    """Типы действий в callback данных."""
    PAY = "pay"
    BACK = "back"
    CANCEL = "cancel"
    CONFIRM = "confirm"
    SELECT = "select"


class TelegramUserData(BaseModel):
    """Модель данных пользователя Telegram."""
    id: int = Field(..., description="ID пользователя Telegram")
    first_name: str = Field(..., description="Имя пользователя")
    last_name: Optional[str] = Field(None, description="Фамилия пользователя")
    username: Optional[str] = Field(None, description="Username пользователя")
    language_code: Optional[str] = Field(None, description="Код языка")
    is_bot: bool = Field(False, description="Является ли ботом")
    
    @validator('id')
    def validate_user_id(cls, v):
        if v <= 0:
            raise ValueError('User ID must be positive')
        if v > 2**63 - 1:  # Максимальное значение для int64
            raise ValueError('User ID exceeds maximum value')
        return v
    
    @validator('first_name')
    def validate_first_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('First name cannot be empty')
        if len(v) > 64:
            raise ValueError('First name too long')
        return v.strip()
    
    @validator('last_name', 'username')
    def validate_optional_strings(cls, v):
        if v is not None:
            if len(v) > 64:
                raise ValueError('String too long')
            return v.strip() if v.strip() else None
        return v
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            # Username должен соответствовать правилам Telegram
            if not re.match(r'^[a-zA-Z0-9_]{5,32}$', v):
                raise ValueError('Invalid username format')
        return v
    
    @validator('language_code')
    def validate_language_code(cls, v):
        if v is not None:
            # Проверяем формат языкового кода (ISO 639-1)
            if not re.match(r'^[a-z]{2}(-[A-Z]{2})?$', v):
                raise ValueError('Invalid language code format')
        return v


class CallbackData(BaseModel):
    """Базовая модель для callback данных."""
    action: str = Field(..., description="Действие")
    data: Optional[str] = Field(None, description="Дополнительные данные")
    
    @validator('action')
    def validate_action(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Action cannot be empty')
        if len(v) > 32:
            raise ValueError('Action too long')
        # Проверяем на безопасные символы
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Action contains invalid characters')
        return v.strip()
    
    @validator('data')
    def validate_data(cls, v):
        if v is not None:
            if len(v) > 64:
                raise ValueError('Data too long')
            # Проверяем на безопасные символы
            if not re.match(r'^[a-zA-Z0-9_.-]+$', v):
                raise ValueError('Data contains invalid characters')
            return v.strip()
        return v


class PaymentCallbackData(CallbackData):
    """Модель для callback данных платежей."""
    action: Literal["pay_kassa", "pay_crypto", "pay_stars"] = Field(..., description="Тип платежа")
    product_callback: str = Field(..., alias="data", description="Callback товара")
    
    @validator('product_callback')
    def validate_product_callback(cls, v):
        if not v:
            raise ValueError('Product callback is required')
        # Проверяем, что callback соответствует формату товаров
        if not re.match(r'^[a-z_]+$', v):
            raise ValueError('Invalid product callback format')
        return v
    
    @property
    def payment_method(self) -> PaymentMethod:
        """Возвращает метод оплаты на основе action."""
        if self.action == "pay_kassa":
            return PaymentMethod.YOOKASSA
        elif self.action == "pay_crypto":
            return PaymentMethod.CRYPTOMUS
        elif self.action == "pay_stars":
            return PaymentMethod.TELEGRAM_STARS
        else:
            raise ValueError(f"Unknown payment action: {self.action}")


class UserCommand(BaseModel):
    """Модель для команд пользователя."""
    command: str = Field(..., description="Команда")
    args: Optional[str] = Field(None, description="Аргументы команды")
    user: TelegramUserData = Field(..., description="Данные пользователя")
    chat_id: int = Field(..., description="ID чата")
    
    @validator('command')
    def validate_command(cls, v):
        if not v:
            raise ValueError('Command cannot be empty')
        # Команды должны начинаться с /
        if not v.startswith('/'):
            v = '/' + v
        # Проверяем формат команды
        if not re.match(r'^/[a-zA-Z0-9_]+$', v):
            raise ValueError('Invalid command format')
        return v.lower()
    
    @validator('args')
    def validate_args(cls, v):
        if v is not None:
            if len(v) > 256:
                raise ValueError('Command arguments too long')
            # Базовая проверка на безопасность
            if re.search(r'[<>"\']', v):
                raise ValueError('Command arguments contain unsafe characters')
            return v.strip()
        return v
    
    @validator('chat_id')
    def validate_chat_id(cls, v):
        if abs(v) > 2**63 - 1:
            raise ValueError('Chat ID exceeds maximum value')
        return v


class MessageText(BaseModel):
    """Модель для текстовых сообщений."""
    text: str = Field(..., description="Текст сообщения")
    user: TelegramUserData = Field(..., description="Данные пользователя")
    chat_id: int = Field(..., description="ID чата")
    
    @validator('text')
    def validate_text(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Message text cannot be empty')
        if len(v) > 4096:  # Максимальная длина сообщения в Telegram
            raise ValueError('Message text too long')
        return v.strip()
    
    @validator('chat_id')
    def validate_chat_id(cls, v):
        if abs(v) > 2**63 - 1:
            raise ValueError('Chat ID exceeds maximum value')
        return v


class ProductSelection(BaseModel):
    """Модель для выбора товара."""
    callback: str = Field(..., description="Callback товара")
    user_id: int = Field(..., description="ID пользователя")
    
    @validator('callback')
    def validate_callback(cls, v):
        if not v:
            raise ValueError('Product callback is required')
        # Проверяем формат callback товара
        if not re.match(r'^[a-z_]+$', v):
            raise ValueError('Invalid product callback format')
        return v
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if v <= 0:
            raise ValueError('User ID must be positive')
        return v


class SubscriptionRequest(BaseModel):
    """Модель для запроса подписки."""
    user_id: int = Field(..., description="ID пользователя")
    product_callback: str = Field(..., description="Callback товара")
    payment_method: PaymentMethod = Field(..., description="Метод оплаты")
    
    @validator('user_id')
    def validate_user_id(cls, v):
        if v <= 0:
            raise ValueError('User ID must be positive')
        return v
    
    @validator('product_callback')
    def validate_product_callback(cls, v):
        if not v:
            raise ValueError('Product callback is required')
        if not re.match(r'^[a-z_]+$', v):
            raise ValueError('Invalid product callback format')
        return v


# Утилитарные функции для валидации

def parse_callback_data(callback_data: str) -> CallbackData:
    """
    Парсит и валидирует callback данные.
    
    Args:
        callback_data: Строка callback данных
        
    Returns:
        Валидированный объект CallbackData
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        if not callback_data:
            raise ValueError("Callback data is empty")
        
        parts = callback_data.split('_', 1)
        action = parts[0]
        data = parts[1] if len(parts) > 1 else None
        
        # Проверяем, является ли это платежным callback
        if action.startswith('pay_'):
            if not data:
                raise ValueError("Payment callback requires product data")
            return PaymentCallbackData(action=callback_data, data=data)
        
        return CallbackData(action=action, data=data)
        
    except Exception as e:
        raise ValidationError(f"Invalid callback data: {str(e)}")


def validate_user_input(text: str, user_data: dict, chat_id: int) -> Union[UserCommand, MessageText]:
    """
    Валидирует пользовательский ввод.
    
    Args:
        text: Текст сообщения
        user_data: Данные пользователя
        chat_id: ID чата
        
    Returns:
        Валидированный объект UserCommand или MessageText
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        user = TelegramUserData(**user_data)
        
        # Проверяем, является ли это командой
        if text.startswith('/'):
            parts = text.split(' ', 1)
            command = parts[0]
            args = parts[1] if len(parts) > 1 else None
            
            return UserCommand(
                command=command,
                args=args,
                user=user,
                chat_id=chat_id
            )
        
        # Обычное текстовое сообщение
        return MessageText(
            text=text,
            user=user,
            chat_id=chat_id
        )
        
    except Exception as e:
        raise ValidationError(f"User input validation failed: {str(e)}")


def validate_product_selection(callback: str, user_id: int) -> ProductSelection:
    """
    Валидирует выбор товара.
    
    Args:
        callback: Callback товара
        user_id: ID пользователя
        
    Returns:
        Валидированный объект ProductSelection
        
    Raises:
        ValidationError: При ошибке валидации
    """
    try:
        return ProductSelection(callback=callback, user_id=user_id)
    except Exception as e:
        raise ValidationError(f"Product selection validation failed: {str(e)}")
