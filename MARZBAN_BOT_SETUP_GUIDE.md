# 🚀 Руководство по настройке VPN бота для Marzban инфраструктуры

## 📋 Обзор конфигурации

Ваш VPN бот настроен для работы с развернутой инфраструктурой Marzban:
- **Основная панель**: `https://panel.snaplyze.me:8000`
- **Ноды с REALITY inbounds**: Порт 12000 для клиентских подключений
- **Протокол**: Только VLESS TCP REALITY (согласно вашей упрощенной конфигурации)
- **Архитектура**: Clean Architecture с Repository Pattern, Service Layer, мониторингом

## 🔧 1. Обязательные настройки .env файла

### 1.1 Критические параметры для изменения

```bash
# 🔐 БЕЗОПАСНОСТЬ - ОБЯЗАТЕЛЬНО ИЗМЕНИТЕ!
ENCRYPTION_KEY=your_32_character_encryption_key_here_change_this
ENCRYPTION_SALT=your_unique_salt_here_change_this

# 🗄️ БАЗА ДАННЫХ - Рекомендуется изменить для продакшена
DB_NAME=unveil_vpn_prod
DB_USER=unveil_user
DB_PASS=unveil_secure_password_2024
DB_ROOT_PASS=root_secure_password_2024

# 🏗️ MARZBAN ПАНЕЛЬ - Проверьте соответствие вашей конфигурации
PANEL_HOST=https://panel.snaplyze.me:8000
PANEL_USER=admin
PANEL_PASS=admin
```

### 1.2 Генерация ключей шифрования

```bash
# Генерация ключа шифрования (32 символа)
python3 -c "import secrets; print(secrets.token_urlsafe(32)[:32])"

# Генерация соли (16 символов)
python3 -c "import secrets; print(secrets.token_urlsafe(16)[:16])"
```

### 1.3 Настройка платежных систем (опционально)

```bash
# YooKassa
YOOKASSA_TOKEN=your_yookassa_token
YOOKASSA_SHOPID=your_shop_id

# Cryptomus
CRYPTO_TOKEN=your_crypto_token
MERCHANT_UUID=your_merchant_uuid
```

## 🌍 2. Настройка стран и нод

### 2.1 Создание стран в базе данных

Выполните скрипт для создания стран:

```bash
cd bot
python3 -c "
import asyncio
from demo.demo_countries_nodes import create_demo_countries_and_nodes
asyncio.run(create_demo_countries_and_nodes())
"
```

### 2.2 Добавление ваших нод в базу данных

Создайте файл `setup_nodes.py`:

```python
import asyncio
from db.database import get_session
from repositories.country_node_repository import CountryRepository, MarzbanNodeRepository
from db.models_countries_nodes import MarzbanNode, NodeStatus

async def setup_your_nodes():
    async with get_session() as session:
        country_repo = CountryRepository(session)
        node_repo = MarzbanNodeRepository(session)
        
        # Получаем Германию (должна быть создана в demo)
        germany = await country_repo.get_by_code('DE')
        
        if germany:
            # Добавляем вашу ноду в Германии
            germany_node = MarzbanNode(
                id='germany-node-1',
                name='Germany Frankfurt #1',
                country_id=germany.id,
                city='Frankfurt',
                host='************',
                port=12000,  # Порт REALITY inbound
                api_port=62050,  # SERVICE_PORT для управления
                max_users=1000,
                current_users=0,
                status=NodeStatus.CONNECTED,
                is_active=True,
                cpu_usage=15.0,
                memory_usage=45.0,
                config={
                    'reality_destination': 'deb.debian.org:443',
                    'reality_server_names': ['deb.debian.org'],
                    'protocols': ['vless']
                }
            )
            
            await node_repo.create(germany_node)
            print(f"Created node: {germany_node.name}")

if __name__ == "__main__":
    asyncio.run(setup_your_nodes())
```

## 🧪 3. Пошаговое тестирование

### 3.1 Проверка подключения к базе данных

```bash
cd bot
python3 test-db-connection.py
```

Ожидаемый результат:
```
✅ Database connection successful
✅ Tables exist: vpnusers, yookassa_payments, crypto_payments
```

### 3.2 Проверка подключения к Marzban API

```bash
cd bot
python3 -c "
import asyncio
from utils.marzban_api import panel

async def test_marzban():
    try:
        token = await panel.get_token()
        print(f'✅ Marzban API connection successful')
        print(f'Token obtained: {token[:20]}...')
        
        # Тест получения пользователей
        users = await panel.get_users(limit=5)
        print(f'✅ Users retrieved: {len(users.get(\"users\", []))}')
        
        # Тест получения нод
        nodes = await panel.get_nodes()
        print(f'✅ Nodes retrieved: {len(nodes)}')
        
    except Exception as e:
        print(f'❌ Marzban API error: {e}')

asyncio.run(test_marzban())
"
```

### 3.3 Тестирование создания пользователя VLESS

```bash
cd bot
python3 -c "
import asyncio
from utils.marzban_api import panel, generate_test_subscription

async def test_user_creation():
    try:
        username = 'test_user_' + str(int(time.time()))
        result = await generate_test_subscription(username)
        print(f'✅ Test user created: {username}')
        print(f'Subscription URL: {result.get(\"subscription_url\", \"N/A\")}')
        
        # Проверяем, что пользователь создался
        user = await panel.get_user(username)
        print(f'✅ User verification successful')
        print(f'Status: {user.get(\"status\")}')
        print(f'Protocols: {list(user.get(\"proxies\", {}).keys())}')
        
    except Exception as e:
        print(f'❌ User creation error: {e}')

import time
asyncio.run(test_user_creation())
"
```

### 3.4 Тестирование системы выбора нод

```bash
cd bot
python3 -c "
import asyncio
from services.marzban_country_node_service import MarzbanCountryNodeService
from services.user_preference_service import UserPreferenceService
from services.node_selection_service import NodeSelectionService, SelectionStrategy
from repositories.country_node_repository import *
from db.database import get_session

async def test_node_selection():
    async with get_session() as session:
        # Создаем сервисы
        country_repo = CountryRepository(session)
        node_repo = MarzbanNodeRepository(session)
        stats_repo = NodeStatisticsRepository(session)
        preference_repo = UserNodePreferenceRepository(session)
        inbound_repo = MarzbanInboundRepository(session)
        
        preference_service = UserPreferenceService(preference_repo, country_repo)
        selection_service = NodeSelectionService(node_repo, stats_repo, preference_service)
        main_service = MarzbanCountryNodeService(
            preference_service, selection_service, country_repo, node_repo, stats_repo
        )
        
        # Тестируем получение стран
        countries = await main_service.get_available_countries_with_nodes()
        print(f'✅ Available countries: {len(countries)}')
        for country in countries:
            print(f'  - {country.country.name_ru} ({country.available_nodes}/{country.total_nodes} nodes)')
        
        # Тестируем выбор ноды
        test_user_id = 12345
        result = await main_service.get_optimal_node_for_user(
            test_user_id, strategy=SelectionStrategy.HYBRID
        )
        
        if result.success:
            print(f'✅ Optimal node selected: {result.selected_node.name}')
            print(f'Strategy: {result.selection_strategy}')
            print(f'Reasons: {result.selection_reasons}')
        else:
            print(f'❌ Node selection failed: {result.selection_reasons}')

asyncio.run(test_node_selection())
"
```

## 🚀 4. Запуск бота

### 4.1 Запуск в режиме разработки

```bash
# Запуск с логированием
cd bot
python3 main.py
```

### 4.2 Запуск через Docker

```bash
# Сборка и запуск
docker-compose up --build -d

# Просмотр логов
docker-compose logs -f bot

# Проверка health checks
curl http://localhost:8000/health
curl http://localhost:8000/health/ready
curl http://localhost:8000/health/live
```

## 📊 5. Мониторинг и диагностика

### 5.1 Health Check endpoints

```bash
# Основной health check
curl http://localhost:8000/health

# Готовность к работе
curl http://localhost:8000/health/ready

# Проверка жизнеспособности
curl http://localhost:8000/health/live

# Метрики Prometheus
curl http://localhost:8000/metrics

# Статистика базы данных
curl http://localhost:8000/api/db/status
curl http://localhost:8000/api/db/performance
curl http://localhost:8000/api/db/cache
```

### 5.2 Мониторинг алертов

```bash
# Активные алерты
curl http://localhost:8000/api/alerts/active

# Статус системы алертов
curl http://localhost:8000/api/alerts/status

# Dashboard алертов
curl http://localhost:8000/alerts
```

### 5.3 Просмотр логов

```bash
# Логи бота
docker-compose logs -f bot

# Фильтрация по уровню
docker-compose logs bot | grep ERROR
docker-compose logs bot | grep WARNING

# Логи базы данных
docker-compose logs -f db
```

## 🔧 6. Команды для диагностики

### 6.1 Проверка состояния сервисов

```bash
# Статус контейнеров
docker-compose ps

# Использование ресурсов
docker stats

# Проверка сети
docker network ls
docker network inspect unveil-vpn_app-network
```

### 6.2 Диагностика базы данных

```bash
# Подключение к базе данных
docker-compose exec db mysql -u unveil_user -p unveil_vpn_prod

# Проверка таблиц
docker-compose exec db mysql -u unveil_user -p -e "SHOW TABLES;" unveil_vpn_prod

# Проверка миграций
docker-compose exec db mysql -u unveil_user -p -e "SELECT * FROM schema_migrations;" unveil_vpn_prod
```

### 6.3 Тестирование Telegram бота

```bash
# Отправка тестового сообщения боту
# 1. Найдите бота в Telegram: @your_bot_username
# 2. Отправьте команду /start
# 3. Проверьте логи на наличие обработки команды

# Проверка webhook (если используется)
curl -X POST https://panel.snaplyze.me:8080/webhook/telegram \
  -H "Content-Type: application/json" \
  -d '{"update_id": 1, "message": {"message_id": 1, "from": {"id": 12345}, "chat": {"id": 12345}, "date": 1234567890, "text": "/start"}}'
```

## ⚠️ 7. Возможные проблемы и решения

### 7.1 Проблемы с подключением к Marzban

```bash
# Проверка доступности панели
curl -v https://panel.snaplyze.me:8000/api/admin/token

# Проверка сертификатов
openssl s_client -connect panel.snaplyze.me:8000 -servername panel.snaplyze.me

# Проверка DNS
nslookup panel.snaplyze.me
```

### 7.2 Проблемы с базой данных

```bash
# Проверка подключения
docker-compose exec bot python3 test-db-connection.py

# Пересоздание базы данных
docker-compose down
docker volume rm unveil-vpn_mysql-data
docker-compose up -d
```

### 7.3 Проблемы с нодами

```bash
# Проверка доступности ноды
telnet ************ 12000
curl -v http://************:62050

# Проверка статуса в панели Marzban
# Зайдите в панель -> Node Settings -> проверьте статус нод
```

## 🎯 8. Следующие шаги

1. **Настройте платежные системы** (YooKassa, Cryptomus)
2. **Добавьте больше нод** в разных странах
3. **Настройте алерты** для мониторинга
4. **Создайте backup стратегию** для базы данных
5. **Настройте SSL сертификаты** для webhook
6. **Добавьте дополнительные страны** и ноды

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи: `docker-compose logs -f bot`
2. Проверьте health checks: `curl http://localhost:8000/health`
3. Проверьте подключение к Marzban API
4. Проверьте состояние базы данных

Ваш VPN бот готов к работе с инфраструктурой Marzban! 🚀
