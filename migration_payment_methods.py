"""
Миграция для создания таблиц управления платежными методами.

Revision ID: payment_methods_system
Revises: 0a5510ae2872
Create Date: 2024-01-15 12:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers
revision = 'payment_methods_system'
down_revision = '0a5510ae2872'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Создание таблиц для системы управления платежными методами."""
    
    # Создание таблицы saved_payment_methods
    op.create_table(
        'saved_payment_methods',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, nullable=False, index=True),
        sa.Column('payment_type', sa.Enum('yookassa', 'cryptomus', 'telegram_stars', name='paymentmethodtype'), nullable=False),
        sa.Column('status', sa.Enum('active', 'inactive', 'expired', 'blocked', name='paymentmethodstatus'), default='active'),
        sa.Column('encrypted_data', sa.Text, nullable=False),
        sa.Column('data_hash', sa.String(128), nullable=False, unique=True),
        sa.Column('display_name', sa.String(100)),
        sa.Column('masked_data', sa.String(50)),
        sa.Column('is_default', sa.Boolean, default=False),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('provider_payment_method_id', sa.String(100)),
        sa.Column('last_used_at', sa.DateTime),
        sa.Column('expires_at', sa.DateTime),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
        
        # Индексы
        sa.Index('idx_saved_payment_methods_user_id', 'user_id'),
        sa.Index('idx_saved_payment_methods_type', 'payment_type'),
        sa.Index('idx_saved_payment_methods_status', 'status'),
        sa.Index('idx_saved_payment_methods_active', 'is_active'),
        sa.Index('idx_saved_payment_methods_default', 'is_default'),
    )
    
    # Создание таблицы auto_renewal_settings
    op.create_table(
        'auto_renewal_settings',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, nullable=False, index=True),
        sa.Column('payment_method_id', sa.String(32), nullable=False),
        sa.Column('status', sa.Enum('enabled', 'disabled', 'paused', 'failed', name='autorenewalstatus'), default='enabled'),
        sa.Column('is_enabled', sa.Boolean, default=True),
        sa.Column('renewal_days_before', sa.Integer, default=3),
        sa.Column('max_retry_attempts', sa.Integer, default=3),
        sa.Column('current_retry_count', sa.Integer, default=0),
        sa.Column('last_renewal_attempt', sa.DateTime),
        sa.Column('last_successful_renewal', sa.DateTime),
        sa.Column('last_failure_reason', sa.Text),
        sa.Column('notify_on_success', sa.Boolean, default=True),
        sa.Column('notify_on_failure', sa.Boolean, default=True),
        sa.Column('notify_before_renewal', sa.Boolean, default=True),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
        
        # Индексы
        sa.Index('idx_auto_renewal_settings_user_id', 'user_id'),
        sa.Index('idx_auto_renewal_settings_payment_method', 'payment_method_id'),
        sa.Index('idx_auto_renewal_settings_status', 'status'),
        sa.Index('idx_auto_renewal_settings_enabled', 'is_enabled'),
    )
    
    # Создание таблицы payment_method_usage_log
    op.create_table(
        'payment_method_usage_log',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, nullable=False, index=True),
        sa.Column('payment_method_id', sa.String(32), nullable=False),
        sa.Column('operation_type', sa.String(50), nullable=False),
        sa.Column('operation_result', sa.String(20), nullable=False),
        sa.Column('amount', sa.String(20)),
        sa.Column('currency', sa.String(3)),
        sa.Column('transaction_id', sa.String(100)),
        sa.Column('subscription_id', sa.String(32)),
        sa.Column('is_auto_renewal', sa.Boolean, default=False),
        sa.Column('error_message', sa.Text),
        sa.Column('metadata', sa.Text),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        
        # Индексы
        sa.Index('idx_payment_method_usage_log_user_id', 'user_id'),
        sa.Index('idx_payment_method_usage_log_payment_method', 'payment_method_id'),
        sa.Index('idx_payment_method_usage_log_operation', 'operation_type'),
        sa.Index('idx_payment_method_usage_log_result', 'operation_result'),
        sa.Index('idx_payment_method_usage_log_created', 'created_at'),
    )
    
    # Создание таблицы auto_renewal_queue
    op.create_table(
        'auto_renewal_queue',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, nullable=False, index=True),
        sa.Column('auto_renewal_settings_id', sa.String(32), nullable=False),
        sa.Column('subscription_id', sa.String(32)),
        sa.Column('subscription_expires_at', sa.DateTime, nullable=False),
        sa.Column('scheduled_at', sa.DateTime, nullable=False, index=True),
        sa.Column('priority', sa.Integer, default=5),
        sa.Column('status', sa.String(20), default='pending'),
        sa.Column('attempts', sa.Integer, default=0),
        sa.Column('max_attempts', sa.Integer, default=3),
        sa.Column('processed_at', sa.DateTime),
        sa.Column('result', sa.String(20)),
        sa.Column('error_message', sa.Text),
        sa.Column('new_subscription_id', sa.String(32)),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now()),
        
        # Индексы
        sa.Index('idx_auto_renewal_queue_user_id', 'user_id'),
        sa.Index('idx_auto_renewal_queue_settings', 'auto_renewal_settings_id'),
        sa.Index('idx_auto_renewal_queue_scheduled', 'scheduled_at'),
        sa.Index('idx_auto_renewal_queue_status', 'status'),
        sa.Index('idx_auto_renewal_queue_priority', 'priority'),
    )
    
    # Создание таблицы payment_method_security_log
    op.create_table(
        'payment_method_security_log',
        sa.Column('id', sa.String(32), primary_key=True),
        sa.Column('user_id', sa.BigInteger, nullable=False, index=True),
        sa.Column('payment_method_id', sa.String(32)),
        sa.Column('event_type', sa.String(50), nullable=False),
        sa.Column('severity', sa.String(20), default='info'),
        sa.Column('ip_address', sa.String(45)),
        sa.Column('user_agent', sa.Text),
        sa.Column('session_id', sa.String(100)),
        sa.Column('description', sa.Text),
        sa.Column('additional_data', sa.Text),
        sa.Column('action_taken', sa.String(100)),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now()),
        
        # Индексы
        sa.Index('idx_payment_method_security_log_user_id', 'user_id'),
        sa.Index('idx_payment_method_security_log_payment_method', 'payment_method_id'),
        sa.Index('idx_payment_method_security_log_event', 'event_type'),
        sa.Index('idx_payment_method_security_log_severity', 'severity'),
        sa.Index('idx_payment_method_security_log_created', 'created_at'),
    )


def downgrade() -> None:
    """Удаление таблиц системы управления платежными методами."""
    
    # Удаляем таблицы в обратном порядке
    op.drop_table('payment_method_security_log')
    op.drop_table('auto_renewal_queue')
    op.drop_table('payment_method_usage_log')
    op.drop_table('auto_renewal_settings')
    op.drop_table('saved_payment_methods')
    
    # Удаляем enum типы
    op.execute("DROP TYPE IF EXISTS paymentmethodtype")
    op.execute("DROP TYPE IF EXISTS paymentmethodstatus")
    op.execute("DROP TYPE IF EXISTS autorenewalstatus")
