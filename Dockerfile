FROM python:3.10-slim-bullseye

# Устанавливаем системные зависимости
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Копируем и устанавливаем зависимости
COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Копируем скрипты для работы с БД
COPY wait-for-db.py /app/wait-for-db.py
COPY test-db-connection.py /app/test-db-connection.py
RUN chmod +x /app/wait-for-db.py /app/test-db-connection.py

# Копируем код приложения
COPY bot /app

# Копируем локализацию и компилируем ее на этапе сборки
COPY locales /app/locales

# Компилируем локализацию как root (до смены пользователя)
WORKDIR /app
RUN pybabel compile -d locales -D bot

# Создаем пользователя для безопасности
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Открываем порты
EXPOSE 8000

# Запуск приложения
ENTRYPOINT ["bash", "-c", "python wait-for-db.py && alembic upgrade head && python main.py"]