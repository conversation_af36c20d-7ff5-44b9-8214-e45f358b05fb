#!/usr/bin/env python3
import requests
import os

# Тестируем с разными вариантами пароля
passwords_to_test = [
    'a2$TFS^2FY',  # Оригинальный
    'a2$$TFS^2FY',  # С двойным $
    'a2\\$TFS^2FY',  # С экранированием
]

host = 'https://panel.snaplyze.me'
username = 'Snaplyze'

print("Testing Marzban API with different password formats...")

for i, password in enumerate(passwords_to_test, 1):
    print(f"\nTest {i}: Password = {repr(password)}")
    
    data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(
            f'{host}/api/admin/token',
            data=data,
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            token_data = response.json()
            print(f"Token type: {token_data.get('token_type')}")
            print(f"Token length: {len(token_data.get('access_token', ''))}")
            break
        else:
            print(f"❌ Failed: {response.text[:100]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

# Тестируем переменную окружения
print(f"\nEnvironment variable PANEL_PASS: {repr(os.getenv('PANEL_PASS'))}")
