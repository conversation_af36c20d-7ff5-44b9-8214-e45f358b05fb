#!/usr/bin/env python3
"""
Тест команды /start через симуляцию Telegram сообщения.
"""

import asyncio
import sys
import os

# Добавляем путь к корню проекта
sys.path.insert(0, '/app')

from aiogram import Bot
from aiogram.types import Message, User, Chat
from handlers.commands import start
from db.base import get_session
from repositories.user_repository import SQLUserRepository
from sqlalchemy import text

async def test_start_command():
    """Тестирует команду /start через симуляцию."""
    print("🔄 Testing /start command simulation...")
    
    # Создаем фиктивного пользователя Telegram
    test_user_id = 987654321
    
    try:
        # Создаем объекты для симуляции Telegram сообщения
        user = User(
            id=test_user_id,
            is_bot=False,
            first_name="Test",
            last_name="User",
            username="testuser"
        )
        
        chat = Chat(
            id=test_user_id,
            type="private"
        )
        
        # Создаем фиктивное сообщение
        message = Message(
            message_id=1,
            date=1234567890,
            chat=chat,
            from_user=user,
            text="/start"
        )
        
        print(f"📱 Simulating /start command from user {test_user_id}")
        
        # Проверяем, что пользователя нет в базе
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            existing_user = await user_repo.get_by_telegram_id(test_user_id)
            
            if existing_user:
                print(f"ℹ️ User {test_user_id} already exists in database")
            else:
                print(f"ℹ️ User {test_user_id} does not exist, will be created")
        
        # Симулируем обработку команды /start
        try:
            # Вызываем обработчик команды start
            await start(message)
            print("✅ /start command executed successfully")
            
        except Exception as e:
            print(f"❌ Error executing /start command: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Проверяем, что пользователь создался в базе
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            created_user = await user_repo.get_by_telegram_id(test_user_id)
            
            if created_user:
                print(f"✅ User successfully created in database:")
                print(f"  - ID: {created_user.id}")
                print(f"  - Telegram ID: {created_user.tg_id}")
                print(f"  - VPN ID: {created_user.vpn_id}")
                print(f"  - Username: {created_user.username}")
                return True
            else:
                print(f"❌ User was not created in database")
                return False
        
    except Exception as e:
        print(f"❌ Error in /start command test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_operations():
    """Дополнительный тест операций с базой данных."""
    print("\n🔄 Testing additional database operations...")
    
    try:
        async with get_session() as session:
            user_repo = SQLUserRepository(session)
            
            # Тест статистики пользователей
            stats = await user_repo.get_user_stats()
            print(f"📊 User statistics: {stats}")
            
            # Тест получения всех пользователей
            users = await user_repo.get_all_users(limit=5)
            print(f"👥 Total users in database: {len(users)}")
            
            for user in users:
                print(f"  - User {user.tg_id}: VPN ID {user.vpn_id}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error in database operations test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Главная функция."""
    print("🚀 Starting Telegram /start command tests...")
    
    # Тест команды /start
    if not await test_start_command():
        return 1
    
    # Тест дополнительных операций с базой данных
    if not await test_database_operations():
        return 1
    
    print("\n🎉 All Telegram /start tests passed!")
    print("✅ The bot should now work correctly with the /start command")
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
