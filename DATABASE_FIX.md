# 🔧 Исправление проблемы подключения к базе данных

## 🔍 Диагностированная проблема

**Корневая причина**: Бот пытался подключиться к `localhost` вместо имени сервиса Docker `db`.

### Что было исправлено:

1. **docker-compose.yml**: 
   - `DB_ADDRESS: db` (вместо `${DB_ADDRESS}`)
   - `DB_PORT: 3306` (вместо `${DB_PORT}`)

2. **wait-for-db.py**: 
   - Улучшена логика чтения переменных окружения
   - Добавлено логирование конфигурации подключения
   - Увеличен timeout подключения

3. **.env.example**: 
   - `DB_ADDRESS=db` (вместо localhost)
   - `DB_PORT=3306` (вместо 3406)

## 🚀 Тестирование исправления

### 1. Остановите текущие контейнеры
```bash
docker compose down
```

### 2. Пересоберите контейнеры
```bash
docker compose build --no-cache
```

### 3. Запустите сервисы
```bash
docker compose up -d
```

### 4. Проверьте логи
```bash
# Логи базы данных
docker compose logs db

# Логи бота
docker compose logs bot

# Следите за логами в реальном времени
docker compose logs -f bot
```

### 5. Тестирование подключения к БД
```bash
# Тест подключения внутри контейнера
docker compose exec bot python test-db-connection.py

# Проверка health check
curl http://localhost:8000/health
```

## 📋 Ожидаемые результаты

### ✅ Успешные логи бота:
```
bot-1  | 2025-06-03 15:44:06,610 - __main__ - INFO - 🚀 Starting database readiness check...
bot-1  | 2025-06-03 15:44:06,610 - __main__ - INFO - 📋 Database config loaded
bot-1  | 2025-06-03 15:44:06,610 - __main__ - INFO - Database config: host=db, port=3306, database=test1
bot-1  | 2025-06-03 15:44:06,610 - __main__ - INFO - 🔍 Detected database type: mysql
bot-1  | 2025-06-03 15:44:06,636 - __main__ - INFO - Waiting for MySQL database to be ready...
bot-1  | 2025-06-03 15:44:06,638 - __main__ - INFO - ✅ MySQL database is ready! (attempt 1/30)
bot-1  | 2025-06-03 15:44:06,638 - __main__ - INFO - 🎉 Database is ready! Proceeding with application startup...
```

### ✅ Последующие этапы запуска:
```
bot-1  | Compiling catalog locales/en/LC_MESSAGES/bot.po to locales/en/LC_MESSAGES/bot.mo
bot-1  | Compiling catalog locales/ru/LC_MESSAGES/bot.po to locales/ru/LC_MESSAGES/bot.mo
bot-1  | INFO  [alembic.runtime.migration] Context impl MySQLImpl.
bot-1  | INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
bot-1  | INFO  [alembic.runtime.migration] Running upgrade  -> abc123, Initial migration
bot-1  | 2025-06-03 15:44:10,123 - __main__ - INFO - Starting VPN bot...
bot-1  | 2025-06-03 15:44:10,456 - __main__ - INFO - VPN bot started successfully
```

## 🔧 Дополнительная диагностика

### Если проблема сохраняется:

1. **Проверьте сетевое подключение**:
```bash
# Проверьте, что контейнеры в одной сети
docker network ls
docker network inspect unveil-vpn_app-network
```

2. **Проверьте переменные окружения**:
```bash
# Переменные в контейнере бота
docker compose exec bot env | grep DB_
```

3. **Проверьте доступность БД**:
```bash
# Ping базы данных из контейнера бота
docker compose exec bot ping db

# Проверьте порт БД
docker compose exec bot nc -zv db 3306
```

4. **Проверьте статус контейнеров**:
```bash
docker compose ps
```

## 🎯 Контрольные точки

- [ ] База данных запускается и готова к подключениям
- [ ] Бот успешно подключается к БД через имя сервиса `db`
- [ ] Выполняются миграции Alembic
- [ ] Компилируются локализации
- [ ] Запускается основное приложение
- [ ] Health checks отвечают успешно

## 📞 Если нужна помощь

1. Сохраните полные логи: `docker compose logs > debug.log`
2. Проверьте переменные окружения: `docker compose config`
3. Убедитесь, что используете правильный `.env` файл
4. Проверьте права доступа к файлам и директориям

## 🔄 Откат изменений

Если что-то пошло не так, можно вернуться к предыдущей версии:

```bash
# Остановить контейнеры
docker compose down

# Удалить образы
docker compose down --rmi all

# Восстановить из git (если используется)
git checkout HEAD~1 -- docker-compose.yml .env.example wait-for-db.py

# Пересобрать
docker compose build --no-cache
docker compose up -d
```
