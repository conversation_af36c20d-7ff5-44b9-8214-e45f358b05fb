#!/usr/bin/env python3
"""
Скрипт ожидания готовности базы данных перед запуском VPN бота.
Проверяет доступность БД с retry механизмом.
"""

import os
import sys
import time
import logging
from typing import Optional

# Настраиваем базовое логирование
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_db_config() -> dict:
    """Получает конфигурацию БД из переменных окружения"""

    # Пытаемся получить URL БД
    db_url = os.getenv('DATABASE_URL')
    if db_url:
        return {'url': db_url}

    # Если нет URL, собираем из отдельных параметров
    # В Docker используем DB_ADDRESS вместо DB_HOST
    db_host = os.getenv('DB_ADDRESS') or os.getenv('DB_HOST', 'localhost')

    config = {
        'host': db_host,
        'port': int(os.getenv('DB_PORT', '3306')),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASS', ''),
        'database': os.getenv('DB_NAME', 'vpn_bot')
    }

    logger.info(f"Database config: host={config['host']}, port={config['port']}, database={config['database']}")
    return config


def wait_for_mysql(config: dict, max_attempts: int = 30, delay: int = 2) -> bool:
    """
    Ожидает готовности MySQL базы данных.
    
    Args:
        config: Конфигурация БД
        max_attempts: Максимальное количество попыток
        delay: Задержка между попытками в секундах
    
    Returns:
        True если БД готова, False если превышено количество попыток
    """
    
    try:
        import pymysql  # type: ignore
    except ImportError:
        logger.error("pymysql not installed. Installing...")
        os.system("pip install pymysql")
        try:
            import pymysql  # type: ignore
        except ImportError as e:
            logger.error(f"Failed to install pymysql: {e}")
            return False
    
    logger.info("Waiting for MySQL database to be ready...")
    
    for attempt in range(1, max_attempts + 1):
        try:
            if 'url' in config:
                # Парсим URL для получения параметров подключения
                from urllib.parse import urlparse
                parsed = urlparse(config['url'])
                
                connection_params = {
                    'host': parsed.hostname or 'localhost',
                    'port': parsed.port or 3306,
                    'user': parsed.username or 'root',
                    'password': parsed.password or '',
                    'database': parsed.path.lstrip('/') if parsed.path else 'vpn_bot'
                }
            else:
                connection_params = {
                    'host': config['host'],
                    'port': config['port'],
                    'user': config['user'],
                    'password': config['password'],
                    'database': config['database']
                }
            
            # Пытаемся подключиться к БД
            logger.debug(f"Attempting connection to {connection_params['host']}:{connection_params['port']}")
            connection = pymysql.connect(
                **connection_params,
                connect_timeout=10,
                read_timeout=10,
                write_timeout=10
            )
            
            # Выполняем простой запрос
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result and result[0] == 1:
                    logger.info(f"✅ MySQL database is ready! (attempt {attempt}/{max_attempts})")
                    connection.close()
                    return True
            
            connection.close()
            
        except Exception as e:
            logger.warning(
                f"❌ Database not ready (attempt {attempt}/{max_attempts}): {str(e)}"
            )
            
            if attempt < max_attempts:
                logger.info(f"⏳ Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                logger.error(f"💥 Failed to connect to database after {max_attempts} attempts")
                return False
    
    return False


def wait_for_postgresql(config: dict, max_attempts: int = 30, delay: int = 2) -> bool:
    """
    Ожидает готовности PostgreSQL базы данных.
    
    Args:
        config: Конфигурация БД
        max_attempts: Максимальное количество попыток
        delay: Задержка между попытками в секундах
    
    Returns:
        True если БД готова, False если превышено количество попыток
    """
    
    try:
        import psycopg2  # type: ignore
    except ImportError:
        logger.error("psycopg2 not installed. Installing...")
        os.system("pip install psycopg2-binary")
        try:
            import psycopg2  # type: ignore
        except ImportError as e:
            logger.error(f"Failed to install psycopg2: {e}")
            return False
    
    logger.info("Waiting for PostgreSQL database to be ready...")
    
    for attempt in range(1, max_attempts + 1):
        try:
            if 'url' in config:
                # Используем URL напрямую
                connection = psycopg2.connect(config['url'])
            else:
                connection = psycopg2.connect(
                    host=config['host'],
                    port=config['port'],
                    user=config['user'],
                    password=config['password'],
                    database=config['database']
                )
            
            # Выполняем простой запрос
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result and result[0] == 1:
                    logger.info(f"✅ PostgreSQL database is ready! (attempt {attempt}/{max_attempts})")
                    connection.close()
                    return True
            
            connection.close()
            
        except Exception as e:
            logger.warning(
                f"❌ Database not ready (attempt {attempt}/{max_attempts}): {str(e)}"
            )
            
            if attempt < max_attempts:
                logger.info(f"⏳ Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                logger.error(f"💥 Failed to connect to database after {max_attempts} attempts")
                return False
    
    return False


def detect_database_type(config: dict) -> str:
    """
    Определяет тип базы данных по конфигурации.
    
    Args:
        config: Конфигурация БД
    
    Returns:
        Тип БД: 'mysql', 'postgresql' или 'unknown'
    """
    
    if 'url' in config:
        url = config['url'].lower()
        if 'mysql' in url or 'mariadb' in url:
            return 'mysql'
        elif 'postgresql' in url or 'postgres' in url:
            return 'postgresql'
    else:
        # По умолчанию считаем MySQL (как в оригинальном проекте)
        return 'mysql'
    
    return 'unknown'


def main():
    """Основная функция скрипта"""
    
    logger.info("🚀 Starting database readiness check...")
    
    # Получаем конфигурацию БД
    try:
        db_config = get_db_config()
        logger.info(f"📋 Database config loaded")
        
        # Определяем тип БД
        db_type = detect_database_type(db_config)
        logger.info(f"🔍 Detected database type: {db_type}")
        
        # Ожидаем готовности БД в зависимости от типа
        if db_type == 'mysql':
            success = wait_for_mysql(db_config)
        elif db_type == 'postgresql':
            success = wait_for_postgresql(db_config)
        else:
            logger.error(f"❌ Unsupported database type: {db_type}")
            sys.exit(1)
        
        if success:
            logger.info("🎉 Database is ready! Proceeding with application startup...")
            sys.exit(0)
        else:
            logger.error("💥 Database readiness check failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 Unexpected error during database check: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
