#!/usr/bin/env python3
"""
Простая инициализация базы данных без Alembic миграций.
Создает все необходимые таблицы напрямую через SQLAlchemy.
"""

import asyncio
import sys
import os
from sqlalchemy import text

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.database import get_session, engine
from db.models import Base
from db.models_countries_nodes import Base as CountriesNodesBase
from utils.logging_config import get_logger

logger = get_logger(__name__)


async def create_all_tables():
    """Создает все таблицы в базе данных."""
    try:
        logger.info("🔄 Creating all database tables...")
        
        # Создаем все таблицы из всех моделей
        async with engine.begin() as conn:
            # Создаем таблицы из основных моделей
            await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ Main tables created")
            
            # Создаем таблицы из моделей стран и нод
            await conn.run_sync(CountriesNodesBase.metadata.create_all)
            logger.info("✅ Countries and nodes tables created")
        
        logger.info("✅ All tables created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create tables: {e}")
        return False


async def check_database_connection():
    """Проверяет подключение к базе данных."""
    try:
        logger.info("🔄 Testing database connection...")
        
        async with get_session() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
            
        logger.info("✅ Database connection successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False


async def check_tables_exist():
    """Проверяет, что все необходимые таблицы существуют."""
    try:
        logger.info("🔄 Checking if tables exist...")
        
        # Список основных таблиц, которые должны существовать
        required_tables = [
            'vpnusers',
            'yookassa_payments', 
            'crypto_payments',
            'countries',
            'marzban_nodes',
            'marzban_inbounds',
            'user_node_preferences',
            'node_statistics'
        ]
        
        async with get_session() as session:
            for table in required_tables:
                try:
                    result = await session.execute(text(f"SHOW TABLES LIKE '{table}'"))
                    if result.fetchone() is None:
                        logger.warning(f"⚠️ Table '{table}' does not exist")
                        return False
                    else:
                        logger.info(f"✅ Table '{table}' exists")
                except Exception as e:
                    logger.error(f"❌ Error checking table '{table}': {e}")
                    return False
        
        logger.info("✅ All required tables exist!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to check tables: {e}")
        return False


async def insert_initial_data():
    """Вставляет начальные данные, если таблицы пустые."""
    try:
        logger.info("🔄 Checking for initial data...")
        
        async with get_session() as session:
            # Проверяем, есть ли страны
            result = await session.execute(text("SELECT COUNT(*) FROM countries"))
            countries_count = result.scalar()
            
            if countries_count == 0:
                logger.info("🔄 Inserting initial countries...")
                
                # Вставляем базовые страны
                countries_sql = """
                INSERT INTO countries (id, name_en, name_ru, iso_code, flag, priority, is_active, created_at, updated_at) VALUES
                ('DE', 'Germany', 'Германия', 'DE', '🇩🇪', 1, 1, NOW(), NOW()),
                ('NL', 'Netherlands', 'Нидерланды', 'NL', '🇳🇱', 2, 1, NOW(), NOW()),
                ('US', 'United States', 'США', 'US', '🇺🇸', 3, 1, NOW(), NOW())
                """
                
                await session.execute(text(countries_sql))
                await session.commit()
                logger.info("✅ Initial countries inserted")
            else:
                logger.info(f"✅ Countries already exist: {countries_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to insert initial data: {e}")
        return False


async def main():
    """Главная функция инициализации базы данных."""
    logger.info("🚀 Starting simple database initialization...")
    
    # 1. Проверяем подключение
    if not await check_database_connection():
        logger.error("❌ Database connection failed, aborting")
        return False
    
    # 2. Создаем таблицы
    if not await create_all_tables():
        logger.error("❌ Failed to create tables, aborting")
        return False
    
    # 3. Проверяем, что таблицы созданы
    if not await check_tables_exist():
        logger.error("❌ Some tables are missing, aborting")
        return False
    
    # 4. Вставляем начальные данные
    if not await insert_initial_data():
        logger.error("❌ Failed to insert initial data, aborting")
        return False
    
    logger.info("🎉 Database initialization completed successfully!")
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
