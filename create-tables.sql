-- =============================================================================
-- SQL SCRIPT FOR CREATING VPN BOT TABLES
-- =============================================================================
-- Создает все необходимые таблицы для VPN бота без использования Alembic
-- =============================================================================

-- Основная таблица пользователей VPN
CREATE TABLE IF NOT EXISTS vpnusers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tg_id BIGINT NOT NULL UNIQUE,
    username VARCHAR(255),
    vpn_id VARCHAR(255),
    subscription_url TEXT,
    expire_date DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tg_id (tg_id),
    INDEX idx_vpn_id (vpn_id),
    INDEX idx_expire_date (expire_date)
);

-- Таблица платежей YooKassa
CREATE TABLE IF NOT EXISTS yookassa_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_id VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'RUB',
    status VARCHAR(50) NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_payment_id (payment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Таблица платежей Cryptomus
CREATE TABLE IF NOT EXISTS crypto_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    status VARCHAR(50) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Таблица стран
CREATE TABLE IF NOT EXISTS countries (
    id VARCHAR(10) PRIMARY KEY,
    name_en VARCHAR(100) NOT NULL,
    name_ru VARCHAR(100) NOT NULL,
    iso_code VARCHAR(3) NOT NULL,
    flag VARCHAR(10),
    priority INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active)
);

-- Таблица нод Marzban
CREATE TABLE IF NOT EXISTS marzban_nodes (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    country_id VARCHAR(10) NOT NULL,
    city VARCHAR(100),
    host VARCHAR(255) NOT NULL,
    port INT NOT NULL,
    api_port INT,
    max_users INT DEFAULT 1000,
    current_users INT DEFAULT 0,
    status ENUM('connected', 'disconnected', 'error') DEFAULT 'disconnected',
    is_active BOOLEAN DEFAULT TRUE,
    cpu_usage DECIMAL(5,2) DEFAULT 0.0,
    memory_usage DECIMAL(5,2) DEFAULT 0.0,
    config JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE,
    INDEX idx_country_id (country_id),
    INDEX idx_status (status),
    INDEX idx_is_active (is_active),
    INDEX idx_current_users (current_users)
);

-- Таблица inbounds для нод
CREATE TABLE IF NOT EXISTS marzban_inbounds (
    id VARCHAR(100) PRIMARY KEY,
    node_id VARCHAR(50) NOT NULL,
    tag VARCHAR(100) NOT NULL,
    protocol ENUM('vless', 'vmess', 'trojan', 'shadowsocks') NOT NULL,
    port INT NOT NULL,
    listen VARCHAR(50) DEFAULT '127.0.0.1',
    security ENUM('none', 'tls', 'reality') DEFAULT 'none',
    network ENUM('tcp', 'ws', 'grpc', 'http') DEFAULT 'tcp',
    is_active BOOLEAN DEFAULT TRUE,
    config JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (node_id) REFERENCES marzban_nodes(id) ON DELETE CASCADE,
    INDEX idx_node_id (node_id),
    INDEX idx_protocol (protocol),
    INDEX idx_is_active (is_active)
);

-- Таблица предпочтений пользователей по нодам
CREATE TABLE IF NOT EXISTS user_node_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    preferred_countries JSON,
    excluded_countries JSON,
    preferred_protocols JSON,
    max_ping INT,
    min_speed INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_preference (user_id),
    INDEX idx_user_id (user_id)
);

-- Таблица статистики нод
CREATE TABLE IF NOT EXISTS node_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    node_id VARCHAR(50) NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    network_usage DECIMAL(10,2),
    active_connections INT,
    response_time INT,
    uptime_percentage DECIMAL(5,2),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (node_id) REFERENCES marzban_nodes(id) ON DELETE CASCADE,
    INDEX idx_node_id (node_id),
    INDEX idx_timestamp (timestamp)
);

-- Таблица для миграций Alembic (чтобы избежать ошибок)
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) NOT NULL PRIMARY KEY
);

-- Вставляем начальные данные

-- Страны
INSERT IGNORE INTO countries (id, name_en, name_ru, iso_code, flag, priority, is_active) VALUES
('DE', 'Germany', 'Германия', 'DE', '🇩🇪', 1, 1),
('NL', 'Netherlands', 'Нидерланды', 'NL', '🇳🇱', 2, 1),
('US', 'United States', 'США', 'US', '🇺🇸', 3, 1),
('FR', 'France', 'Франция', 'FR', '🇫🇷', 4, 1),
('UK', 'United Kingdom', 'Великобритания', 'GB', '🇬🇧', 5, 1);

-- Пример ноды в Германии (обновите данные под вашу инфраструктуру)
INSERT IGNORE INTO marzban_nodes (id, name, country_id, city, host, port, api_port, max_users, current_users, status, is_active, config) VALUES
('germany-node-1', 'Germany Frankfurt #1', 'DE', 'Frankfurt', '************', 12000, 62050, 1000, 0, 'connected', 1, 
 JSON_OBJECT(
   'reality_destination', 'deb.debian.org:443',
   'reality_server_names', JSON_ARRAY('deb.debian.org'),
   'protocols', JSON_ARRAY('vless'),
   'haproxy_enabled', false,
   'description', 'VLESS TCP REALITY node in Germany'
 ));

-- Пример inbound для ноды
INSERT IGNORE INTO marzban_inbounds (id, node_id, tag, protocol, port, listen, security, network, is_active, config) VALUES
('germany-node-1-vless-reality', 'germany-node-1', 'VLESS_TCP_REALITY_GERMANY_NODE_1', 'vless', 12000, '127.0.0.1', 'reality', 'tcp', 1,
 JSON_OBJECT(
   'reality_settings', JSON_OBJECT(
     'dest', 'deb.debian.org:443',
     'server_names', JSON_ARRAY('deb.debian.org'),
     'private_key', 'YOUR_REALITY_PRIVATE_KEY_HERE',
     'short_ids', JSON_ARRAY('YOUR_SHORT_ID_HERE')
   ),
   'tcp_settings', JSON_OBJECT(
     'accept_proxy_protocol', true
   ),
   'sniffing', JSON_OBJECT(
     'enabled', true,
     'dest_override', JSON_ARRAY('http', 'tls')
   )
 ));

-- Устанавливаем версию Alembic, чтобы избежать ошибок миграции
INSERT IGNORE INTO alembic_version (version_num) VALUES ('head');

-- Показываем созданные таблицы
SHOW TABLES;
