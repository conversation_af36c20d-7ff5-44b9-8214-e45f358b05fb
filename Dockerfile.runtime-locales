FROM python:3.10-slim-bullseye

# Устанавливаем системные зависимости
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Копируем и устанавливаем зависимости
COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Копируем скрипты для работы с БД
COPY wait-for-db.py /app/wait-for-db.py
COPY test-db-connection.py /app/test-db-connection.py
RUN chmod +x /app/wait-for-db.py /app/test-db-connection.py

# Копируем код приложения
COPY bot /app

# Создаем пользователя для безопасности с определенным UID/GID
RUN groupadd -r appuser -g 1000 && useradd -r -g appuser -u 1000 appuser

# Создаем директорию для локализации и устанавливаем права
RUN mkdir -p /app/locales && chown -R appuser:appuser /app

# Переключаемся на пользователя appuser
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Открываем порты
EXPOSE 8000

# Запуск приложения с компиляцией локализации во время выполнения
ENTRYPOINT ["bash", "-c", "python wait-for-db.py && pybabel compile -d locales -D bot && alembic upgrade head && python main.py"]
