#!/bin/bash
# Скрипт для переключения между режимами компиляции локализации

set -e

CURRENT_MODE=""
if grep -q "pybabel compile -d locales -D bot" Dockerfile; then
    if grep -q "COPY locales /app/locales" Dockerfile; then
        CURRENT_MODE="build-time"
    else
        CURRENT_MODE="runtime"
    fi
else
    CURRENT_MODE="build-time"
fi

echo "🔍 Current mode: $CURRENT_MODE"
echo ""
echo "Available modes:"
echo "1) build-time  - Compile locales during Docker build (recommended for production)"
echo "2) runtime     - Compile locales during container startup (for development)"
echo ""

read -p "Select mode (1 or 2): " choice

case $choice in
    1)
        echo "🔄 Switching to build-time mode..."
        
        # Backup current files
        cp Dockerfile Dockerfile.backup
        cp docker-compose.yml docker-compose.yml.backup
        
        # Update Dockerfile for build-time compilation
        cat > Dockerfile << 'EOF'
FROM python:3.10-slim-bullseye

# Устанавливаем системные зависимости
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Копируем и устанавливаем зависимости
COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Копируем скрипты для работы с БД
COPY wait-for-db.py /app/wait-for-db.py
COPY test-db-connection.py /app/test-db-connection.py
RUN chmod +x /app/wait-for-db.py /app/test-db-connection.py

# Копируем код приложения
COPY bot /app

# Копируем локализацию и компилируем ее на этапе сборки
COPY locales /app/locales

# Компилируем локализацию как root (до смены пользователя)
WORKDIR /app
RUN pybabel compile -d locales -D bot

# Создаем пользователя для безопасности
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Открываем порты
EXPOSE 8000

# Запуск приложения
ENTRYPOINT ["bash", "-c", "python wait-for-db.py && alembic upgrade head && python main.py"]
EOF

        # Update docker-compose.yml to remove locales volume
        sed -i '/- "\.\/locales:\/app\/locales"/d' docker-compose.yml
        
        echo "✅ Switched to build-time mode"
        echo "📝 Locales will be compiled during Docker build"
        echo "🚀 Run: docker compose build --no-cache && docker compose up -d"
        ;;
        
    2)
        echo "🔄 Switching to runtime mode..."
        
        # Backup current files
        cp Dockerfile Dockerfile.backup
        cp docker-compose.yml docker-compose.yml.backup
        
        # Update Dockerfile for runtime compilation
        cat > Dockerfile << 'EOF'
FROM python:3.10-slim-bullseye

# Устанавливаем системные зависимости
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Копируем и устанавливаем зависимости
COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Копируем скрипты для работы с БД
COPY wait-for-db.py /app/wait-for-db.py
COPY test-db-connection.py /app/test-db-connection.py
RUN chmod +x /app/wait-for-db.py /app/test-db-connection.py

# Копируем код приложения
COPY bot /app

# Создаем пользователя для безопасности с определенным UID/GID
RUN groupadd -r appuser -g 1000 && useradd -r -g appuser -u 1000 appuser

# Создаем директорию для локализации и устанавливаем права
RUN mkdir -p /app/locales && chown -R appuser:appuser /app

# Переключаемся на пользователя appuser
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Открываем порты
EXPOSE 8000

# Запуск приложения с компиляцией локализации во время выполнения
ENTRYPOINT ["bash", "-c", "python wait-for-db.py && pybabel compile -d locales -D bot && alembic upgrade head && python main.py"]
EOF

        # Add locales volume to docker-compose.yml if not present
        if ! grep -q "locales:/app/locales" docker-compose.yml; then
            sed -i '/- "\.\/goods\.json:\/app\/goods\.json"/a\            - "./locales:/app/locales"' docker-compose.yml
        fi
        
        # Fix permissions
        echo "🔧 Fixing file permissions..."
        ./fix-permissions.sh
        
        echo "✅ Switched to runtime mode"
        echo "📝 Locales will be compiled during container startup"
        echo "🚀 Run: docker compose build --no-cache && docker compose up -d"
        ;;
        
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
echo "📋 Backup files created:"
echo "  - Dockerfile.backup"
echo "  - docker-compose.yml.backup"
echo ""
echo "🔄 To restore backups:"
echo "  mv Dockerfile.backup Dockerfile"
echo "  mv docker-compose.yml.backup docker-compose.yml"
