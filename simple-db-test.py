#!/usr/bin/env python3
"""
Простой тест подключения к базе данных без миграций Alembic.
"""

import asyncio
import os
import sys
import pymysql

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.database import get_session
from sqlalchemy import text


async def test_database_connection():
    """Тестирует подключение к базе данных."""
    print("🔄 Testing database connection...")
    
    try:
        # Получаем параметры подключения из переменных окружения
        db_host = os.getenv('DB_ADDRESS', 'db')
        db_port = int(os.getenv('DB_PORT', '3306'))
        db_user = os.getenv('DB_USER', 'unveil_user')
        db_pass = os.getenv('DB_PASS', 'unveil_secure_password_2024')
        db_name = os.getenv('DB_NAME', 'unveil_vpn_prod')
        
        print(f"Testing connection to:")
        print(f"  Host: {db_host}")
        print(f"  Port: {db_port}")
        print(f"  User: {db_user}")
        print(f"  Database: {db_name}")
        print()
        
        # Тест 1: Прямое подключение через pymysql
        print("🔄 Test 1: Direct pymysql connection...")
        connection = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_pass,
            database=db_name,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
        
        connection.close()
        print("✅ Direct pymysql connection successful")
        
        # Тест 2: SQLAlchemy подключение
        print("🔄 Test 2: SQLAlchemy connection...")
        async with get_session() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        print("✅ SQLAlchemy connection successful")
        
        # Тест 3: Проверка таблиц
        print("🔄 Test 3: Checking tables...")
        async with get_session() as session:
            result = await session.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            
            required_tables = [
                'vpnusers', 'yookassa_payments', 'crypto_payments',
                'countries', 'marzban_nodes', 'marzban_inbounds',
                'user_node_preferences', 'node_statistics'
            ]
            
            missing_tables = []
            for table in required_tables:
                if table in tables:
                    print(f"  ✅ Table '{table}' exists")
                else:
                    print(f"  ❌ Table '{table}' missing")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"❌ Missing tables: {missing_tables}")
                return False
        
        print("✅ All required tables exist")
        
        # Тест 4: Проверка данных
        print("🔄 Test 4: Checking initial data...")
        async with get_session() as session:
            # Проверяем страны
            result = await session.execute(text("SELECT COUNT(*) FROM countries"))
            countries_count = result.scalar()
            print(f"  Countries in database: {countries_count}")
            
            # Проверяем ноды
            result = await session.execute(text("SELECT COUNT(*) FROM marzban_nodes"))
            nodes_count = result.scalar()
            print(f"  Nodes in database: {nodes_count}")
            
            # Проверяем inbounds
            result = await session.execute(text("SELECT COUNT(*) FROM marzban_inbounds"))
            inbounds_count = result.scalar()
            print(f"  Inbounds in database: {inbounds_count}")
        
        print("✅ Database data check completed")
        
        print("\n🎉 All database tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


async def main():
    """Главная функция."""
    success = await test_database_connection()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
