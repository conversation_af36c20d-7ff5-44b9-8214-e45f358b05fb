# Сводка изменений: Упрощенная конфигурация Mar<PERSON>ban (только VLESS TCP REALITY)

## Основные изменения

### 1. Упрощение HAProxy конфигурации
- **Убраны**: TLS fallback backend, WebSocket backend'ы
- **Оставлены**: Только panel и reality backend'ы
- **Изменено**: Использование `send-proxy-v2` вместо `send-proxy`
- **Упрощено**: Конфигурация frontend с минимальными правилами

### 2. Конфигурация Xray
- **Убраны**: Все TLS fallback inbound'ы
- **Убраны**: Все WebSocket inbound'ы (VLESS WS, VMess WS, Trojan WS)
- **Убраны**: VMess и Trojan протоколы
- **Оставлен**: Только один VLESS TCP REALITY inbound
- **Изменено**: Destination с `tradingview.com` на `deb.debian.org`

### 3. Host Settings
- **Упрощены**: Только настройки для REALITY хостов
- **Убраны**: Все TLS host настройки
- **Изменено**: SNI и Host на `deb.debian.org`

### 4. Переменные окружения Marzban
- **Убрано**: `XRAY_FALLBACKS_INBOUND_TAG` (не нужен для REALITY-only)
- **Убраны**: SSL настройки для Marzban (работает только HTTP)
- **Оставлены**: Базовые настройки для работы с HAProxy

### 5. Тестирование и диагностика
- **Упрощены**: Команды тестирования только для REALITY
- **Убраны**: Все проверки TLS и WebSocket
- **Добавлены**: Специфичные проверки для `deb.debian.org`

## Преимущества упрощенной конфигурации

### ✅ Простота
- Меньше компонентов для настройки
- Легче понимать и обслуживать
- Меньше точек отказа

### ✅ Производительность
- Отсутствие fallback механизмов
- Прямая маршрутизация трафика
- Меньше нагрузки на сервер

### ✅ Надежность
- Только современный протокол REALITY
- Стабильная работа без сложных зависимостей
- Простая диагностика проблем

### ✅ Безопасность
- REALITY обеспечивает лучшую защиту от обнаружения
- Использование `deb.debian.org` как надежного destination
- Минимальная атакуемая поверхность

## Ключевые файлы конфигурации

### HAProxy (`/etc/haproxy/haproxy.cfg`)
```haproxy
listen front
    bind *:443
    mode tcp
    tcp-request inspect-delay 5s
    tcp-request content accept if { req_ssl_hello_type 1 }
    
    use_backend panel if { req.ssl_sni -i end panel.yourdomain.com }
    use_backend reality if { req.ssl_sni -i end reality.yourdomain.com }
    default_backend panel

backend panel
    mode tcp
    server srv1 127.0.0.1:10000

backend reality
    mode tcp
    server srv1 127.0.0.1:12000 send-proxy-v2
```

### REALITY Inbound (Marzban Core Settings)
```json
{
  "tag": "VLESS_TCP_REALITY",
  "listen": "127.0.0.1",
  "port": 12000,
  "protocol": "vless",
  "settings": {
    "clients": [],
    "decryption": "none"
  },
  "streamSettings": {
    "network": "tcp",
    "tcpSettings": {
      "acceptProxyProtocol": true
    },
    "security": "reality",
    "realitySettings": {
      "show": false,
      "dest": "deb.debian.org:443",
      "xver": 0,
      "serverNames": ["deb.debian.org"],
      "privateKey": "ВАШИ_ПРИВАТНЫЙ_КЛЮЧ_REALITY",
      "shortIds": ["ВАШИ_SHORT_ID"]
    }
  },
  "sniffing": {
    "enabled": true,
    "destOverride": ["http", "tls"]
  }
}
```

### HAProxy для нод (`/etc/haproxy/haproxy.cfg`)
```haproxy
listen front
    bind *:443
    mode tcp
    tcp-request inspect-delay 5s
    tcp-request content accept if { req_ssl_hello_type 1 }

    # Маршрутизация для ноды
    use_backend reality if { req.ssl_sni -i end node1.yourdomain.com }
    default_backend reality

backend reality
    mode tcp
    server srv1 127.0.0.1:12000 send-proxy-v2
```

### Marzban Environment (`.env`)
```env
UVICORN_HOST = "127.0.0.1"
UVICORN_PORT = 10000
XRAY_SUBSCRIPTION_URL_PREFIX = "https://panel.yourdomain.com"
DOCS = true
```

## Порты и маршрутизация

### Основной сервер
| Порт | Назначение | Описание |
|------|------------|----------|
| 443 | HAProxy Frontend | Единственный внешний порт |
| 10000 | Marzban Panel | Локальный HTTP сервер |
| 12000 | REALITY Inbound | VLESS TCP REALITY |

### Ноды
| Порт | Назначение | Описание |
|------|------------|----------|
| 443 | HAProxy Frontend | Единственный внешний порт |
| 12000 | REALITY Inbound | VLESS TCP REALITY |
| 62050 | Marzban Node API | Подключение к панели |
| 62051 | Xray API | Управление Xray |

### Архитектура с нодами
```
Клиенты → Порт 443 → HAProxy → REALITY inbound (12000)
├─ panel.yourdomain.com:443 → 127.0.0.1:12000 (основной)
├─ node1.yourdomain.com:443 → 127.0.0.1:12000 (нода 1)
└─ node2.yourdomain.com:443 → 127.0.0.1:12000 (нода 2)
```

## Быстрая проверка работоспособности

### Основной сервер
```bash
# Проверка портов
netstat -tlnp | grep -E ':(443|10000|12000)'

# Проверка HAProxy
systemctl status haproxy
curl -s http://127.0.0.1:8404/stats

# Проверка Marzban
marzban status
curl -v http://127.0.0.1:10000/docs

# Проверка REALITY
telnet 127.0.0.1 12000
marzban logs | grep REALITY
```

### Ноды
```bash
# Проверка портов на ноде
netstat -tlnp | grep -E ':(443|12000|62050|62051)'

# Проверка HAProxy на ноде
systemctl status haproxy
curl -s http://127.0.0.1:8404/stats

# Проверка Marzban-node
docker ps | grep marzban-node
docker logs marzban-node-marzban-node-1

# Проверка REALITY на ноде
telnet 127.0.0.1 12000
```

### Проверка всей системы
```bash
# Тест подключения к основному серверу
curl -v --resolve reality.yourdomain.com:443:MAIN_SERVER_IP \
  https://reality.yourdomain.com:443

# Тест подключения к ноде
curl -v --resolve node1.yourdomain.com:443:NODE1_IP \
  https://node1.yourdomain.com:443
```

## Результат

Упрощенная конфигурация обеспечивает:
- **Стабильную работу** только с протоколом VLESS TCP REALITY
- **Простое обслуживание** без сложных fallback механизмов
- **Высокую производительность** за счет прямой маршрутизации
- **Легкую диагностику** проблем
- **Совместимость** с VPN ботами через API

Система готова к продуктивному использованию! 🚀
