#!/usr/bin/env python3
"""
Тест импорта Python модулей в контейнере.
"""

import sys
import os

print("🔍 Testing Python imports in container...")
print(f"Python version: {sys.version}")
print(f"Working directory: {os.getcwd()}")
print(f"Python path: {sys.path}")

# Добавляем /app в Python path
sys.path.insert(0, '/app')
print(f"Added /app to Python path")

# Тестируем импорты
test_imports = [
    'db.database',
    'repositories.country_node_repository',
    'db.models_countries_nodes',
    'utils.logging_config',
    'utils.marzban_api'
]

for module_name in test_imports:
    try:
        __import__(module_name)
        print(f"✅ Successfully imported: {module_name}")
    except ImportError as e:
        print(f"❌ Failed to import {module_name}: {e}")
    except Exception as e:
        print(f"⚠️ Error importing {module_name}: {e}")

# Проверяем структуру файлов
print("\n📁 File structure check:")
important_paths = [
    '/app/db',
    '/app/db/database.py',
    '/app/repositories',
    '/app/utils',
    '/app/utils/marzban_api.py'
]

for path in important_paths:
    if os.path.exists(path):
        print(f"✅ {path} exists")
    else:
        print(f"❌ {path} missing")

print("\n🔍 Contents of /app:")
try:
    for item in sorted(os.listdir('/app')):
        item_path = os.path.join('/app', item)
        if os.path.isdir(item_path):
            print(f"📁 {item}/")
        else:
            print(f"📄 {item}")
except Exception as e:
    print(f"❌ Error listing /app: {e}")
