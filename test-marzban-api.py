#!/usr/bin/env python3
"""
Простой тест Marzban API.
"""

import os
import requests
import json
import sys

def test_marzban_api():
    """Тестирует подключение к Marzban API."""
    print("🔄 Testing Marzban API connection...")
    
    # Получаем параметры из переменных окружения
    host = os.getenv('PANEL_HOST', 'https://panel.snaplyze.me')
    username = os.getenv('PANEL_USER', 'Snaplyze')
    password = os.getenv('PANEL_PASS', 'a2$TFS^2FY')
    
    print(f"Host: {host}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print()
    
    # Тест 1: Получение токена
    print("🔄 Test 1: Getting authentication token...")
    
    data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(
            f'{host}/api/admin/token',
            data=data,
            timeout=10
        )
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            token_type = token_data.get('token_type')
            
            print("✅ Token obtained successfully!")
            print(f"Token type: {token_type}")
            print(f"Token length: {len(access_token)}")
            print()
            
            # Тест 2: Проверка API с токеном
            print("🔄 Test 2: Testing API with token...")
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            # Тестируем получение системной информации
            try:
                system_response = requests.get(
                    f'{host}/api/system',
                    headers=headers,
                    timeout=10
                )
                
                print(f"System API status: {system_response.status_code}")
                
                if system_response.status_code == 200:
                    system_data = system_response.json()
                    print("✅ System API works!")
                    print(f"System info keys: {list(system_data.keys())}")
                else:
                    print(f"❌ System API error: {system_response.text}")
                
            except Exception as e:
                print(f"❌ System API exception: {e}")
            
            # Тест 3: Получение списка пользователей
            print("\n🔄 Test 3: Getting users list...")
            
            try:
                users_response = requests.get(
                    f'{host}/api/users?offset=0&limit=10',
                    headers=headers,
                    timeout=10
                )
                
                print(f"Users API status: {users_response.status_code}")
                
                if users_response.status_code == 200:
                    users_data = users_response.json()
                    print("✅ Users API works!")
                    print(f"Total users: {users_data.get('total', 0)}")
                    print(f"Users in response: {len(users_data.get('users', []))}")
                else:
                    print(f"❌ Users API error: {users_response.text}")
                
            except Exception as e:
                print(f"❌ Users API exception: {e}")
            
            # Тест 4: Получение нод
            print("\n🔄 Test 4: Getting nodes list...")
            
            try:
                nodes_response = requests.get(
                    f'{host}/api/nodes',
                    headers=headers,
                    timeout=10
                )
                
                print(f"Nodes API status: {nodes_response.status_code}")
                
                if nodes_response.status_code == 200:
                    nodes_data = nodes_response.json()
                    print("✅ Nodes API works!")
                    if isinstance(nodes_data, list):
                        print(f"Nodes count: {len(nodes_data)}")
                        for node in nodes_data[:3]:  # Показываем первые 3 ноды
                            print(f"  - {node.get('name', 'Unknown')} ({node.get('address', 'No address')})")
                    else:
                        print(f"Nodes data type: {type(nodes_data)}")
                        print(f"Nodes data keys: {list(nodes_data.keys()) if isinstance(nodes_data, dict) else 'Not a dict'}")
                else:
                    print(f"❌ Nodes API error: {nodes_response.text}")
                
            except Exception as e:
                print(f"❌ Nodes API exception: {e}")
            
            print("\n🎉 Marzban API tests completed!")
            return True
            
        else:
            print(f"❌ Failed to get token: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout - check network connectivity")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - check host URL")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Главная функция."""
    success = test_marzban_api()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
