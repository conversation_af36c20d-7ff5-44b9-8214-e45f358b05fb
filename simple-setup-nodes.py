#!/usr/bin/env python3
"""
Простой скрипт для настройки нод через прямые SQL запросы.
Обходит проблемы с моделями SQLAlchemy.
"""

import asyncio
import os
import sys
import pymysql
import json

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logging_config import get_logger

logger = get_logger(__name__)


def get_db_connection():
    """Получает подключение к базе данных."""
    return pymysql.connect(
        host=os.getenv('DB_ADDRESS', 'db'),
        port=int(os.getenv('DB_PORT', '3306')),
        user=os.getenv('DB_USER', 'unveil_user'),
        password=os.getenv('DB_PASS', 'unveil_secure_password_2024'),
        database=os.getenv('DB_NAME', 'unveil_vpn_prod'),
        charset='utf8mb4'
    )


def setup_countries():
    """Создает страны в базе данных."""
    logger.info("📍 Setting up countries...")
    
    countries_data = [
        ('DE', 'Germany', 'Германия', 'DE', '🇩🇪', 1, 1),
        ('NL', 'Netherlands', 'Нидерланды', 'NL', '🇳🇱', 2, 1),
        ('US', 'United States', 'США', 'US', '🇺🇸', 3, 1),
        ('FR', 'France', 'Франция', 'FR', '🇫🇷', 4, 1),
        ('UK', 'United Kingdom', 'Великобритания', 'GB', '🇬🇧', 5, 1)
    ]
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Проверяем существующие страны
            cursor.execute("SELECT id FROM countries")
            existing_countries = {row[0] for row in cursor.fetchall()}
            
            # Добавляем новые страны
            for country_data in countries_data:
                country_id = country_data[0]
                if country_id in existing_countries:
                    logger.info(f"Country {country_data[2]} already exists")
                    continue
                
                cursor.execute("""
                    INSERT INTO countries (id, name_en, name_ru, iso_code, flag, priority, is_active)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, country_data)
                logger.info(f"Created country: {country_data[2]}")
            
            connection.commit()
            logger.info(f"✅ Countries setup complete")
            
    except Exception as e:
        logger.error(f"❌ Error setting up countries: {e}")
        connection.rollback()
        raise
    finally:
        connection.close()


def setup_nodes():
    """Создает ноды в базе данных."""
    logger.info("🖥️ Setting up Marzban nodes...")
    
    nodes_data = [
        {
            'id': 'germany-node-1',
            'name': 'Germany Frankfurt #1',
            'country_id': 'DE',
            'city': 'Frankfurt',
            'host': '************',
            'port': 12000,
            'api_port': 62050,
            'max_users': 1000,
            'current_users': 0,
            'status': 'connected',
            'is_active': 1,
            'cpu_usage': 15.0,
            'memory_usage': 45.0,
            'config': {
                'reality_destination': 'deb.debian.org:443',
                'reality_server_names': ['deb.debian.org'],
                'protocols': ['vless'],
                'haproxy_enabled': False,
                'description': 'VLESS TCP REALITY node in Germany'
            }
        },
        {
            'id': 'netherlands-node-1',
            'name': 'Netherlands Amsterdam #1',
            'country_id': 'NL',
            'city': 'Amsterdam',
            'host': 'your-netherlands-server-ip',
            'port': 12000,
            'api_port': 62050,
            'max_users': 1000,
            'current_users': 0,
            'status': 'disconnected',
            'is_active': 0,
            'cpu_usage': 0.0,
            'memory_usage': 0.0,
            'config': {
                'reality_destination': 'deb.debian.org:443',
                'reality_server_names': ['deb.debian.org'],
                'protocols': ['vless'],
                'haproxy_enabled': False,
                'description': 'VLESS TCP REALITY node in Netherlands (not configured yet)'
            }
        }
    ]
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Проверяем существующие ноды
            cursor.execute("SELECT id FROM marzban_nodes")
            existing_nodes = {row[0] for row in cursor.fetchall()}
            
            # Добавляем новые ноды
            for node_data in nodes_data:
                node_id = node_data['id']
                if node_id in existing_nodes:
                    logger.info(f"Node {node_data['name']} already exists")
                    continue
                
                cursor.execute("""
                    INSERT INTO marzban_nodes 
                    (id, name, country_id, city, host, port, api_port, max_users, current_users, 
                     status, is_active, cpu_usage, memory_usage, config)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    node_data['id'],
                    node_data['name'],
                    node_data['country_id'],
                    node_data['city'],
                    node_data['host'],
                    node_data['port'],
                    node_data['api_port'],
                    node_data['max_users'],
                    node_data['current_users'],
                    node_data['status'],
                    node_data['is_active'],
                    node_data['cpu_usage'],
                    node_data['memory_usage'],
                    json.dumps(node_data['config'])
                ))
                logger.info(f"Created node: {node_data['name']}")
            
            connection.commit()
            logger.info(f"✅ Nodes setup complete")
            
    except Exception as e:
        logger.error(f"❌ Error setting up nodes: {e}")
        connection.rollback()
        raise
    finally:
        connection.close()


def setup_inbounds():
    """Создает inbounds для нод."""
    logger.info("🔌 Setting up inbounds...")
    
    inbounds_data = [
        {
            'id': 'germany-node-1-vless-reality',
            'node_id': 'germany-node-1',
            'tag': 'VLESS_TCP_REALITY_GERMANY_NODE_1',
            'protocol': 'vless',
            'port': 12000,
            'listen': '127.0.0.1',
            'security': 'reality',
            'network': 'tcp',
            'is_active': 1,
            'config': {
                'reality_settings': {
                    'dest': 'deb.debian.org:443',
                    'server_names': ['deb.debian.org'],
                    'private_key': 'YOUR_REALITY_PRIVATE_KEY_HERE',
                    'short_ids': ['YOUR_SHORT_ID_HERE']
                },
                'tcp_settings': {
                    'accept_proxy_protocol': True
                },
                'sniffing': {
                    'enabled': True,
                    'dest_override': ['http', 'tls']
                }
            }
        },
        {
            'id': 'netherlands-node-1-vless-reality',
            'node_id': 'netherlands-node-1',
            'tag': 'VLESS_TCP_REALITY_NETHERLANDS_NODE_1',
            'protocol': 'vless',
            'port': 12000,
            'listen': '127.0.0.1',
            'security': 'reality',
            'network': 'tcp',
            'is_active': 0,
            'config': {
                'reality_settings': {
                    'dest': 'deb.debian.org:443',
                    'server_names': ['deb.debian.org'],
                    'private_key': 'YOUR_REALITY_PRIVATE_KEY_HERE',
                    'short_ids': ['YOUR_SHORT_ID_HERE']
                },
                'tcp_settings': {
                    'accept_proxy_protocol': True
                },
                'sniffing': {
                    'enabled': True,
                    'dest_override': ['http', 'tls']
                }
            }
        }
    ]
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Проверяем существующие inbounds
            cursor.execute("SELECT id FROM marzban_inbounds")
            existing_inbounds = {row[0] for row in cursor.fetchall()}
            
            # Добавляем новые inbounds
            for inbound_data in inbounds_data:
                inbound_id = inbound_data['id']
                if inbound_id in existing_inbounds:
                    logger.info(f"Inbound {inbound_data['tag']} already exists")
                    continue
                
                cursor.execute("""
                    INSERT INTO marzban_inbounds 
                    (id, node_id, tag, protocol, port, listen, security, network, is_active, config)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    inbound_data['id'],
                    inbound_data['node_id'],
                    inbound_data['tag'],
                    inbound_data['protocol'],
                    inbound_data['port'],
                    inbound_data['listen'],
                    inbound_data['security'],
                    inbound_data['network'],
                    inbound_data['is_active'],
                    json.dumps(inbound_data['config'])
                ))
                logger.info(f"Created inbound: {inbound_data['tag']}")
            
            connection.commit()
            logger.info(f"✅ Inbounds setup complete")
            
    except Exception as e:
        logger.error(f"❌ Error setting up inbounds: {e}")
        connection.rollback()
        raise
    finally:
        connection.close()


def show_summary():
    """Показывает сводку созданных данных."""
    logger.info("\n" + "="*60)
    logger.info("🎉 MARZBAN INFRASTRUCTURE SETUP COMPLETE!")
    logger.info("="*60)
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Страны
            cursor.execute("SELECT COUNT(*) FROM countries")
            countries_count = cursor.fetchone()[0]
            
            # Ноды
            cursor.execute("SELECT COUNT(*) FROM marzban_nodes")
            nodes_count = cursor.fetchone()[0]
            
            # Inbounds
            cursor.execute("SELECT COUNT(*) FROM marzban_inbounds")
            inbounds_count = cursor.fetchone()[0]
            
            logger.info(f"📊 Summary:")
            logger.info(f"  - Countries: {countries_count}")
            logger.info(f"  - Nodes: {nodes_count}")
            logger.info(f"  - Inbounds: {inbounds_count}")
            
            # Детали стран
            cursor.execute("SELECT flag, name_ru, id FROM countries ORDER BY priority")
            countries = cursor.fetchall()
            logger.info(f"\n🌍 Countries:")
            for flag, name_ru, country_id in countries:
                logger.info(f"  - {flag} {name_ru} ({country_id})")
            
            # Детали нод
            cursor.execute("SELECT name, host, port, status FROM marzban_nodes")
            nodes = cursor.fetchall()
            logger.info(f"\n🖥️ Nodes:")
            for name, host, port, status in nodes:
                status_emoji = "🟢" if status == "connected" else "🔴"
                logger.info(f"  - {status_emoji} {name} ({host}:{port})")
            
            # Детали inbounds
            cursor.execute("SELECT tag, protocol, is_active FROM marzban_inbounds")
            inbounds = cursor.fetchall()
            logger.info(f"\n🔌 Inbounds:")
            for tag, protocol, is_active in inbounds:
                active_emoji = "✅" if is_active else "❌"
                logger.info(f"  - {active_emoji} {tag} ({protocol})")
            
    except Exception as e:
        logger.error(f"❌ Error showing summary: {e}")
    finally:
        connection.close()
    
    logger.info("\n" + "="*60)
    logger.info("⚠️  IMPORTANT NEXT STEPS:")
    logger.info("="*60)
    logger.info("1. 🔑 Update REALITY private keys and short IDs in the database")
    logger.info("2. 🔧 Configure actual node IPs and verify connectivity")
    logger.info("3. 🧪 Test node selection and user creation")
    logger.info("4. 📊 Set up monitoring for node health checks")
    logger.info("5. 🚀 Start the bot and test country selection feature")


def main():
    """Главная функция."""
    logger.info("🚀 Starting Marzban infrastructure setup...")
    
    try:
        setup_countries()
        setup_nodes()
        setup_inbounds()
        show_summary()
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
