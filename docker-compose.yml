services:
    bot:
        build: .
        restart: always
        stop_signal: SIGINT
        ports:
            - "${WEBHOOK_PORT}:${WEBHOOK_PORT}"
            - "8000:8000"  # Порт для мониторинга
        environment:
            # Настройки логирования и мониторинга
            LOG_LEVEL: ${LOG_LEVEL:-INFO}
            ENABLE_JSON_LOGS: ${ENABLE_JSON_LOGS:-true}
            BOT_TOKEN: ${BOT_TOKEN}
            SHOP_NAME: ${SHOP_NAME}
            PROTOCOLS: ${PROTOCOLS}
            TEST_PERIOD: ${TEST_PERIOD}
            PERIOD_LIMIT: ${PERIOD_LIMIT}
            ABOUT: ${ABOUT}
            RULES_LINK: ${RULES_LINK}
            SUPPORT_LINK: ${SUPPORT_LINK}
            YOOKASSA_TOKEN: ${YOOKASSA_TOKEN}
            YOOKASSA_SHOPID: ${YOOKASSA_SHOPID}
            EMAIL: ${EMAIL}
            CRYPTO_TOKEN: ${CRYPTO_TOKEN}
            MERCHANT_UUID: ${MERCHANT_UUID}
            DB_NAME: ${DB_NAME}
            DB_USER: ${DB_USER}
            DB_PASS: ${DB_PASS}
            DB_ADDRESS: db
            DB_PORT: 3306
            PANEL_HOST: ${PANEL_HOST}
            PANEL_GLOBAL: ${PANEL_GLOBAL}
            PANEL_USER: ${PANEL_USER}
            PANEL_PASS: ${PANEL_PASS}
            WEBHOOK_URL: ${WEBHOOK_URL}
            WEBHOOK_PORT: ${WEBHOOK_PORT}
            RENEW_NOTIFICATION_TIME: ${RENEW_NOTIFICATION_TIME}
            TG_INFO_CHANEL: ${TG_INFO_CHANEL}
            STARS_PAYMENT_ENABLED: ${STARS_PAYMENT_ENABLED}
            EXPIRED_NOTIFICATION_TIME: ${EXPIRED_NOTIFICATION_TIME}
        volumes:
            - "./goods.json:/app/goods.json"
        healthcheck:
            test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health/live', timeout=5)"]
            start_period: 30s
            start_interval: 5s
            interval: 30s
            timeout: 10s
            retries: 3
        depends_on:
            db:
                condition: service_healthy
        networks:
            - app-network
    db:
        image: mariadb:lts
        restart: always
        ports:
          - "3406:3306"
        environment:
            MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASS}
            MARIADB_DATABASE: ${DB_NAME}
            MARIADB_USER: ${DB_USER}
            MARIADB_PASSWORD: ${DB_PASS}
        volumes:
            - "mysql-data:/var/lib/mysql"
        healthcheck:
            test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
            start_period: 10s
            start_interval: 3s
            interval: 10s
            timeout: 5s
            retries: 3
        networks:
            - app-network
volumes:
  mysql-data:
networks:
  app-network:
    driver: bridge