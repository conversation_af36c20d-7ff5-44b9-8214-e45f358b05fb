# 📊 Система мониторинга VPN бота

Документация по новой системе мониторинга, логирования и health checks.

## 🚀 Быстрый старт

### 1. Сборка и запуск
```bash
# Сборка контейнеров
docker compose build --no-cache

# Запуск сервисов
docker compose up -d

# Проверка статуса
docker compose ps
```

### 2. Проверка health checks
```bash
# Проверка готовности бота
curl http://localhost:8000/health/live

# Полная проверка здоровья
curl http://localhost:8000/health

# Проверка готовности к работе
curl http://localhost:8000/health/ready
```

### 3. Просмотр метрик
```bash
# Метрики Prometheus
curl http://localhost:8000/metrics

# Общий статус системы
curl http://localhost:8000/status
```

## 📋 Доступные endpoints

| Endpoint | Описание | Статус коды |
|----------|----------|-------------|
| `/health` | Полная проверка всех компонентов | 200, 503 |
| `/health/ready` | Готовность к обслуживанию запросов | 200, 503 |
| `/health/live` | Простая проверка жизнеспособности | 200, 503 |
| `/metrics` | Метрики в формате Prometheus | 200, 500 |
| `/status` | Краткий статус системы в JSON | 200, 500 |
| `/` | Информация о доступных endpoints | 200 |

## 🔍 Компоненты мониторинга

### Health Checks
- **Database**: Проверка соединения с MariaDB
- **Marzban API**: Доступность панели управления
- **Bot Core**: Состояние основных компонентов бота
- **Memory**: Использование оперативной памяти

### Метрики Prometheus
- **Пользователи**: регистрации, активные пользователи
- **Подписки**: создание, продление, активные подписки
- **Платежи**: попытки, суммы, время обработки
- **API**: запросы к Marzban, время ответа, ошибки
- **Система**: uptime, обработка сообщений, соединения с БД

### Логирование
- **Структурированные JSON логи** с correlation ID
- **Фильтрация чувствительных данных** (токены, пароли)
- **Специализированные логи** для разных типов событий
- **Трассировка запросов** через correlation ID

## ⚙️ Конфигурация

### Переменные окружения
```bash
# Уровень логирования (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Включить JSON формат логов
ENABLE_JSON_LOGS=true

# Порт для сервера мониторинга
MONITORING_PORT=8000
```

### Docker Compose
Сервер мониторинга автоматически запускается на порту 8000:
```yaml
ports:
  - "8000:8000"  # Порт для мониторинга
```

## 🔧 Troubleshooting

### Проблемы с запуском
1. **База данных не готова**:
   ```bash
   # Проверить логи wait-for-db.py
   docker compose logs bot | grep "database"
   ```

2. **Ошибки health checks**:
   ```bash
   # Детальная информация о проблемах
   curl http://localhost:8000/health | jq
   ```

3. **Проблемы с Marzban API**:
   ```bash
   # Проверить подключение к панели
   docker compose logs bot | grep "marzban"
   ```

### Мониторинг в production

#### Prometheus конфигурация
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'vpn-bot'
    static_configs:
      - targets: ['bot:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

#### Grafana Dashboard
Основные метрики для мониторинга:
- `vpn_bot_uptime_seconds` - время работы бота
- `vpn_bot_active_users` - активные пользователи
- `vpn_bot_errors_total` - общее количество ошибок
- `vpn_bot_payment_attempts_total` - попытки платежей
- `vpn_bot_marzban_api_duration_seconds` - время ответа Marzban API

#### Алерты
Рекомендуемые алерты:
```yaml
# Бот недоступен
- alert: BotDown
  expr: up{job="vpn-bot"} == 0
  for: 1m

# Высокий уровень ошибок
- alert: HighErrorRate
  expr: rate(vpn_bot_errors_total[5m]) > 0.1
  for: 5m

# Медленный Marzban API
- alert: SlowMarzbanAPI
  expr: histogram_quantile(0.95, vpn_bot_marzban_api_duration_seconds) > 5
  for: 2m
```

## 📈 Метрики и их значение

### Критические метрики
- **Bot Uptime**: Время работы бота без перезапусков
- **Error Rate**: Частота ошибок (должна быть < 1%)
- **API Response Time**: Время ответа Marzban API (< 2 сек)
- **Database Connections**: Количество соединений с БД

### Бизнес метрики
- **User Registrations**: Новые регистрации пользователей
- **Subscription Conversions**: Конверсия в платные подписки
- **Payment Success Rate**: Успешность платежей
- **Active Subscriptions**: Количество активных подписок

## 🎯 Следующие шаги

1. **Настроить Prometheus** для сбора метрик
2. **Создать Grafana dashboard** для визуализации
3. **Настроить алерты** для критических событий
4. **Интегрировать с системой логирования** (ELK, Loki)
5. **Добавить distributed tracing** (Jaeger, Zipkin)

## 📞 Поддержка

При возникновении проблем:
1. Проверьте health checks: `curl http://localhost:8000/health`
2. Просмотрите логи: `docker compose logs bot`
3. Проверьте метрики: `curl http://localhost:8000/metrics`
4. Обратитесь к документации по troubleshooting
