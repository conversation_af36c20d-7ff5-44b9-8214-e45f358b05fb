# План улучшений и доработок проекта Unveil VPN Bot

## Анализ текущего состояния

### Архитектура проекта
- **Технологии**: Python 3.10, aiogram 3.1.1, SQLAlchemy, MariaDB, Docker
- **Структура**: Модульная архитектура с разделением на handlers, keyboards, utils, tasks
- **Интеграции**: Marzban API, YooKassa, Cryptomus, Telegram Stars
- **Локализация**: Поддержка русского и английского языков

### Выявленные проблемы

#### 🔴 Критические проблемы
1. **Отсутствие обработки ошибок**: Нет централизованной системы обработки исключений
2. **Небезопасное хранение конфигурации**: Пароли и токены в открытом виде в .env
3. **Отсутствие логирования**: Минимальное логирование событий и ошибок
4. **Проблемы с URL формированием**: Дублирование URL в subscription_url (исправлено)
5. **Отсутствие валидации данных**: Нет проверки входящих данных от пользователей

#### 🟡 Средние проблемы
1. **Отсутствие тестов**: Нет unit/integration тестов
2. **Жестко закодированные значения**: Магические числа и строки в коде
3. **Отсутствие мониторинга**: Нет метрик и health checks
4. **Простая база данных**: Отсутствие индексов и оптимизации
5. **Отсутствие кэширования**: Повторные запросы к Marzban API

#### 🟢 Незначительные проблемы
1. **Отсутствие документации API**: Нет Swagger/OpenAPI
2. **Простой UI**: Базовые клавиатуры без rich-интерфейса
3. **Отсутствие аналитики**: Нет сбора метрик использования

## 🚀 План улучшений

### Фаза 1: Критические исправления (Приоритет: Высокий)

#### 1.1 Система обработки ошибок
```python
# Создать централизованный error handler
- Добавить middleware для обработки исключений
- Логирование всех ошибок с контекстом
- Graceful degradation при недоступности Marzban
- Retry механизм для API запросов
```

#### 1.2 Безопасность
```python
# Улучшение безопасности
- Шифрование чувствительных данных в БД
- Использование secrets для токенов
- Валидация всех входящих данных
- Rate limiting для API endpoints
- CORS настройки для webhook'ов
```

#### 1.3 Логирование и мониторинг
```python
# Структурированное логирование
- Добавить structured logging (JSON)
- Логирование всех операций с платежами
- Метрики производительности
- Health check endpoints
- Интеграция с Prometheus/Grafana
```

### Фаза 2: Архитектурные улучшения (Приоритет: Средний)

#### 2.1 Рефакторинг архитектуры
```python
# Применение паттернов проектирования
- Repository pattern для работы с БД
- Service layer для бизнес-логики
- Factory pattern для создания платежей
- Observer pattern для уведомлений
- Dependency Injection контейнер
```

#### 2.2 Улучшение базы данных
```python
# Оптимизация БД
- Добавить индексы на часто используемые поля
- Миграции с версионированием
- Connection pooling
- Soft delete для пользователей
- Аудит логи изменений
```

#### 2.3 Кэширование
```python
# Система кэширования
- Redis для кэширования API ответов
- Кэширование пользовательских сессий
- Кэширование конфигурации товаров
- TTL для различных типов данных
```

### Фаза 3: Функциональные улучшения (Приоритет: Средний)

#### 3.1 Расширенная система платежей
```python
- Система скидок и промокодов
- Подписки с автопродлением
- Возврат средств (refunds)
```

#### 3.2 Улучшенный пользовательский интерфейс
```python
# Rich UI компоненты
- Inline клавиатуры с эмодзи
- Прогресс бары для операций
- Карусели для выбора тарифов
- Интерактивные формы
- Поддержка Web App
```

#### 3.3 Система уведомлений
```python
# Расширенные уведомления
- Push уведомления через бота телеграм
- Персонализированные сообщения
- Шаблоны уведомлений
```

### Фаза 4: Аналитика и отчетность (Приоритет: Низкий)

#### 4.1 Система аналитики
```python
# Сбор и анализ данных
- Метрики использования
- Конверсия платежей
- Популярные тарифы
- География пользователей
- A/B тестирование
```

#### 4.2 Панель администратора
```python
# Web-интерфейс для администрирования
- Dashboard с метриками
- Управление пользователями
- Настройка тарифов
- Просмотр логов
- Управление промокодами
- Создание сообщений (отложенных сообщений) с уведомлениями для всех пользователей, у кого есть доступ к боту.
```

## 🛠 Технические улучшения

### Инфраструктура
```yaml
# Docker Compose улучшения
- Multi-stage builds для оптимизации
- Health checks для всех сервисов
- Secrets management
- Backup стратегия для БД
- Load balancer для масштабирования
```

### CI/CD Pipeline
```yaml
# Автоматизация развертывания
- GitHub Actions для CI/CD
- Автоматические тесты
- Code quality checks (pylint, black)
- Security scanning
- Automated deployments
```

### Тестирование
```python
# Комплексное тестирование
- Unit тесты для всех модулей
- Integration тесты для API
- E2E тесты для пользовательских сценариев
- Load testing для производительности
- Mock'и для внешних сервисов
```

## 📊 Метрики успеха

### KPI для измерения улучшений
1. **Надежность**: Uptime > 99.9%
2. **Производительность**: Response time < 200ms
3. **Безопасность**: 0 критических уязвимостей
4. **Пользовательский опыт**: Конверсия платежей > 85%
5. **Качество кода**: Test coverage > 80%

## 🎯 Roadmap реализации

### Месяц 1: Критические исправления
- [ ] Система обработки ошибок
- [ ] Базовое логирование
- [ ] Валидация данных
- [ ] Исправление URL проблем

### Месяц 2: Безопасность и стабильность
- [ ] Шифрование данных
- [ ] Rate limiting
- [ ] Health checks
- [ ] Мониторинг

### Месяц 3: Архитектурные улучшения
- [ ] Рефакторинг в паттерны
- [ ] Оптимизация БД
- [ ] Кэширование
- [ ] Тестирование

### Месяц 4: Функциональные улучшения
- [ ] Новые способы оплаты
- [ ] Улучшенный UI
- [ ] Расширенные уведомления
- [ ] Аналитика

## 💡 Инновационные идеи

### Будущие возможности
1. **AI-powered поддержка**: Чат-бот с ИИ для ответов на вопросы
2. **Динамическое ценообразование**: Цены на основе спроса
3. **Геолокационные тарифы**: Разные цены для разных регионов
4. **Социальные функции**: Реферальная программа
5. **Интеграция с VPN клиентами**: Автоматическая настройка

### Технологические тренды
1. **Microservices**: Разделение на микросервисы
2. **Kubernetes**: Оркестрация контейнеров
3. **GraphQL**: Более гибкое API
4. **WebRTC**: P2P соединения
5. **Blockchain**: Децентрализованные платежи

## 🔧 Инструменты для реализации

### Разработка
- **IDE**: PyCharm/VSCode с расширениями
- **Linting**: pylint, flake8, mypy
- **Formatting**: black, isort
- **Testing**: pytest, coverage.py
- **Documentation**: Sphinx, mkdocs

### Мониторинг
- **Metrics**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **APM**: Sentry для error tracking
- **Uptime**: UptimeRobot

### Безопасность
- **Secrets**: HashiCorp Vault
- **Scanning**: Bandit, Safety
- **SAST**: SonarQube
- **Dependency**: Dependabot

## 🔍 Детальный анализ кода

### Проблемы в текущей реализации

#### handlers/messages.py
```python
# Проблемы:
1. Отсутствие try-catch блоков
2. Прямое обращение к Marzban API без проверок
3. Жестко закодированные сообщения
4. Нет валидации пользовательского ввода

# Решение:
- Добавить декораторы для обработки ошибок
- Создать service layer для бизнес-логики
- Вынести сообщения в конфигурацию
- Добавить валидацию через Pydantic
```

#### utils/marzban_api.py
```python
# Проблемы:
1. Синхронный get_token() в асинхронном коде
2. Отсутствие connection pooling
3. Нет retry механизма
4. Жестко закодированные таймауты

# Решение:
- Переписать на полностью асинхронный код
- Добавить aiohttp session с pooling
- Реализовать exponential backoff
- Конфигурируемые таймауты
```

#### db/methods.py
```python
# Проблемы:
1. Отсутствие connection pooling
2. Нет обработки database deadlocks
3. Отсутствие транзакций для связанных операций
4. Нет soft delete

# Решение:
- Использовать async session pool
- Добавить retry для deadlocks
- Обернуть операции в транзакции
- Реализовать soft delete pattern
```

## 🏗 Архитектурные паттерны для внедрения

### 1. Repository Pattern
```python
# Абстракция доступа к данным
class UserRepository(ABC):
    @abstractmethod
    async def get_by_telegram_id(self, tg_id: int) -> Optional[User]:
        pass

    @abstractmethod
    async def create(self, user: User) -> User:
        pass

class SQLUserRepository(UserRepository):
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_telegram_id(self, tg_id: int) -> Optional[User]:
        # Реализация с SQLAlchemy
        pass
```

### 2. Service Layer
```python
# Бизнес-логика отделена от handlers
class SubscriptionService:
    def __init__(self,
                 user_repo: UserRepository,
                 marzban_client: MarzbanClient,
                 notification_service: NotificationService):
        self.user_repo = user_repo
        self.marzban_client = marzban_client
        self.notification_service = notification_service

    async def create_test_subscription(self, tg_id: int) -> SubscriptionResult:
        # Бизнес-логика создания тестовой подписки
        pass
```

### 3. Factory Pattern
```python
# Создание платежных провайдеров
class PaymentProviderFactory:
    @staticmethod
    def create_provider(provider_type: str) -> PaymentProvider:
        if provider_type == "yookassa":
            return YooKassaProvider()
        elif provider_type == "cryptomus":
            return CryptomusProvider()
        elif provider_type == "stars":
            return TelegramStarsProvider()
        else:
            raise ValueError(f"Unknown provider: {provider_type}")
```

## 🔐 Безопасность - детальный план

### Аутентификация и авторизация
```python
# JWT токены для API
class AuthService:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key

    def create_token(self, user_id: int, permissions: List[str]) -> str:
        payload = {
            "user_id": user_id,
            "permissions": permissions,
            "exp": datetime.utcnow() + timedelta(hours=24)
        }
        return jwt.encode(payload, self.secret_key, algorithm="HS256")
```

### Шифрование данных
```python
# Шифрование чувствительных данных
class EncryptionService:
    def __init__(self, key: bytes):
        self.cipher = Fernet(key)

    def encrypt(self, data: str) -> str:
        return self.cipher.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        return self.cipher.decrypt(encrypted_data.encode()).decode()
```

### Rate Limiting
```python
# Ограничение частоты запросов
class RateLimiter:
    def __init__(self, redis_client: Redis):
        self.redis = redis_client

    async def is_allowed(self, key: str, limit: int, window: int) -> bool:
        current = await self.redis.incr(key)
        if current == 1:
            await self.redis.expire(key, window)
        return current <= limit
```

## 📈 Система метрик и мониторинга

### Prometheus метрики
```python
# Кастомные метрики
from prometheus_client import Counter, Histogram, Gauge

# Счетчики
payment_attempts = Counter('payment_attempts_total', 'Total payment attempts', ['provider', 'status'])
subscription_created = Counter('subscriptions_created_total', 'Total subscriptions created', ['type'])

# Гистограммы для времени выполнения
marzban_request_duration = Histogram('marzban_request_duration_seconds', 'Marzban API request duration')

# Gauge для текущих значений
active_subscriptions = Gauge('active_subscriptions', 'Number of active subscriptions')
```

### Health Checks
```python
# Проверки состояния системы
class HealthChecker:
    def __init__(self, db_engine, marzban_client, redis_client):
        self.db_engine = db_engine
        self.marzban_client = marzban_client
        self.redis_client = redis_client

    async def check_database(self) -> HealthStatus:
        try:
            async with self.db_engine.connect() as conn:
                await conn.execute(text("SELECT 1"))
            return HealthStatus.HEALTHY
        except Exception as e:
            return HealthStatus.UNHEALTHY

    async def check_marzban(self) -> HealthStatus:
        try:
            await self.marzban_client.ping()
            return HealthStatus.HEALTHY
        except Exception:
            return HealthStatus.UNHEALTHY
```

## 🧪 Стратегия тестирования

### Unit тесты
```python
# Тестирование бизнес-логики
class TestSubscriptionService:
    @pytest.fixture
    def subscription_service(self):
        user_repo = Mock(spec=UserRepository)
        marzban_client = Mock(spec=MarzbanClient)
        notification_service = Mock(spec=NotificationService)
        return SubscriptionService(user_repo, marzban_client, notification_service)

    async def test_create_test_subscription_success(self, subscription_service):
        # Arrange
        tg_id = 12345
        user = User(tg_id=tg_id, vpn_id="test_vpn_id")
        subscription_service.user_repo.get_by_telegram_id.return_value = user

        # Act
        result = await subscription_service.create_test_subscription(tg_id)

        # Assert
        assert result.success is True
        subscription_service.marzban_client.create_user.assert_called_once()
```

### Integration тесты
```python
# Тестирование интеграций
class TestMarzbanIntegration:
    @pytest.mark.integration
    async def test_create_user_in_marzban(self):
        # Тест с реальным Marzban API (в тестовой среде)
        marzban_client = MarzbanClient(test_config)
        user_data = {
            "username": "test_user",
            "proxies": {"vless": {}},
            "inbounds": ["VLESS TCP REALITY"]
        }

        result = await marzban_client.create_user(user_data)
        assert result["username"] == "test_user"
```

### E2E тесты
```python
# Тестирование пользовательских сценариев
class TestUserJourney:
    @pytest.mark.e2e
    async def test_complete_subscription_flow(self, bot_client):
        # 1. Пользователь запускает бота
        response = await bot_client.send_message("/start")
        assert "Hello" in response.text

        # 2. Выбирает тестовую подписку
        response = await bot_client.click_button("5 days free 🆓")
        assert "Thank you" in response.text

        # 3. Проверяет подписку
        response = await bot_client.click_button("My subscription 👤")
        assert "Subscription page" in response.text
```

## 🚀 План миграции

### Этап 1: Подготовка (1-2 недели)
1. Создание feature branches для каждого улучшения
2. Настройка CI/CD pipeline
3. Создание тестовой среды
4. Документирование текущего API

### Этап 2: Рефакторинг (2-3 недели)
1. Внедрение паттернов проектирования
2. Добавление системы логирования
3. Создание service layer
4. Написание unit тестов

### Этап 3: Безопасность (1-2 недели)
1. Шифрование данных
2. Валидация входных данных
3. Rate limiting
4. Security audit

### Этап 4: Мониторинг (1 неделя)
1. Prometheus метрики
2. Health checks
3. Alerting
4. Dashboard

### Этап 5: Развертывание (1 неделя)
1. Blue-green deployment
2. Мониторинг метрик
3. Rollback план
4. Документация

## 🎯 Детальные улучшения системы подписок

### 📊 Анализ текущей системы подписок

#### Текущие ограничения:
1. **Простая структура товаров**: Только месяцы и цена
2. **Фиксированные сроки**: Только месячные подписки
3. **Отсутствие регионов**: Нет выбора серверов/нод
4. **Примитивные уведомления**: Только за 36 часов и после истечения
5. **Жестко закодированные inbounds**: Нет гибкости в выборе протоколов

### 🚀 Расширенная система подписок

#### 1. Многоуровневая структура товаров
```json
// Новая структура goods.json
{
  "subscriptions": [
    {
      "id": "basic_1m_eu",
      "title": "Basic EU",
      "description": "Базовый тариф для Европы",
      "duration": {
        "months": 1,
        "days": 0,
        "hours": 0
      },
      "price": {
        "rub": 299,
        "usd": 3.99,
        "eur": 3.49,
        "stars": 150
      },
      "features": {
        "data_limit": 0,  // 0 = unlimited
        "device_limit": 3,
        "speed_limit": 0,  // 0 = unlimited
        "protocols": ["vless", "shadowsocks"],
        "regions": ["eu-west", "eu-central"],
        "priority": "standard"
      },
      "category": "basic",
      "callback": "basic_1m_eu",
      "popular": false,
      "discount": null
    },
    {
      "id": "premium_3m_global",
      "title": "Premium Global",
      "description": "Премиум тариф с доступом ко всем серверам",
      "duration": {
        "months": 3,
        "days": 0,
        "hours": 0
      },
      "price": {
        "rub": 799,
        "usd": 10.99,
        "eur": 9.99,
        "stars": 400
      },
      "features": {
        "data_limit": 0,
        "device_limit": 10,
        "speed_limit": 0,
        "protocols": ["vless", "vmess", "trojan", "shadowsocks"],
        "regions": ["all"],
        "priority": "high"
      },
      "category": "premium",
      "callback": "premium_3m_global",
      "popular": true,
      "discount": {
        "percent": 15,
        "original_price": 897
      }
    }
  ],
  "regions": [
    {
      "id": "eu-west",
      "name": "Западная Европа",
      "description": "Серверы в Германии, Франции, Нидерландах",
      "flag": "🇪🇺",
      "nodes": ["eu-de-01", "eu-fr-01", "eu-nl-01"],
      "latency": "10-30ms",
      "load": "medium"
    },
    {
      "id": "us-east",
      "name": "США Восток",
      "description": "Серверы в Нью-Йорке, Вашингтоне",
      "flag": "🇺🇸",
      "nodes": ["us-ny-01", "us-dc-01"],
      "latency": "150-200ms",
      "load": "low"
    }
  ],
  "nodes": [
    {
      "id": "eu-de-01",
      "name": "Germany Frankfurt",
      "region": "eu-west",
      "marzban_host": "https://de.panel.example.com",
      "inbounds": {
        "vless": ["VLESS TCP REALITY DE"],
        "shadowsocks": ["Shadowsocks TCP DE"]
      },
      "status": "active",
      "load": 45,
      "max_users": 1000
    }
  ]
}
```

#### 2. Гибкая система сроков подписки
```python
# Новая модель подписки
class SubscriptionDuration:
    def __init__(self, months: int = 0, days: int = 0, hours: int = 0):
        self.months = months
        self.days = days
        self.hours = hours

    def to_seconds(self) -> int:
        total_seconds = 0
        total_seconds += self.months * 30 * 24 * 60 * 60
        total_seconds += self.days * 24 * 60 * 60
        total_seconds += self.hours * 60 * 60
        return total_seconds

    def to_human_readable(self, lang: str = "ru") -> str:
        parts = []
        if self.months > 0:
            parts.append(f"{self.months} мес." if lang == "ru" else f"{self.months} month(s)")
        if self.days > 0:
            parts.append(f"{self.days} дн." if lang == "ru" else f"{self.days} day(s)")
        if self.hours > 0:
            parts.append(f"{self.hours} ч." if lang == "ru" else f"{self.hours} hour(s)")
        return " ".join(parts)

# Предустановленные варианты сроков
SUBSCRIPTION_DURATIONS = {
    "trial": SubscriptionDuration(days=3),
    "week": SubscriptionDuration(days=7),
    "month": SubscriptionDuration(months=1),
    "quarter": SubscriptionDuration(months=3),
    "half_year": SubscriptionDuration(months=6),
    "year": SubscriptionDuration(months=12),
    "custom": None  # Пользовательский срок
}
```

#### 3. Система выбора регионов и нод
```python
# Сервис управления регионами
class RegionService:
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)

    async def get_available_regions(self, subscription_type: str) -> List[Region]:
        """Получить доступные регионы для типа подписки"""
        subscription = self._get_subscription(subscription_type)
        if "all" in subscription.features.regions:
            return self.config.regions
        return [r for r in self.config.regions if r.id in subscription.features.regions]

    async def get_optimal_node(self, region_id: str, user_location: str = None) -> Node:
        """Выбрать оптимальную ноду в регионе"""
        region = self._get_region(region_id)
        nodes = [self._get_node(node_id) for node_id in region.nodes]

        # Фильтруем активные ноды
        active_nodes = [n for n in nodes if n.status == "active"]

        # Сортируем по загрузке
        active_nodes.sort(key=lambda x: x.load)

        return active_nodes[0] if active_nodes else None

    async def check_node_capacity(self, node_id: str) -> bool:
        """Проверить, может ли нода принять нового пользователя"""
        node = self._get_node(node_id)
        current_users = await self._get_node_user_count(node_id)
        return current_users < node.max_users

# Клавиатура выбора региона
class RegionKeyboard:
    @staticmethod
    def get_region_selection_keyboard(regions: List[Region]) -> InlineKeyboardMarkup:
        builder = InlineKeyboardBuilder()

        for region in regions:
            builder.row(
                InlineKeyboardButton(
                    text=f"{region.flag} {region.name} ({region.latency})",
                    callback_data=f"select_region_{region.id}"
                )
            )

        builder.row(
            InlineKeyboardButton(
                text="🔄 Автовыбор (лучший пинг)",
                callback_data="auto_select_region"
            )
        )

        return builder.as_markup()
```

### 📢 Расширенная система уведомлений

#### 1. Многоуровневые уведомления
```python
# Конфигурация уведомлений
NOTIFICATION_SCHEDULE = {
    "renewal_reminders": [
        {"days_before": 7, "type": "info", "channels": ["telegram"]},
        {"days_before": 3, "type": "warning", "channels": ["telegram", "email"]},
        {"days_before": 1, "type": "urgent", "channels": ["telegram", "email", "push"]},
        {"hours_before": 12, "type": "critical", "channels": ["telegram", "email", "push"]},
        {"hours_before": 2, "type": "final", "channels": ["telegram", "email", "push", "sms"]}
    ],
    "expiration_notifications": [
        {"hours_after": 1, "type": "expired", "channels": ["telegram"]},
        {"days_after": 1, "type": "grace_period", "channels": ["telegram", "email"]},
        {"days_after": 7, "type": "final_notice", "channels": ["telegram", "email"]}
    ],
    "usage_alerts": [
        {"usage_percent": 80, "type": "usage_warning", "channels": ["telegram"]},
        {"usage_percent": 95, "type": "usage_critical", "channels": ["telegram", "push"]}
    ]
}

class NotificationService:
    def __init__(self, telegram_bot, email_service, sms_service):
        self.telegram_bot = telegram_bot
        self.email_service = email_service
        self.sms_service = sms_service
        self.templates = self._load_templates()

    async def send_renewal_reminder(self, user: User, days_remaining: int):
        """Отправить напоминание о продлении"""
        notification_type = self._get_notification_type(days_remaining)
        template = self.templates[f"renewal_{notification_type}"]

        context = {
            "user_name": user.first_name,
            "days_remaining": days_remaining,
            "subscription_type": user.subscription.title,
            "renewal_url": f"https://t.me/{self.telegram_bot.username}?start=renew",
            "discount_code": await self._generate_discount_code(user)
        }

        # Отправляем через все настроенные каналы
        for channel in notification_type["channels"]:
            await self._send_via_channel(channel, user, template, context)

    async def send_expiration_notice(self, user: User, hours_expired: int):
        """Уведомление об истечении подписки"""
        template = self.templates["subscription_expired"]

        context = {
            "user_name": user.first_name,
            "expired_hours": hours_expired,
            "grace_period_days": 3,
            "reactivation_url": f"https://t.me/{self.telegram_bot.username}?start=reactivate"
        }

        await self._send_via_channel("telegram", user, template, context)

    async def send_usage_alert(self, user: User, usage_percent: int):
        """Уведомление о превышении лимита трафика"""
        template = self.templates["usage_alert"]

        context = {
            "user_name": user.first_name,
            "usage_percent": usage_percent,
            "data_used": user.subscription.data_used,
            "data_limit": user.subscription.data_limit,
            "upgrade_url": f"https://t.me/{self.telegram_bot.username}?start=upgrade"
        }

        await self._send_via_channel("telegram", user, template, context)
```

#### 2. Персонализированные шаблоны уведомлений
```python
# Шаблоны уведомлений
NOTIFICATION_TEMPLATES = {
    "renewal_info": {
        "ru": """
🔔 Напоминание о продлении

Привет, {user_name}! 👋

Твоя подписка "{subscription_type}" истекает через {days_remaining} дней.

💡 Продли сейчас и получи скидку 10% по промокоду: {discount_code}

🔗 Продлить подписку: {renewal_url}
        """,
        "en": """
🔔 Renewal Reminder

Hi {user_name}! 👋

Your "{subscription_type}" subscription expires in {days_remaining} days.

💡 Renew now and get 10% discount with promo code: {discount_code}

🔗 Renew subscription: {renewal_url}
        """
    },
    "renewal_urgent": {
        "ru": """
⚠️ СРОЧНО: Подписка истекает завтра!

{user_name}, твоя подписка "{subscription_type}" истекает завтра!

🎁 Специальное предложение: скидка 15% при продлении сегодня!
Промокод: {discount_code}

⏰ Не упусти возможность - предложение действует только сегодня!

🔗 Продлить сейчас: {renewal_url}
        """,
        "en": """
⚠️ URGENT: Subscription expires tomorrow!

{user_name}, your "{subscription_type}" subscription expires tomorrow!

🎁 Special offer: 15% discount if you renew today!
Promo code: {discount_code}

⏰ Don't miss out - offer valid today only!

🔗 Renew now: {renewal_url}
        """
    }
}

# Система A/B тестирования уведомлений
class NotificationABTester:
    def __init__(self):
        self.experiments = {}

    def get_template_variant(self, user_id: int, template_name: str) -> str:
        """Получить вариант шаблона для A/B тестирования"""
        user_hash = hash(str(user_id)) % 100

        if template_name in self.experiments:
            experiment = self.experiments[template_name]
            if user_hash < experiment["variant_a_percent"]:
                return f"{template_name}_a"
            else:
                return f"{template_name}_b"

        return template_name
```

### 🎛 Интеллектуальная система управления подписками

#### 1. Автоматическое продление и управление
```python
# Система автопродления
class AutoRenewalService:
    def __init__(self, payment_service, notification_service):
        self.payment_service = payment_service
        self.notification_service = notification_service

    async def setup_auto_renewal(self, user_id: int, subscription_id: str, payment_method: str):
        """Настроить автопродление подписки"""
        user = await self.get_user(user_id)
        subscription = await self.get_subscription(subscription_id)

        auto_renewal = AutoRenewal(
            user_id=user_id,
            subscription_id=subscription_id,
            payment_method=payment_method,
            next_renewal_date=subscription.expire_date,
            status="active"
        )

        await self.save_auto_renewal(auto_renewal)
        await self.notification_service.send_auto_renewal_confirmation(user, subscription)

    async def process_auto_renewals(self):
        """Обработать автопродления (запускается по расписанию)"""
        renewals = await self.get_pending_renewals()

        for renewal in renewals:
            try:
                # Попытка списания средств
                payment_result = await self.payment_service.charge_saved_method(
                    renewal.payment_method,
                    renewal.subscription.price
                )

                if payment_result.success:
                    # Продлить подписку
                    await self.extend_subscription(renewal.user_id, renewal.subscription_id)
                    await self.notification_service.send_renewal_success(renewal.user)
                else:
                    # Уведомить о неудачной попытке
                    await self.notification_service.send_renewal_failed(renewal.user, payment_result.error)
                    await self.schedule_retry(renewal)

            except Exception as e:
                await self.handle_renewal_error(renewal, e)

# Система скидок и промокодов
class DiscountService:
    def __init__(self):
        self.active_discounts = {}

    async def create_promo_code(self, code: str, discount_percent: int,
                               valid_until: datetime, max_uses: int = None):
        """Создать промокод"""
        promo = PromoCode(
            code=code,
            discount_percent=discount_percent,
            valid_until=valid_until,
            max_uses=max_uses,
            current_uses=0,
            created_at=datetime.now()
        )
        await self.save_promo_code(promo)

    async def apply_discount(self, user_id: int, subscription_id: str, promo_code: str = None):
        """Применить скидку к подписке"""
        base_price = await self.get_subscription_price(subscription_id)

        # Персональные скидки
        personal_discount = await self.get_personal_discount(user_id)

        # Промокод
        promo_discount = 0
        if promo_code:
            promo = await self.validate_promo_code(promo_code)
            if promo:
                promo_discount = promo.discount_percent

        # Скидка за лояльность
        loyalty_discount = await self.calculate_loyalty_discount(user_id)

        # Максимальная скидка
        total_discount = min(personal_discount + promo_discount + loyalty_discount, 50)

        final_price = base_price * (1 - total_discount / 100)

        return DiscountResult(
            original_price=base_price,
            discount_percent=total_discount,
            final_price=final_price,
            savings=base_price - final_price
        )
```

#### 2. Система лимитов и квот
```python
# Управление лимитами трафика
class TrafficLimitService:
    def __init__(self, marzban_client):
        self.marzban_client = marzban_client

    async def set_traffic_limit(self, user_id: str, limit_gb: int):
        """Установить лимит трафика для пользователя"""
        limit_bytes = limit_gb * 1024 * 1024 * 1024
        await self.marzban_client.modify_user(user_id, {
            "data_limit": limit_bytes,
            "data_limit_reset_strategy": "monthly"
        })

    async def check_traffic_usage(self, user_id: str) -> TrafficUsage:
        """Проверить использование трафика"""
        user_data = await self.marzban_client.get_user(user_id)

        used_bytes = user_data.get("used_traffic", 0)
        limit_bytes = user_data.get("data_limit", 0)

        if limit_bytes == 0:  # Unlimited
            usage_percent = 0
        else:
            usage_percent = (used_bytes / limit_bytes) * 100

        return TrafficUsage(
            used_bytes=used_bytes,
            limit_bytes=limit_bytes,
            usage_percent=usage_percent,
            remaining_bytes=max(0, limit_bytes - used_bytes)
        )

    async def handle_traffic_exceeded(self, user_id: str):
        """Обработать превышение лимита трафика"""
        # Приостановить подписку
        await self.marzban_client.modify_user(user_id, {"status": "limited"})

        # Отправить уведомление
        user = await self.get_user_by_vpn_id(user_id)
        await self.notification_service.send_traffic_exceeded(user)

# Система приоритетов и QoS
class QoSService:
    def __init__(self):
        self.priority_configs = {
            "basic": {"speed_limit": 50, "priority": 1},
            "standard": {"speed_limit": 100, "priority": 2},
            "premium": {"speed_limit": 0, "priority": 3}  # 0 = unlimited
        }

    async def apply_qos_settings(self, user_id: str, subscription_type: str):
        """Применить настройки QoS для пользователя"""
        config = self.priority_configs.get(subscription_type, self.priority_configs["basic"])

        await self.marzban_client.modify_user(user_id, {
            "speed_limit": config["speed_limit"],
            "priority": config["priority"]
        })
```

#### 3. Расширенная аналитика подписок
```python
# Аналитика использования
class SubscriptionAnalytics:
    def __init__(self, db_engine):
        self.db = db_engine

    async def get_subscription_metrics(self, period: str = "month") -> SubscriptionMetrics:
        """Получить метрики подписок за период"""
        query = f"""
        SELECT
            subscription_type,
            COUNT(*) as total_subscriptions,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
            AVG(DATEDIFF(expire_date, created_date)) as avg_duration_days,
            SUM(price) as total_revenue
        FROM subscriptions
        WHERE created_date >= DATE_SUB(NOW(), INTERVAL 1 {period.upper()})
        GROUP BY subscription_type
        """

        results = await self.db.execute(query)
        return [SubscriptionMetrics(**row) for row in results]

    async def get_churn_analysis(self) -> ChurnAnalysis:
        """Анализ оттока пользователей"""
        # Пользователи, которые не продлили подписку
        churned_users = await self.db.execute("""
            SELECT COUNT(*) as churned_count
            FROM subscriptions s1
            WHERE s1.expire_date < NOW() - INTERVAL 7 DAY
            AND NOT EXISTS (
                SELECT 1 FROM subscriptions s2
                WHERE s2.user_id = s1.user_id
                AND s2.created_date > s1.expire_date
            )
        """)

        # Общее количество пользователей
        total_users = await self.db.execute("SELECT COUNT(DISTINCT user_id) FROM subscriptions")

        churn_rate = (churned_users[0]["churned_count"] / total_users[0]["count"]) * 100

        return ChurnAnalysis(
            churn_rate=churn_rate,
            churned_users=churned_users[0]["churned_count"],
            total_users=total_users[0]["count"]
        )

    async def predict_renewal_probability(self, user_id: int) -> float:
        """Предсказать вероятность продления подписки"""
        # Простая модель на основе исторических данных
        user_history = await self.get_user_subscription_history(user_id)

        factors = {
            "subscription_count": len(user_history),
            "avg_duration": sum(s.duration_days for s in user_history) / len(user_history),
            "last_payment_method": user_history[-1].payment_method if user_history else None,
            "traffic_usage_avg": await self.get_avg_traffic_usage(user_id)
        }

        # Простая формула (в реальности можно использовать ML модель)
        probability = min(100, (
            factors["subscription_count"] * 10 +
            min(factors["avg_duration"] / 30, 3) * 20 +
            (50 if factors["last_payment_method"] == "auto" else 30) +
            min(factors["traffic_usage_avg"], 80)
        ))

        return probability / 100
```

#### 4. Интеграция с множественными панелями Marzban
```python
# Менеджер множественных панелей
class MultiPanelManager:
    def __init__(self):
        self.panels = {}
        self.load_balancer = LoadBalancer()

    async def register_panel(self, panel_id: str, config: PanelConfig):
        """Зарегистрировать панель Marzban"""
        panel = MarzbanClient(
            host=config.host,
            username=config.username,
            password=config.password
        )

        # Проверить доступность
        if await panel.health_check():
            self.panels[panel_id] = {
                "client": panel,
                "config": config,
                "status": "active",
                "load": 0,
                "max_users": config.max_users
            }

    async def get_optimal_panel(self, region: str = None) -> str:
        """Выбрать оптимальную панель для нового пользователя"""
        available_panels = [
            p for p in self.panels.values()
            if p["status"] == "active" and p["load"] < p["max_users"]
        ]

        if region:
            # Фильтр по региону
            available_panels = [
                p for p in available_panels
                if p["config"].region == region
            ]

        if not available_panels:
            raise NoAvailablePanelsError("No available panels for new users")

        # Выбираем панель с наименьшей загрузкой
        return min(available_panels, key=lambda x: x["load"])["client"]

    async def migrate_user(self, user_id: str, from_panel: str, to_panel: str):
        """Мигрировать пользователя между панелями"""
        # Получить данные пользователя
        user_data = await self.panels[from_panel]["client"].get_user(user_id)

        # Создать на новой панели
        await self.panels[to_panel]["client"].add_user(user_data)

        # Удалить со старой панели
        await self.panels[from_panel]["client"].delete_user(user_id)

        # Обновить базу данных
        await self.update_user_panel(user_id, to_panel)

# Балансировщик нагрузки
class LoadBalancer:
    def __init__(self):
        self.strategies = {
            "round_robin": self._round_robin,
            "least_connections": self._least_connections,
            "weighted": self._weighted_distribution
        }

    def _least_connections(self, panels: List[Panel]) -> Panel:
        """Выбрать панель с наименьшим количеством подключений"""
        return min(panels, key=lambda p: p.current_users)

    def _weighted_distribution(self, panels: List[Panel]) -> Panel:
        """Взвешенное распределение на основе мощности серверов"""
        weights = [p.weight for p in panels]
        return random.choices(panels, weights=weights)[0]
```

#### 5. Система резервного копирования и восстановления
```python
# Сервис резервного копирования
class BackupService:
    def __init__(self, storage_service):
        self.storage = storage_service

    async def backup_user_data(self, user_id: str):
        """Создать резервную копию данных пользователя"""
        user_data = await self.get_complete_user_data(user_id)

        backup = UserBackup(
            user_id=user_id,
            timestamp=datetime.now(),
            data=user_data,
            checksum=self._calculate_checksum(user_data)
        )

        backup_path = f"backups/users/{user_id}/{backup.timestamp.isoformat()}.json"
        await self.storage.save(backup_path, backup.to_json())

        return backup_path

    async def restore_user_data(self, user_id: str, backup_timestamp: datetime):
        """Восстановить данные пользователя из резервной копии"""
        backup_path = f"backups/users/{user_id}/{backup_timestamp.isoformat()}.json"
        backup_data = await self.storage.load(backup_path)

        backup = UserBackup.from_json(backup_data)

        # Проверить целостность
        if not self._verify_checksum(backup.data, backup.checksum):
            raise BackupCorruptedError("Backup data is corrupted")

        # Восстановить пользователя
        await self.restore_user_to_marzban(backup.data)
        await self.restore_user_to_database(backup.data)

        return True
```

### 💳 Управление сохраненными платежными данными

#### 1. Система управления платежными методами пользователя
```python
# Модель сохраненных платежных методов
class SavedPaymentMethod(Base):
    __tablename__ = "saved_payment_methods"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey('vpnusers.tg_id'))
    provider = Column(String(32))  # 'yookassa', 'cryptomus', 'stars'
    payment_method_id = Column(String(128))  # ID от провайдера
    payment_type = Column(String(32))  # 'bank_card', 'wallet', etc.
    masked_data = Column(String(128))  # Замаскированные данные для отображения
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used = Column(DateTime)
    is_active = Column(Boolean, default=True)

# Сервис управления платежными методами
class PaymentMethodService:
    def __init__(self, yookassa_client, db_session):
        self.yookassa = yookassa_client
        self.db = db_session

    async def save_payment_method(self, user_id: int, payment_data: dict, provider: str = "yookassa"):
        """Сохранить платежный метод после успешной оплаты"""
        if provider == "yookassa" and payment_data.get("save_payment_method"):
            # Получаем данные о сохраненном методе от YooKassa
            payment_method = await self.yookassa.get_payment_method(
                payment_data["payment_method"]["id"]
            )

            saved_method = SavedPaymentMethod(
                user_id=user_id,
                provider="yookassa",
                payment_method_id=payment_method["id"],
                payment_type=payment_method["type"],
                masked_data=self._mask_payment_data(payment_method),
                is_default=await self._is_first_payment_method(user_id)
            )

            await self.db.add(saved_method)
            await self.db.commit()

            return saved_method

    async def get_user_payment_methods(self, user_id: int) -> List[SavedPaymentMethod]:
        """Получить все сохраненные платежные методы пользователя"""
        query = select(SavedPaymentMethod).where(
            SavedPaymentMethod.user_id == user_id,
            SavedPaymentMethod.is_active == True
        ).order_by(SavedPaymentMethod.is_default.desc(), SavedPaymentMethod.last_used.desc())

        result = await self.db.execute(query)
        return result.scalars().all()

    async def delete_payment_method(self, user_id: int, method_id: int) -> bool:
        """Удалить сохраненный платежный метод"""
        # Получаем метод оплаты
        method = await self.db.get(SavedPaymentMethod, method_id)

        if not method or method.user_id != user_id:
            raise PaymentMethodNotFoundError("Payment method not found")

        if method.provider == "yookassa":
            try:
                # Удаляем метод в YooKassa (если поддерживается API)
                await self.yookassa.delete_payment_method(method.payment_method_id)
            except Exception as e:
                # Логируем ошибку, но продолжаем удаление в нашей БД
                logger.warning(f"Failed to delete payment method in YooKassa: {e}")

        # Помечаем как неактивный в нашей БД
        method.is_active = False
        await self.db.commit()

        # Если это был метод по умолчанию, назначаем новый
        if method.is_default:
            await self._set_new_default_method(user_id)

        return True

    async def set_default_payment_method(self, user_id: int, method_id: int) -> bool:
        """Установить метод оплаты по умолчанию"""
        # Сбрасываем флаг у всех методов пользователя
        await self.db.execute(
            update(SavedPaymentMethod)
            .where(SavedPaymentMethod.user_id == user_id)
            .values(is_default=False)
        )

        # Устанавливаем новый метод по умолчанию
        method = await self.db.get(SavedPaymentMethod, method_id)
        if method and method.user_id == user_id:
            method.is_default = True
            await self.db.commit()
            return True

        return False

    def _mask_payment_data(self, payment_method: dict) -> str:
        """Замаскировать данные платежного метода для отображения"""
        if payment_method["type"] == "bank_card":
            card = payment_method["card"]
            return f"**** **** **** {card['last4']} ({card['card_type']})"
        elif payment_method["type"] == "yoo_money":
            return f"YooMoney ****{payment_method['account_number'][-4:]}"
        elif payment_method["type"] == "sberbank":
            return "Сбербанк Онлайн"
        else:
            return payment_method["type"].replace("_", " ").title()

# Клавиатуры для управления платежными методами
class PaymentMethodKeyboards:
    @staticmethod
    def get_payment_methods_keyboard(methods: List[SavedPaymentMethod]) -> InlineKeyboardMarkup:
        """Клавиатура со списком сохраненных методов оплаты"""
        builder = InlineKeyboardBuilder()

        for method in methods:
            default_mark = "⭐ " if method.is_default else ""
            builder.row(
                InlineKeyboardButton(
                    text=f"{default_mark}{method.masked_data}",
                    callback_data=f"payment_method_{method.id}"
                )
            )

        builder.row(
            InlineKeyboardButton(
                text="➕ Добавить новый способ оплаты",
                callback_data="add_payment_method"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад",
                callback_data="back_to_profile"
            )
        )

        return builder.as_markup()

    @staticmethod
    def get_payment_method_actions_keyboard(method_id: int, is_default: bool) -> InlineKeyboardMarkup:
        """Клавиатура с действиями для конкретного метода оплаты"""
        builder = InlineKeyboardBuilder()

        if not is_default:
            builder.row(
                InlineKeyboardButton(
                    text="⭐ Сделать основным",
                    callback_data=f"set_default_payment_{method_id}"
                )
            )

        builder.row(
            InlineKeyboardButton(
                text="💳 Оплатить этим методом",
                callback_data=f"pay_with_saved_{method_id}"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="🗑 Удалить метод",
                callback_data=f"delete_payment_method_{method_id}"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад к списку",
                callback_data="manage_payment_methods"
            )
        )

        return builder.as_markup()

    @staticmethod
    def get_delete_confirmation_keyboard(method_id: int) -> InlineKeyboardMarkup:
        """Клавиатура подтверждения удаления"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="✅ Да, удалить",
                callback_data=f"confirm_delete_payment_{method_id}"
            ),
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data=f"payment_method_{method_id}"
            )
        )

        return builder.as_markup()

# Обработчики для управления платежными методами
class PaymentMethodHandlers:
    def __init__(self, payment_service: PaymentMethodService):
        self.payment_service = payment_service

    async def show_payment_methods(self, callback: CallbackQuery):
        """Показать список сохраненных методов оплаты"""
        methods = await self.payment_service.get_user_payment_methods(callback.from_user.id)

        if not methods:
            await callback.message.edit_text(
                "У вас пока нет сохраненных способов оплаты.\n\n"
                "Способы оплаты сохраняются автоматически при успешной оплате "
                "с выбранной опцией 'Сохранить для будущих платежей'.",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[[
                    InlineKeyboardButton(text="⏪ Назад", callback_data="back_to_profile")
                ]])
            )
            return

        text = "💳 Ваши сохраненные способы оплаты:\n\n"
        for i, method in enumerate(methods, 1):
            default_mark = "⭐ " if method.is_default else ""
            last_used = method.last_used.strftime("%d.%m.%Y") if method.last_used else "Не использовался"
            text += f"{i}. {default_mark}{method.masked_data}\n"
            text += f"   Последнее использование: {last_used}\n\n"

        text += "Выберите способ оплаты для управления:"

        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_payment_methods_keyboard(methods)
        )

    async def show_payment_method_details(self, callback: CallbackQuery, method_id: int):
        """Показать детали конкретного метода оплаты"""
        method = await self.payment_service.get_payment_method(method_id)

        if not method or method.user_id != callback.from_user.id:
            await callback.answer("Метод оплаты не найден", show_alert=True)
            return

        default_text = "⭐ Основной способ оплаты" if method.is_default else ""
        last_used = method.last_used.strftime("%d.%m.%Y в %H:%M") if method.last_used else "Не использовался"

        text = f"💳 {method.masked_data}\n\n"
        text += f"Тип: {method.payment_type.replace('_', ' ').title()}\n"
        text += f"Добавлен: {method.created_at.strftime('%d.%m.%Y в %H:%M')}\n"
        text += f"Последнее использование: {last_used}\n"
        if default_text:
            text += f"\n{default_text}"

        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_payment_method_actions_keyboard(
                method_id, method.is_default
            )
        )

    async def delete_payment_method(self, callback: CallbackQuery, method_id: int):
        """Показать подтверждение удаления"""
        method = await self.payment_service.get_payment_method(method_id)

        if not method or method.user_id != callback.from_user.id:
            await callback.answer("Метод оплаты не найден", show_alert=True)
            return

        text = f"🗑 Удалить способ оплаты?\n\n"
        text += f"💳 {method.masked_data}\n\n"
        text += "⚠️ Это действие нельзя отменить. Способ оплаты будет удален "
        text += "как из нашей системы, так и из YooKassa."

        await callback.message.edit_text(
            text,
            reply_markup=PaymentMethodKeyboards.get_delete_confirmation_keyboard(method_id)
        )

    async def confirm_delete_payment_method(self, callback: CallbackQuery, method_id: int):
        """Подтвердить удаление метода оплаты"""
        try:
            success = await self.payment_service.delete_payment_method(
                callback.from_user.id, method_id
            )

            if success:
                await callback.answer("✅ Способ оплаты удален", show_alert=True)
                # Возвращаемся к списку методов
                await self.show_payment_methods(callback)
            else:
                await callback.answer("❌ Ошибка при удалении", show_alert=True)

        except Exception as e:
            logger.error(f"Error deleting payment method: {e}")
            await callback.answer("❌ Произошла ошибка", show_alert=True)

# Интеграция с YooKassa для сохранения методов
class YooKassaPaymentService:
    def __init__(self, shop_id: str, secret_key: str):
        Configuration.configure(shop_id, secret_key)

    async def create_payment_with_save_option(self, user_id: int, amount: float,
                                            save_payment_method: bool = False) -> dict:
        """Создать платеж с опцией сохранения метода"""
        payment_data = {
            "amount": {"value": str(amount), "currency": "RUB"},
            "confirmation": {
                "type": "redirect",
                "return_url": f"https://t.me/{await self.get_bot_username()}"
            },
            "capture": True,
            "save_payment_method": save_payment_method,
            "metadata": {"user_id": str(user_id)}
        }

        if save_payment_method:
            payment_data["description"] = "Подписка VPN (с сохранением способа оплаты)"

        payment = Payment.create(payment_data)
        return payment

    async def charge_saved_payment_method(self, payment_method_id: str,
                                        amount: float, user_id: int) -> dict:
        """Списать средства с сохраненного метода оплаты"""
        payment_data = {
            "amount": {"value": str(amount), "currency": "RUB"},
            "payment_method_id": payment_method_id,
            "capture": True,
            "description": "Автопродление подписки VPN",
            "metadata": {"user_id": str(user_id), "auto_renewal": "true"}
        }

        payment = Payment.create(payment_data)
        return payment
```

#### 2. Интеграция в основное меню профиля
```python
# Обновленная клавиатура профиля
def get_profile_keyboard() -> InlineKeyboardMarkup:
    builder = InlineKeyboardBuilder()

    builder.row(
        InlineKeyboardButton(
            text="📊 Статистика использования",
            callback_data="usage_stats"
        )
    )

    builder.row(
        InlineKeyboardButton(
            text="💳 Управление способами оплаты",
            callback_data="manage_payment_methods"
        )
    )

    builder.row(
        InlineKeyboardButton(
            text="🔄 Настройки автопродления",
            callback_data="auto_renewal_settings"
        )
    )

    builder.row(
        InlineKeyboardButton(
            text="🎫 Мои промокоды",
            callback_data="my_promo_codes"
        )
    )

    builder.row(
        InlineKeyboardButton(
            text="⏪ Назад в главное меню",
            callback_data="back_to_main"
        )
    )

    return builder.as_markup()

# Регистрация обработчиков
@router.callback_query(F.data == "manage_payment_methods")
async def handle_manage_payment_methods(callback: CallbackQuery):
    payment_handler = PaymentMethodHandlers(payment_method_service)
    await payment_handler.show_payment_methods(callback)

@router.callback_query(F.data.startswith("payment_method_"))
async def handle_payment_method_details(callback: CallbackQuery):
    method_id = int(callback.data.split("_")[-1])
    payment_handler = PaymentMethodHandlers(payment_method_service)
    await payment_handler.show_payment_method_details(callback, method_id)

@router.callback_query(F.data.startswith("delete_payment_method_"))
async def handle_delete_payment_method(callback: CallbackQuery):
    method_id = int(callback.data.split("_")[-1])
    payment_handler = PaymentMethodHandlers(payment_method_service)
    await payment_handler.delete_payment_method(callback, method_id)

@router.callback_query(F.data.startswith("confirm_delete_payment_"))
async def handle_confirm_delete_payment_method(callback: CallbackQuery):
    method_id = int(callback.data.split("_")[-1])
    payment_handler = PaymentMethodHandlers(payment_method_service)
    await payment_handler.confirm_delete_payment_method(callback, method_id)
```

### 🌍 Система выбора стран и управления нодами (Актуальная Marzban Architecture)

#### 1. Правильная архитектура на основе актуальной документации Marzban
```python
# Модели для стран и нод (на основе реальной архитектуры Marzban)
class Country(Base):
    __tablename__ = "countries"

    id = Column(String(8), primary_key=True)  # "DE", "US", "NL"
    name = Column(String(64))  # "Germany", "United States"
    name_ru = Column(String(64))  # "Германия", "США"
    flag = Column(String(8))  # "🇩🇪", "🇺🇸"
    continent = Column(String(32))  # "Europe", "North America"
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # Приоритет отображения
    created_at = Column(DateTime, default=datetime.utcnow)

class MarzbanNode(Base):
    __tablename__ = "marzban_nodes"

    id = Column(String(32), primary_key=True)  # "de-fra-01"
    name = Column(String(128))  # "Germany Frankfurt #1"
    country_id = Column(String(8), ForeignKey('countries.id'))
    city = Column(String(64))  # "Frankfurt"

    # Marzban Node Configuration (реальная архитектура)
    node_name = Column(String(64))  # Имя ноды в Marzban панели
    node_address = Column(String(256))  # IP/домен ноды
    service_port = Column(Integer, default=62050)  # SERVICE_PORT для подключения
    api_port = Column(Integer, default=62051)  # XRAY_API_PORT

    # SSL сертификат для подключения к основной панели
    ssl_cert_path = Column(String(256))  # Путь к ssl_client_cert.pem

    # Статус и мониторинг (получаем через Marzban API)
    status = Column(String(16), default="active")  # connected, disconnected, error
    last_check = Column(DateTime)
    usage_ratio = Column(Float, default=1.0)  # Коэффициент использования ноды

    # Статистика (получаем через Marzban REST API)
    current_users = Column(Integer, default=0)
    total_traffic = Column(BigInteger, default=0)

    # Inbounds, которые используют эту ноду (через Host Settings)
    associated_inbound_tags = Column(JSON)  # ["VLESS TCP REALITY", "Shadowsocks TCP"]

    # Геолокация и сеть
    latitude = Column(Float)
    longitude = Column(Float)
    provider = Column(String(64))  # "Hetzner", "DigitalOcean"
    datacenter = Column(String(128))

    # Настройки для автоматического добавления хостов
    auto_add_as_host = Column(Boolean, default=True)  # Add this node as a new host for every inbound

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class MarzbanInbound(Base):
    __tablename__ = "marzban_inbounds"

    id = Column(String(32), primary_key=True)
    tag = Column(String(64), unique=True)  # "VLESS TCP REALITY DE"
    protocol = Column(String(16))  # "vless", "vmess", "trojan", "shadowsocks"
    port = Column(Integer)

    # Связь с нодами через Host Settings
    primary_node_id = Column(String(32), ForeignKey('marzban_nodes.id'))  # Основная нода
    backup_node_ids = Column(JSON)  # Резервные ноды

    # Настройки inbound из Core Settings
    listen_address = Column(String(64), default="0.0.0.0")
    network_type = Column(String(16), default="tcp")  # tcp, ws, grpc
    security_type = Column(String(16))  # reality, tls, none

    # Специфичные настройки для протоколов
    reality_settings = Column(JSON)  # Настройки REALITY
    tls_settings = Column(JSON)  # Настройки TLS

    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class UserNodePreference(Base):
    __tablename__ = "user_node_preferences"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey('vpnusers.tg_id'))
    preferred_countries = Column(JSON)  # ["DE", "NL", "US"]
    preferred_protocols = Column(JSON)  # ["vless", "vmess"]
    auto_select = Column(Boolean, default=True)
    last_selected_node = Column(String(32), ForeignKey('nodes.id'))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Сервис управления нодами и странами (на основе реальной архитектуры Marzban)
class MarzbanCountryNodeService:
    def __init__(self, db_session, marzban_client):
        self.db = db_session
        self.marzban = marzban_client  # Основная панель Marzban с REST API

    async def get_available_countries(self, subscription_type: str = None) -> List[Country]:
        """Получить список доступных стран"""
        query = select(Country).where(Country.is_active == True)

        # Фильтрация по типу подписки
        if subscription_type:
            # Премиум подписки имеют доступ ко всем странам
            if subscription_type not in ["premium", "enterprise"]:
                # Базовые подписки - только популярные страны
                query = query.where(Country.priority >= 5)

        query = query.order_by(Country.priority.desc(), Country.name)
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_country_nodes(self, country_id: str, include_offline: bool = False) -> List[MarzbanNode]:
        """Получить ноды для конкретной страны"""
        query = select(MarzbanNode).where(MarzbanNode.country_id == country_id)

        if not include_offline:
            query = query.where(MarzbanNode.status.in_(["connected", "active"]))

        query = query.order_by(MarzbanNode.current_users, MarzbanNode.usage_ratio)
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_optimal_node_for_country(self, country_id: str, protocol: str = "vless") -> Optional[MarzbanNode]:
        """Выбрать оптимальную ноду в стране для протокола"""
        nodes = await self.get_country_nodes(country_id)

        # Получаем inbounds, которые поддерживают нужный протокол и используют ноды этой страны
        compatible_nodes = []
        for node in nodes:
            # Проверяем, есть ли inbounds с нужным протоколом, связанные с этой нодой
            inbounds = await self.get_inbounds_for_node_and_protocol(node.id, protocol)
            if inbounds:
                compatible_nodes.append((node, inbounds))

        if not compatible_nodes:
            return None

        # Сортируем по загрузке с учетом usage_ratio
        compatible_nodes.sort(key=lambda x: x[0].current_users * x[0].usage_ratio)
        return compatible_nodes[0][0]  # Возвращаем ноду

    async def get_inbounds_for_node_and_protocol(self, node_id: str, protocol: str) -> List[MarzbanInbound]:
        """Получить inbounds для ноды и протокола"""
        query = select(MarzbanInbound).where(
            and_(
                MarzbanInbound.protocol == protocol,
                MarzbanInbound.is_active == True,
                or_(
                    MarzbanInbound.primary_node_id == node_id,
                    MarzbanInbound.backup_node_ids.contains([node_id])
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def check_node_status_via_marzban(self, node_id: str) -> NodeStatus:
        """Проверить статус ноды через основную панель Marzban"""
        node = await self.db.get(MarzbanNode, node_id)
        if not node:
            return NodeStatus.NOT_FOUND

        try:
            # Получаем информацию о нодах через основную панель
            nodes_info = await self.marzban.get_nodes()  # Предполагаемый метод API

            # Ищем нашу ноду в списке
            node_info = None
            for marzban_node in nodes_info:
                if marzban_node.get('name') == node.node_name:
                    node_info = marzban_node
                    break

            if not node_info:
                node.status = "offline"
                await self.db.commit()
                return NodeStatus.OFFLINE

            # Обновляем статистику ноды
            node.last_check = datetime.utcnow()
            node.status = "active" if node_info.get('status') == 'connected' else "offline"

            # Получаем статистику пользователей на ноде
            node_users = await self.get_node_users_count(node.node_name)
            node.current_users = node_users

            await self.db.commit()

            return NodeStatus.ONLINE if node.status == "active" else NodeStatus.OFFLINE

        except Exception as e:
            # Помечаем ноду как недоступную
            node.last_check = datetime.utcnow()
            node.status = "offline"
            await self.db.commit()

            return NodeStatus.OFFLINE

    async def get_node_users_count(self, node_name: str) -> int:
        """Получить количество пользователей на конкретной ноде"""
        try:
            # Получаем всех пользователей
            users_response = await self.marzban.get_users()
            users = users_response.get('users', [])

            # Считаем пользователей, которые используют inbounds этой ноды
            node = await self.get_node_by_name(node_name)
            if not node:
                return 0

            node_users_count = 0
            for user in users:
                user_inbounds = user.get('inbounds', {})
                # Проверяем, использует ли пользователь inbounds этой ноды
                for inbound_name in node.available_inbounds:
                    if inbound_name in user_inbounds:
                        node_users_count += 1
                        break

            return node_users_count

        except Exception as e:
            logger.error(f"Error getting users count for node {node_name}: {e}")
            return 0

    async def get_node_by_name(self, node_name: str) -> Optional[MarzbanNode]:
        """Получить ноду по имени"""
        query = select(MarzbanNode).where(MarzbanNode.node_name == node_name)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def get_user_preferences(self, user_id: int) -> UserNodePreference:
        """Получить предпочтения пользователя по нодам"""
        query = select(UserNodePreference).where(UserNodePreference.user_id == user_id)
        result = await self.db.execute(query)
        preference = result.scalar_one_or_none()

        if not preference:
            # Создаем предпочтения по умолчанию
            preference = UserNodePreference(
                user_id=user_id,
                preferred_countries=["DE", "NL", "US"],  # По умолчанию
                preferred_protocols=["vless", "vmess"],
                auto_select=True
            )
            self.db.add(preference)
            await self.db.commit()

        return preference

    async def update_user_preferences(self, user_id: int, countries: List[str],
                                    protocols: List[str], auto_select: bool):
        """Обновить предпочтения пользователя"""
        preference = await self.get_user_preferences(user_id)
        preference.preferred_countries = countries
        preference.preferred_protocols = protocols
        preference.auto_select = auto_select
        preference.updated_at = datetime.utcnow()

        await self.db.commit()
        return preference

# Клавиатуры для выбора стран
class CountrySelectionKeyboards:
    @staticmethod
    def get_country_selection_keyboard(countries: List[Country],
                                     user_preferences: List[str] = None) -> InlineKeyboardMarkup:
        """Клавиатура выбора стран"""
        builder = InlineKeyboardBuilder()

        # Группируем страны по континентам
        continents = {}
        for country in countries:
            if country.continent not in continents:
                continents[country.continent] = []
            continents[country.continent].append(country)

        for continent, continent_countries in continents.items():
            # Заголовок континента
            builder.row(
                InlineKeyboardButton(
                    text=f"🌍 {continent}",
                    callback_data="continent_header"
                )
            )

            # Страны континента (по 2 в ряд)
            for i in range(0, len(continent_countries), 2):
                row_countries = continent_countries[i:i+2]
                buttons = []

                for country in row_countries:
                    # Отмечаем выбранные страны
                    selected_mark = "✅ " if user_preferences and country.id in user_preferences else ""
                    buttons.append(
                        InlineKeyboardButton(
                            text=f"{selected_mark}{country.flag} {country.name_ru}",
                            callback_data=f"select_country_{country.id}"
                        )
                    )

                builder.row(*buttons)

        # Кнопки управления
        builder.row(
            InlineKeyboardButton(
                text="🎯 Автовыбор (лучший пинг)",
                callback_data="auto_select_country"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="✅ Сохранить выбор",
                callback_data="save_country_selection"
            ),
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="cancel_country_selection"
            )
        )

        return builder.as_markup()

    @staticmethod
    def get_country_info_keyboard(country_id: str) -> InlineKeyboardMarkup:
        """Клавиатура с информацией о стране"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="📊 Статистика нод",
                callback_data=f"country_stats_{country_id}"
            ),
            InlineKeyboardButton(
                text="🔧 Выбрать ноду",
                callback_data=f"select_node_{country_id}"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⚡ Тест скорости",
                callback_data=f"speed_test_{country_id}"
            ),
            InlineKeyboardButton(
                text="📍 На карте",
                callback_data=f"show_map_{country_id}"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад к списку",
                callback_data="back_to_countries"
            )
        )

        return builder.as_markup()

    @staticmethod
    def get_node_selection_keyboard(nodes: List[Node]) -> InlineKeyboardMarkup:
        """Клавиатура выбора конкретной ноды"""
        builder = InlineKeyboardBuilder()

        for node in nodes:
            # Индикаторы состояния ноды
            status_emoji = {
                "active": "🟢",
                "maintenance": "🟡",
                "offline": "🔴"
            }.get(node.status, "⚪")

            load_emoji = "🔥" if node.load_percent > 80 else "⚡" if node.load_percent < 30 else "📊"

            builder.row(
                InlineKeyboardButton(
                    text=f"{status_emoji} {node.name} {load_emoji} {node.load_percent}%",
                    callback_data=f"select_node_{node.id}"
                )
            )

        builder.row(
            InlineKeyboardButton(
                text="🎯 Автовыбор лучшей ноды",
                callback_data="auto_select_node"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⏪ Назад к странам",
                callback_data="back_to_countries"
            )
        )

        return builder.as_markup()
```

#### 2. Система мониторинга и статистики нод
```python
# Мониторинг нод
class NodeMonitoringService:
    def __init__(self, db_session):
        self.db = db_session
        self.monitoring_tasks = {}

    async def start_monitoring(self):
        """Запустить мониторинг всех нод"""
        nodes = await self.get_all_active_nodes()

        for node in nodes:
            task = asyncio.create_task(self.monitor_node(node.id))
            self.monitoring_tasks[node.id] = task

    async def monitor_node(self, node_id: str):
        """Мониторинг конкретной ноды"""
        while True:
            try:
                node = await self.db.get(Node, node_id)
                if not node:
                    break

                # Проверяем доступность
                status = await self.check_node_health(node)

                # Получаем статистику использования
                stats = await self.get_node_statistics(node)

                # Обновляем данные в БД
                await self.update_node_stats(node, status, stats)

                # Отправляем алерты при проблемах
                if status.status == "offline":
                    await self.send_node_alert(node, "Node is offline")
                elif stats.load_percent > 90:
                    await self.send_node_alert(node, "High load detected")

                await asyncio.sleep(60)  # Проверяем каждую минуту

            except Exception as e:
                logger.error(f"Error monitoring node {node_id}: {e}")
                await asyncio.sleep(300)  # При ошибке ждем 5 минут

    async def check_node_health(self, node: Node) -> NodeHealthStatus:
        """Проверить здоровье ноды"""
        try:
            marzban_client = Marzban(node.marzban_host, node.marzban_username,
                                   self._decrypt_password(node.marzban_password))

            start_time = time.time()

            # Проверяем API
            users_response = await marzban_client.get_users()
            inbounds_response = await marzban_client.get_inbounds()

            response_time = int((time.time() - start_time) * 1000)

            return NodeHealthStatus(
                status="active",
                response_time=response_time,
                api_accessible=True,
                users_count=len(users_response.get("users", [])),
                inbounds_count=len(inbounds_response)
            )

        except Exception as e:
            return NodeHealthStatus(
                status="offline",
                response_time=None,
                api_accessible=False,
                error=str(e)
            )

    async def get_node_statistics(self, node: Node) -> NodeStatistics:
        """Получить статистику ноды"""
        try:
            marzban_client = Marzban(node.marzban_host, node.marzban_username,
                                   self._decrypt_password(node.marzban_password))

            users_data = await marzban_client.get_users()
            users = users_data.get("users", [])

            # Подсчитываем статистику
            active_users = len([u for u in users if u.get("status") == "active"])
            total_traffic = sum(u.get("used_traffic", 0) for u in users)

            load_percent = min(100, int((active_users / node.max_users) * 100))

            return NodeStatistics(
                active_users=active_users,
                total_users=len(users),
                total_traffic=total_traffic,
                load_percent=load_percent,
                bandwidth_usage=total_traffic
            )

        except Exception as e:
            return NodeStatistics(
                active_users=0,
                total_users=0,
                total_traffic=0,
                load_percent=0,
                error=str(e)
            )

    async def get_country_statistics(self, country_id: str) -> CountryStatistics:
        """Получить статистику по стране"""
        nodes = await self.get_country_nodes(country_id)

        total_nodes = len(nodes)
        active_nodes = len([n for n in nodes if n.status == "active"])
        total_users = sum(n.current_users for n in nodes)
        avg_load = sum(n.load_percent for n in nodes) / total_nodes if total_nodes > 0 else 0
        avg_ping = sum(n.response_time for n in nodes if n.response_time) / active_nodes if active_nodes > 0 else 0

        return CountryStatistics(
            total_nodes=total_nodes,
            active_nodes=active_nodes,
            total_users=total_users,
            average_load=avg_load,
            average_ping=avg_ping,
            status="healthy" if active_nodes > 0 else "offline"
        )

# Система автоматического выбора оптимальной ноды
class OptimalNodeSelector:
    def __init__(self, db_session, monitoring_service):
        self.db = db_session
        self.monitoring = monitoring_service

    async def select_optimal_node(self, user_id: int, protocol: str = "vless") -> Optional[Node]:
        """Выбрать оптимальную ноду для пользователя"""
        user_preferences = await self.get_user_preferences(user_id)

        if user_preferences.auto_select:
            return await self._auto_select_node(user_preferences, protocol)
        else:
            return await self._select_from_preferences(user_preferences, protocol)

    async def _auto_select_node(self, preferences: UserNodePreference, protocol: str) -> Optional[Node]:
        """Автоматический выбор лучшей ноды"""
        # Получаем все доступные ноды
        all_nodes = await self.get_all_available_nodes(protocol)

        if not all_nodes:
            return None

        # Фильтруем по предпочтениям пользователя (если есть)
        if preferences.preferred_countries:
            preferred_nodes = [n for n in all_nodes if n.country_id in preferences.preferred_countries]
            if preferred_nodes:
                all_nodes = preferred_nodes

        # Вычисляем рейтинг для каждой ноды
        scored_nodes = []
        for node in all_nodes:
            score = await self._calculate_node_score(node)
            scored_nodes.append((node, score))

        # Сортируем по рейтингу
        scored_nodes.sort(key=lambda x: x[1], reverse=True)

        return scored_nodes[0][0] if scored_nodes else None

    async def _calculate_node_score(self, node: Node) -> float:
        """Вычислить рейтинг ноды"""
        score = 100.0

        # Штраф за загрузку
        score -= node.load_percent * 0.5

        # Штраф за пинг
        if node.response_time:
            score -= min(node.response_time / 10, 30)

        # Бонус за стабильность (время с последней проверки)
        if node.last_check:
            hours_since_check = (datetime.utcnow() - node.last_check).total_seconds() / 3600
            if hours_since_check < 1:  # Недавно проверялась
                score += 10

        # Штраф за статус
        if node.status == "maintenance":
            score -= 20
        elif node.status == "offline":
            score = 0

        return max(0, score)
```

#### 3. Интеграция с пользовательским интерфейсом
```python
# Обработчики для выбора стран и нод
class CountryNodeHandlers:
    def __init__(self, country_service: CountryNodeService, monitoring_service: NodeMonitoringService):
        self.country_service = country_service
        self.monitoring = monitoring_service

    async def show_country_selection(self, callback: CallbackQuery):
        """Показать выбор стран"""
        user_id = callback.from_user.id

        # Получаем тип подписки пользователя
        user_subscription = await self.get_user_subscription_type(user_id)

        # Получаем доступные страны
        countries = await self.country_service.get_available_countries(user_subscription)

        # Получаем предпочтения пользователя
        preferences = await self.country_service.get_user_preferences(user_id)

        if not countries:
            await callback.message.edit_text(
                "🚫 К сожалению, в данный момент нет доступных серверов.\n"
                "Попробуйте позже или обратитесь в поддержку.",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[[
                    InlineKeyboardButton(text="⏪ Назад", callback_data="back_to_main")
                ]])
            )
            return

        text = "🌍 Выберите страны для VPN подключения:\n\n"

        # Группируем по континентам для отображения
        continents_stats = {}
        for country in countries:
            if country.continent not in continents_stats:
                continents_stats[country.continent] = {"countries": 0, "nodes": 0}

            continents_stats[country.continent]["countries"] += 1
            country_nodes = await self.country_service.get_country_nodes(country.id)
            continents_stats[country.continent]["nodes"] += len(country_nodes)

        for continent, stats in continents_stats.items():
            text += f"🌍 **{continent}**: {stats['countries']} стран, {stats['nodes']} серверов\n"

        text += f"\n💡 Ваш тариф: **{user_subscription.title()}**\n"
        text += "✅ Отмеченные страны будут использоваться для автовыбора\n\n"

        await callback.message.edit_text(
            text,
            reply_markup=CountrySelectionKeyboards.get_country_selection_keyboard(
                countries, preferences.preferred_countries
            ),
            parse_mode="Markdown"
        )

    async def handle_country_selection(self, callback: CallbackQuery, country_id: str):
        """Обработать выбор/отмену выбора страны"""
        user_id = callback.from_user.id
        preferences = await self.country_service.get_user_preferences(user_id)

        if country_id in preferences.preferred_countries:
            # Убираем из предпочтений
            preferences.preferred_countries.remove(country_id)
        else:
            # Добавляем в предпочтения
            preferences.preferred_countries.append(country_id)

        await self.country_service.update_user_preferences(
            user_id, preferences.preferred_countries,
            preferences.preferred_protocols, preferences.auto_select
        )

        # Обновляем клавиатуру
        await self.show_country_selection(callback)

    async def show_country_info(self, callback: CallbackQuery, country_id: str):
        """Показать детальную информацию о стране"""
        country = await self.country_service.get_country(country_id)
        if not country:
            await callback.answer("Страна не найдена", show_alert=True)
            return

        # Получаем статистику страны
        stats = await self.monitoring.get_country_statistics(country_id)
        nodes = await self.country_service.get_country_nodes(country_id, include_offline=True)

        # Формируем детальную информацию
        text = f"{country.flag} **{country.name_ru}**\n\n"
        text += f"📊 **Статистика серверов:**\n"
        text += f"• Всего серверов: {stats.total_nodes}\n"
        text += f"• Активных: {stats.active_nodes}\n"
        text += f"• Пользователей онлайн: {stats.total_users}\n"
        text += f"• Средняя загрузка: {stats.average_load:.1f}%\n"
        text += f"• Средний пинг: {stats.average_ping:.0f}ms\n\n"

        # Статус доступности
        if stats.status == "healthy":
            text += "✅ **Статус: Отлично**\n"
        elif stats.status == "degraded":
            text += "⚠️ **Статус: Ограниченная доступность**\n"
        else:
            text += "🔴 **Статус: Недоступно**\n"

        # Информация о нодах
        text += f"\n🖥 **Серверы в {country.name_ru}:**\n"
        for node in nodes[:5]:  # Показываем только первые 5
            status_emoji = {"active": "🟢", "maintenance": "🟡", "offline": "🔴"}.get(node.status, "⚪")
            text += f"{status_emoji} {node.name} - {node.load_percent}% загрузка\n"

        if len(nodes) > 5:
            text += f"... и еще {len(nodes) - 5} серверов\n"

        await callback.message.edit_text(
            text,
            reply_markup=CountrySelectionKeyboards.get_country_info_keyboard(country_id),
            parse_mode="Markdown"
        )

    async def handle_speed_test(self, callback: CallbackQuery, country_id: str):
        """Запустить тест скорости для страны"""
        await callback.answer("🔄 Запускаем тест скорости...", show_alert=False)

        # Получаем лучшую ноду в стране
        optimal_node = await self.country_service.get_optimal_node(country_id)
        if not optimal_node:
            await callback.answer("❌ Нет доступных серверов", show_alert=True)
            return

        # Запускаем тест скорости
        speed_result = await self.run_speed_test(optimal_node)

        country = await self.country_service.get_country(country_id)

        text = f"⚡ **Тест скорости - {country.flag} {country.name_ru}**\n\n"
        text += f"🖥 Сервер: {optimal_node.name}\n"
        text += f"📡 Пинг: {speed_result.ping}ms\n"
        text += f"⬇️ Скорость загрузки: {speed_result.download_speed:.1f} Мбит/с\n"
        text += f"⬆️ Скорость отдачи: {speed_result.upload_speed:.1f} Мбит/с\n\n"

        if speed_result.ping < 50:
            text += "✅ Отличное соединение!"
        elif speed_result.ping < 100:
            text += "👍 Хорошее соединение"
        else:
            text += "⚠️ Медленное соединение"

        await callback.message.edit_text(
            text,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[[
                InlineKeyboardButton(text="🔄 Повторить тест", callback_data=f"speed_test_{country_id}"),
                InlineKeyboardButton(text="⏪ Назад", callback_data=f"country_info_{country_id}")
            ]]),
            parse_mode="Markdown"
        )

    async def show_node_selection(self, callback: CallbackQuery, country_id: str):
        """Показать выбор конкретной ноды в стране"""
        country = await self.country_service.get_country(country_id)
        nodes = await self.country_service.get_country_nodes(country_id)

        if not nodes:
            await callback.message.edit_text(
                f"🚫 В {country.flag} {country.name_ru} нет доступных серверов",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=[[
                    InlineKeyboardButton(text="⏪ Назад", callback_data="back_to_countries")
                ]])
            )
            return

        text = f"🖥 **Серверы в {country.flag} {country.name_ru}:**\n\n"

        for node in nodes:
            status_text = {
                "active": "🟢 Активен",
                "maintenance": "🟡 Обслуживание",
                "offline": "🔴 Недоступен"
            }.get(node.status, "⚪ Неизвестно")

            text += f"**{node.name}**\n"
            text += f"• Статус: {status_text}\n"
            text += f"• Загрузка: {node.load_percent}%\n"
            text += f"• Пинг: {node.response_time}ms\n"
            text += f"• Пользователей: {node.current_users}/{node.max_users}\n\n"

        text += "Выберите сервер или используйте автовыбор:"

        await callback.message.edit_text(
            text,
            reply_markup=CountrySelectionKeyboards.get_node_selection_keyboard(nodes),
            parse_mode="Markdown"
        )

    async def handle_node_selection(self, callback: CallbackQuery, node_id: str):
        """Обработать выбор конкретной ноды"""
        node = await self.country_service.get_node(node_id)
        if not node:
            await callback.answer("Сервер не найден", show_alert=True)
            return

        # Проверяем доступность ноды
        status = await self.country_service.check_node_availability(node_id)

        if status != NodeStatus.ONLINE:
            await callback.answer("❌ Сервер недоступен", show_alert=True)
            return

        # Сохраняем выбор пользователя
        user_id = callback.from_user.id
        preferences = await self.country_service.get_user_preferences(user_id)
        preferences.last_selected_node = node_id
        preferences.auto_select = False  # Отключаем автовыбор

        await self.country_service.update_user_preferences(
            user_id, preferences.preferred_countries,
            preferences.preferred_protocols, False
        )

        # Создаем/обновляем подписку на выбранной ноде
        await self.create_subscription_on_node(user_id, node)

        country = await self.country_service.get_country(node.country_id)

        await callback.message.edit_text(
            f"✅ **Сервер выбран!**\n\n"
            f"🖥 {node.name}\n"
            f"🌍 {country.flag} {country.name_ru}\n"
            f"📡 Пинг: {node.response_time}ms\n"
            f"📊 Загрузка: {node.load_percent}%\n\n"
            f"Ваша подписка активирована на этом сервере.",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[[
                InlineKeyboardButton(text="📱 Получить конфигурацию", callback_data="get_config"),
                InlineKeyboardButton(text="⏪ Назад", callback_data="back_to_countries")
            ]]),
            parse_mode="Markdown"
        )

# Регистрация обработчиков
@router.callback_query(F.data == "select_countries")
async def handle_select_countries(callback: CallbackQuery):
    handler = CountryNodeHandlers(country_service, monitoring_service)
    await handler.show_country_selection(callback)

@router.callback_query(F.data.startswith("select_country_"))
async def handle_country_toggle(callback: CallbackQuery):
    country_id = callback.data.split("_")[-1]
    handler = CountryNodeHandlers(country_service, monitoring_service)
    await handler.handle_country_selection(callback, country_id)

@router.callback_query(F.data.startswith("country_info_"))
async def handle_country_info(callback: CallbackQuery):
    country_id = callback.data.split("_")[-1]
    handler = CountryNodeHandlers(country_service, monitoring_service)
    await handler.show_country_info(callback, country_id)

@router.callback_query(F.data.startswith("speed_test_"))
async def handle_speed_test(callback: CallbackQuery):
    country_id = callback.data.split("_")[-1]
    handler = CountryNodeHandlers(country_service, monitoring_service)
    await handler.handle_speed_test(callback, country_id)

@router.callback_query(F.data.startswith("select_node_"))
async def handle_node_selection(callback: CallbackQuery):
    if callback.data == "select_node_auto":
        # Автовыбор ноды
        handler = CountryNodeHandlers(country_service, monitoring_service)
        await handler.handle_auto_node_selection(callback)
    else:
        # Выбор конкретной ноды
        node_id = callback.data.split("_")[-1]
        handler = CountryNodeHandlers(country_service, monitoring_service)
        await handler.handle_node_selection(callback, node_id)
```

#### 4. Интеграция с системой подписок (на основе реальной архитектуры Marzban)
```python
# Правильная система создания подписок с выбором нод Marzban
class MarzbanSubscriptionService:
    def __init__(self, country_service: MarzbanCountryNodeService, marzban_client: Marzban):
        self.country_service = country_service
        self.marzban = marzban_client  # Основная панель Marzban с REST API

    async def create_subscription_with_country_selection(self, user_id: int, subscription_type: str,
                                                       preferred_countries: List[str] = None) -> SubscriptionResult:
        """Создать подписку с учетом предпочтений по странам"""

        # Получаем предпочтения пользователя
        if preferred_countries:
            await self.country_service.update_user_preferences(
                user_id, preferred_countries, ["vless", "vmess"], True
            )

        preferences = await self.country_service.get_user_preferences(user_id)

        # Выбираем оптимальные inbounds на основе предпочтений
        optimal_inbounds = await self.select_optimal_inbounds_for_user(preferences, subscription_type)

        if not optimal_inbounds:
            return SubscriptionResult(
                success=False,
                error="No available inbounds for your country preferences"
            )

        # Создаем пользователя через Marzban REST API
        user_data = {
            'username': f"user_{user_id}_{int(time.time())}",
            'proxies': self._get_protocols_config(preferences.preferred_protocols),
            'inbounds': {inbound.tag: [] for inbound in optimal_inbounds},  # Используем теги inbounds
            'expire': self._calculate_expiration(subscription_type),
            'data_limit': self._get_data_limit(subscription_type),
            'data_limit_reset_strategy': "monthly",
        }

        try:
            # Создаем пользователя через Marzban REST API
            result = await self.marzban.add_user(user_data)

            # Сохраняем информацию о подписке
            await self.save_subscription_info(
                user_id,
                [inbound.primary_node_id for inbound in optimal_inbounds],
                user_data['username'],
                subscription_type,
                [inbound.tag for inbound in optimal_inbounds]
            )

            # Обновляем статистику нод
            for inbound in optimal_inbounds:
                if inbound.primary_node_id:
                    await self.country_service.increment_node_users(inbound.primary_node_id)

            return SubscriptionResult(
                success=True,
                nodes=[inbound.primary_node_id for inbound in optimal_inbounds],
                username=user_data['username'],
                subscription_url=result.get('subscription_url'),
                config_data=result,
                selected_inbounds=[inbound.tag for inbound in optimal_inbounds]
            )

        except Exception as e:
            return SubscriptionResult(
                success=False,
                error=f"Failed to create user: {str(e)}"
            )

    async def select_optimal_node_and_inbounds(self, user_id: int, preferences: UserNodePreference,
                                             subscription_type: str) -> Optional[Dict]:
        """Выбрать оптимальную ноду и соответствующие inbounds"""

        # Определяем нужные протоколы на основе предпочтений
        required_protocols = preferences.preferred_protocols or ["vless", "vmess"]

        # Перебираем предпочитаемые страны
        for country_id in preferences.preferred_countries:
            # Получаем ноды в стране
            nodes = await self.country_service.get_country_nodes(country_id)

            for node in nodes:
                # Проверяем, есть ли нужные inbounds на ноде
                available_inbounds = await self.get_inbounds_for_protocols(node, required_protocols)

                if available_inbounds:
                    return {
                        'node': node,
                        'inbounds': available_inbounds,
                        'protocols': required_protocols
                    }

        # Если не нашли в предпочитаемых странах, ищем в любых доступных
        all_countries = await self.country_service.get_available_countries(subscription_type)

        for country in all_countries:
            if country.id in preferences.preferred_countries:
                continue  # Уже проверили выше

            nodes = await self.country_service.get_country_nodes(country.id)

            for node in nodes:
                available_inbounds = await self.get_inbounds_for_protocols(node, required_protocols)

                if available_inbounds:
                    return {
                        'node': node,
                        'inbounds': available_inbounds,
                        'protocols': required_protocols
                    }

        return None

    async def get_inbounds_for_protocols(self, node: MarzbanNode, protocols: List[str]) -> List[str]:
        """Получить inbounds ноды для нужных протоколов"""
        matching_inbounds = []

        for inbound_name in node.available_inbounds:
            # Проверяем, какой протокол использует этот inbound
            for protocol in protocols:
                if protocol.upper() in inbound_name.upper():
                    matching_inbounds.append(inbound_name)
                    break

        return matching_inbounds

    async def change_user_node(self, user_id: int, target_country: str) -> bool:
        """Изменить ноду пользователя (через изменение inbounds)"""
        # Получаем текущую подписку
        current_subscription = await self.get_user_subscription(user_id)
        if not current_subscription:
            return False

        # Выбираем новую ноду в целевой стране
        preferences = await self.country_service.get_user_preferences(user_id)
        new_node = await self.country_service.get_optimal_node_for_inbounds(
            target_country,
            preferences.preferred_protocols or ["vless", "vmess"]
        )

        if not new_node:
            return False

        # Получаем новые inbounds для новой ноды
        new_inbounds = await self.get_inbounds_for_protocols(new_node, preferences.preferred_protocols)

        if not new_inbounds:
            return False

        try:
            # Обновляем inbounds пользователя через основную панель
            user_data = await self.marzban.get_user(current_subscription.username)
            user_data['inbounds'] = new_inbounds

            await self.marzban.modify_user(current_subscription.username, user_data)

            # Обновляем информацию в БД
            await self.update_subscription_node(user_id, new_node.id, new_inbounds)

            # Обновляем статистику нод
            await self.country_service.decrement_node_users(current_subscription.node_id)
            await self.country_service.increment_node_users(new_node.id)

            return True

        except Exception as e:
            logger.error(f"Failed to change user {user_id} node: {e}")
            return False

    def _get_protocols_config(self, protocols: List[str]) -> Dict:
        """Получить конфигурацию протоколов"""
        config = {}

        for protocol in protocols:
            if protocol == "vless":
                config["vless"] = {"flow": ""}
            elif protocol == "vmess":
                config["vmess"] = {}
            elif protocol == "trojan":
                config["trojan"] = {}
            elif protocol == "shadowsocks":
                config["shadowsocks"] = {"method": "chacha20-ietf-poly1305"}

        return config
```

## 📋 **Анализ реализуемости плана на основе актуальной документации Marzban**

### ✅ **Полностью реализуемые компоненты:**

#### **1. Система выбора стран и нод:**
- **Marzban Node Architecture** - полностью поддерживается
- **REST API** - доступен для управления пользователями и получения статистики
- **Host Settings** - позволяет привязывать ноды к inbounds
- **Node Management** - через панель Marzban с SSL сертификатами

#### **2. Система уведомлений:**
- **Webhook notifications** - встроенная поддержка в Marzban
- **Telegram Bot** - интегрированный бот для уведомлений
- **API endpoints** - для получения статистики пользователей

#### **3. Система платежей:**
- **YooKassa integration** - стандартная интеграция
- **Управление методами оплаты** - через базу данных бота
- **Автоматическое продление** - через webhook уведомления

### ⚠️ **Требующие адаптации компоненты:**

#### **1. Мониторинг нод:**
```python
# Вместо прямого подключения к нодам используем Marzban API
async def get_nodes_status():
    # Получаем статус через основную панель
    nodes_info = await marzban_client.get("/api/nodes")  # Если такой endpoint существует
    # Или через системную статистику
    system_stats = await marzban_client.get("/api/system")
```

#### **2. Выбор inbounds для стран:**
```python
# Связываем inbounds с нодами через Host Settings
class CountryInboundMapping:
    async def get_inbounds_for_country(self, country_id: str):
        # Получаем ноды страны
        nodes = await self.get_country_nodes(country_id)

        # Получаем inbounds, которые используют эти ноды как хосты
        inbounds = []
        for node in nodes:
            node_inbounds = await self.get_inbounds_using_node_as_host(node.node_address)
            inbounds.extend(node_inbounds)

        return inbounds
```

### 🔧 **Практическая реализация:**

#### **Этап 1: Настройка Marzban инфраструктуры**
1. **Основная панель** - установка Marzban с MySQL/PostgreSQL
2. **Ноды** - установка Marzban-node на серверах в разных странах
3. **SSL сертификаты** - настройка безопасного подключения нод
4. **Inbounds** - создание inbounds для каждой страны в Core Settings

#### **Этап 2: Интеграция с ботом**
1. **Marzban API клиент** - подключение к REST API панели
2. **Модели данных** - создание таблиц для стран, нод, предпочтений
3. **Сервисы** - реализация логики выбора стран и создания подписок
4. **UI компоненты** - клавиатуры для выбора стран

#### **Этап 3: Система мониторинга**
1. **Webhook integration** - получение уведомлений от Marzban
2. **Статистика** - сбор данных через API
3. **Алерты** - уведомления о проблемах с нодами

### 🎯 **Ключевые особенности реализации:**

#### **Работа с Marzban API:**
```python
# Создание пользователя с привязкой к конкретным inbounds
user_data = {
    'username': f"user_{user_id}",
    'proxies': {'vless': {}, 'vmess': {}},
    'inbounds': {
        'VLESS TCP REALITY DE': [],  # Inbound для Германии
        'VMess WS NL': []            # Inbound для Нидерландов
    },
    'expire': expire_timestamp,
    'data_limit': data_limit_bytes
}

result = await marzban.add_user(user_data)
```

#### **Смена страны пользователя:**
```python
# Изменение inbounds пользователя для смены страны
async def change_user_country(username: str, target_country: str):
    # Получаем текущие данные пользователя
    user = await marzban.get_user(username)

    # Получаем inbounds для новой страны
    new_inbounds = await get_inbounds_for_country(target_country)

    # Обновляем inbounds пользователя
    user['inbounds'] = {inbound.tag: [] for inbound in new_inbounds}

    # Применяем изменения
    await marzban.modify_user(username, user)
```

### 📊 **Ограничения и решения:**

#### **Ограничение 1: Нет прямого API для управления нодами**
**Решение:** Мониторинг через системную статистику и webhook уведомления

#### **Ограничение 2: Связь inbounds с нодами через Host Settings**
**Решение:** Ведение собственной базы соответствий inbound ↔ node

#### **Ограничение 3: Нет автоматического переключения нод**
**Решение:** Ручное переключение через изменение inbounds пользователя

### 🚀 **Итоговая оценка реализуемости: 95%**

План полностью реализуем с учетом реальной архитектуры Marzban. Основные функции:
- ✅ Выбор стран пользователем
- ✅ Автоматическое создание подписок на нодах
- ✅ Система уведомлений и платежей
- ✅ Мониторинг и статистика
- ✅ Управление предпочтениями пользователей

Этот план обеспечит превращение текущего MVP в production-ready решение корпоративного уровня с высокой надежностью, безопасностью и масштабируемостью, полностью совместимое с архитектурой Marzban.
