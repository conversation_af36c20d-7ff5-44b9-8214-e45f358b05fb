#!/usr/bin/env python3
"""
Простой скрипт для настройки нод через прямые SQL запросы.
"""

import os
import pymysql
import json

def get_db_connection():
    """Получает подключение к базе данных."""
    return pymysql.connect(
        host=os.getenv('DB_ADDRESS', 'db'),
        port=int(os.getenv('DB_PORT', '3306')),
        user=os.getenv('DB_USER', 'unveil_user'),
        password=os.getenv('DB_PASS', 'unveil_secure_password_2024'),
        database=os.getenv('DB_NAME', 'unveil_vpn_prod'),
        charset='utf8mb4'
    )

def setup_nodes():
    """Создает ноды в базе данных."""
    print("🖥️ Setting up Marzban nodes...")
    
    nodes_data = [
        {
            'id': 'germany-node-1',
            'name': 'Germany Frankfurt #1',
            'country_id': 'DE',
            'city': 'Frankfurt',
            'host': '************',
            'port': 12000,
            'api_port': 62050,
            'max_users': 1000,
            'current_users': 0,
            'status': 'connected',
            'is_active': 1,
            'cpu_usage': 15.0,
            'memory_usage': 45.0,
            'config': {
                'reality_destination': 'deb.debian.org:443',
                'reality_server_names': ['deb.debian.org'],
                'protocols': ['vless'],
                'haproxy_enabled': False,
                'description': 'VLESS TCP REALITY node in Germany'
            }
        }
    ]
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Проверяем существующие ноды
            cursor.execute("SELECT id FROM marzban_nodes")
            existing_nodes = {row[0] for row in cursor.fetchall()}
            
            # Добавляем новые ноды
            for node_data in nodes_data:
                node_id = node_data['id']
                if node_id in existing_nodes:
                    print(f"Node {node_data['name']} already exists")
                    continue
                
                cursor.execute("""
                    INSERT INTO marzban_nodes 
                    (id, name, country_id, city, host, port, api_port, max_users, current_users, 
                     status, is_active, cpu_usage, memory_usage, config)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    node_data['id'],
                    node_data['name'],
                    node_data['country_id'],
                    node_data['city'],
                    node_data['host'],
                    node_data['port'],
                    node_data['api_port'],
                    node_data['max_users'],
                    node_data['current_users'],
                    node_data['status'],
                    node_data['is_active'],
                    node_data['cpu_usage'],
                    node_data['memory_usage'],
                    json.dumps(node_data['config'])
                ))
                print(f"Created node: {node_data['name']}")
            
            connection.commit()
            print(f"✅ Nodes setup complete")
            
    except Exception as e:
        print(f"❌ Error setting up nodes: {e}")
        connection.rollback()
        raise
    finally:
        connection.close()

def setup_inbounds():
    """Создает inbounds для нод."""
    print("🔌 Setting up inbounds...")
    
    inbounds_data = [
        {
            'id': 'germany-node-1-vless-reality',
            'node_id': 'germany-node-1',
            'tag': 'VLESS_TCP_REALITY_GERMANY_NODE_1',
            'protocol': 'vless',
            'port': 12000,
            'listen': '127.0.0.1',
            'security': 'reality',
            'network': 'tcp',
            'is_active': 1,
            'config': {
                'reality_settings': {
                    'dest': 'deb.debian.org:443',
                    'server_names': ['deb.debian.org'],
                    'private_key': 'YOUR_REALITY_PRIVATE_KEY_HERE',
                    'short_ids': ['YOUR_SHORT_ID_HERE']
                },
                'tcp_settings': {
                    'accept_proxy_protocol': True
                },
                'sniffing': {
                    'enabled': True,
                    'dest_override': ['http', 'tls']
                }
            }
        }
    ]
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Проверяем существующие inbounds
            cursor.execute("SELECT id FROM marzban_inbounds")
            existing_inbounds = {row[0] for row in cursor.fetchall()}
            
            # Добавляем новые inbounds
            for inbound_data in inbounds_data:
                inbound_id = inbound_data['id']
                if inbound_id in existing_inbounds:
                    print(f"Inbound {inbound_data['tag']} already exists")
                    continue
                
                cursor.execute("""
                    INSERT INTO marzban_inbounds 
                    (id, node_id, tag, protocol, port, listen, security, network, is_active, config)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    inbound_data['id'],
                    inbound_data['node_id'],
                    inbound_data['tag'],
                    inbound_data['protocol'],
                    inbound_data['port'],
                    inbound_data['listen'],
                    inbound_data['security'],
                    inbound_data['network'],
                    inbound_data['is_active'],
                    json.dumps(inbound_data['config'])
                ))
                print(f"Created inbound: {inbound_data['tag']}")
            
            connection.commit()
            print(f"✅ Inbounds setup complete")
            
    except Exception as e:
        print(f"❌ Error setting up inbounds: {e}")
        connection.rollback()
        raise
    finally:
        connection.close()

def show_summary():
    """Показывает сводку созданных данных."""
    print("\n" + "="*60)
    print("🎉 MARZBAN INFRASTRUCTURE SETUP COMPLETE!")
    print("="*60)
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # Страны
            cursor.execute("SELECT COUNT(*) FROM countries")
            countries_count = cursor.fetchone()[0]
            
            # Ноды
            cursor.execute("SELECT COUNT(*) FROM marzban_nodes")
            nodes_count = cursor.fetchone()[0]
            
            # Inbounds
            cursor.execute("SELECT COUNT(*) FROM marzban_inbounds")
            inbounds_count = cursor.fetchone()[0]
            
            print(f"📊 Summary:")
            print(f"  - Countries: {countries_count}")
            print(f"  - Nodes: {nodes_count}")
            print(f"  - Inbounds: {inbounds_count}")
            
            # Детали нод
            cursor.execute("SELECT name, host, port, status FROM marzban_nodes")
            nodes = cursor.fetchall()
            print(f"\n🖥️ Nodes:")
            for name, host, port, status in nodes:
                status_emoji = "🟢" if status == "connected" else "🔴"
                print(f"  - {status_emoji} {name} ({host}:{port})")
            
    except Exception as e:
        print(f"❌ Error showing summary: {e}")
    finally:
        connection.close()

def main():
    """Главная функция."""
    print("🚀 Starting Marzban infrastructure setup...")
    
    try:
        setup_nodes()
        setup_inbounds()
        show_summary()
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
