# Упрощенное руководство по развертыванию системы Marzban (только VLESS TCP REALITY)

## Обзор упрощенной архитектуры

Данное руководство описывает развертывание **упрощенной** системы Marzban со следующей архитектурой:
- **Основная панель Marzban** (главный сервер управления)
- **Несколько нод** (подключенных серверов для распределения нагрузки)
- **Упрощенная конфигурация "все на одном порту"** с использованием HAProxy
- **ТОЛЬКО VLESS TCP REALITY inbound** на порту 443 с destination `deb.debian.org`
- **SSL/TLS сертификаты** через Let's Encrypt и Cloudflare
- **Исключены все остальные протоколы**: VMess, Trojan, WebSocket

## ⚠️ Важное примечание

Это **упрощенная версия** полного руководства, которая:
- Использует **ТОЛЬКО протокол VLESS TCP REALITY**
- Убирает все TLS fallback конфигурации
- Исключает WebSocket, VMess, Trojan протоколы
- Упрощает конфигурацию HAProxy
- Изменяет destination с `tradingview.com` на `deb.debian.org`

Если вам нужна полная конфигурация с множественными протоколами, обратитесь к полной версии руководства.

## Требования к системе

### Технические требования
- **ОС**: Debian 12 на всех серверах
- **Домен**: Управляется через Cloudflare
- **Минимальные ресурсы**: 1 CPU, 1GB RAM, 10GB SSD
- **Сеть**: Публичные IP адреса для всех серверов

### Предварительные условия
- Доступ к серверам с правами root
- Домен, настроенный в Cloudflare
- Базовые знания Linux и Docker

---

## 1. Подготовка инфраструктуры

### 1.1 Настройка DNS записей в Cloudflare

1. **Войдите в панель Cloudflare** и выберите ваш домен

2. **Создайте следующие DNS записи**:
   ```
   Type: A
   Name: panel
   Content: [IP_ОСНОВНОГО_СЕРВЕРА]
   Proxy status: DNS only (серый облако)
   TTL: Auto

   Type: A
   Name: node1
   Content: [IP_НОДЫ_1]
   Proxy status: DNS only (серый облако)
   TTL: Auto

   Type: A
   Name: node2
   Content: [IP_НОДЫ_2]
   Proxy status: DNS only (серый облако)
   TTL: Auto
   ```

3. **Настройте дополнительные записи для REALITY**:
   ```
   Type: A
   Name: reality
   Content: [IP_ОСНОВНОГО_СЕРВЕРА]
   Proxy status: DNS only (серый облако)
   TTL: Auto
   ```

### 1.2 Подготовка серверов

Выполните следующие команды на **всех серверах** (основной панели и нодах):

```bash
# Обновление системы
apt update && apt upgrade -y

# Установка необходимых пакетов
apt install -y curl wget git nano htop ufw socat

# Настройка временной зоны
timedatectl set-timezone Europe/Moscow

# Проверка статуса системы
systemctl status systemd-timesyncd
```

### 1.3 Настройка файрвола

**На основном сервере (панель)**:
```bash
# Сброс правил UFW
ufw --force reset

# Разрешить SSH
ufw allow 22/tcp

# Разрешить HTTP и HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Разрешить порт панели Marzban
ufw allow 8000/tcp

# Разрешить порты для нод (если используются)
ufw allow 62050:62060/tcp

# Включить файрвол
ufw --force enable

# Проверить статус
ufw status verbose
```

**На серверах нод**:
```bash
# Сброс правил UFW
ufw --force reset

# Разрешить SSH
ufw allow 22/tcp

# Разрешить HTTPS для REALITY конфигураций (единственный внешний порт)
ufw allow 443/tcp

# Разрешить порты для подключения к панели
ufw allow 62050:62060/tcp

# НЕ НУЖНЫ дополнительные порты - используем только 443 через HAProxy

# Включить файрвол
ufw --force enable

# Проверить статус
ufw status verbose
```

---

## 2. Установка основной панели Marzban

### 2.1 Установка Docker

```bash
# Установка Docker
curl -fsSL https://get.docker.com | sh

# Добавление пользователя в группу docker
usermod -aG docker $USER

# Перезагрузка для применения изменений группы
newgrp docker

# Проверка установки
docker --version
docker compose version
```

### 2.2 Быстрая установка Marzban

```bash
# Установка Marzban с помощью официального скрипта
sudo bash -c "$(curl -sL https://github.com/Gozargah/Marzban-scripts/raw/master/marzban.sh)" @ install

# Дождитесь завершения установки и остановите логи (Ctrl+C)
```

### 2.3 Создание администратора

```bash
# Создание sudo администратора
marzban cli admin create --sudo

# Введите желаемые username и password
```

### 2.4 Базовая конфигурация

Отредактируйте файл конфигурации:
```bash
nano /opt/marzban/.env
```

Добавьте/измените следующие параметры:
```env
# Основные настройки
UVICORN_HOST = "127.0.0.1"
UVICORN_PORT = 10000

# URL для подписок (замените на ваш домен)
XRAY_SUBSCRIPTION_URL_PREFIX = "https://panel.snaplyze.me"

# Настройки для "все на одном порту"
XRAY_FALLBACKS_INBOUND_TAG = "TROJAN_FALLBACK_INBOUND"

# Telegram бот (опционально)
# TELEGRAM_API_TOKEN = "YOUR_BOT_TOKEN"
# TELEGRAM_ADMIN_ID = YOUR_TELEGRAM_ID

# Включение документации API
DOCS = true
```

### 2.5 Перезапуск Marzban

```bash
# Перезапуск для применения настроек
marzban restart

# Проверка статуса
marzban status

# Просмотр логов
marzban logs
```

---

## 3. Настройка SSL сертификатов

### 3.1 Установка acme.sh

```bash
# Установка acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>

# Перезагрузка bash для применения изменений
source ~/.bashrc
```

### 3.2 Получение SSL сертификата

**Метод 1: Standalone (рекомендуется)**
```bash
# Остановка Marzban временно
marzban stop

# Установка переменной домена
export DOMAIN=panel.snaplyze.me

# Создание директории для сертификатов
mkdir -p /var/lib/marzban/certs

# Получение сертификата
~/.acme.sh/acme.sh \
  --issue --force --standalone -d "$DOMAIN" \
  --fullchain-file "/var/lib/marzban/certs/fullchain.pem" \
  --key-file "/var/lib/marzban/certs/key.pem"

# Запуск Marzban
marzban start
```

**Метод 2: Cloudflare DNS (если standalone не работает)**
```bash
# Получение API токена Cloudflare
export CF_Token="your_cloudflare_api_token"
export CF_Account_ID="your_cloudflare_account_id"

# Получение сертификата через DNS
~/.acme.sh/acme.sh \
  --issue --dns dns_cf -d panel.snaplyze.me \
  --fullchain-file "/var/lib/marzban/certs/fullchain.pem" \
  --key-file "/var/lib/marzban/certs/key.pem"
```

### 3.3 Настройка автообновления сертификатов

```bash
# Создание скрипта для перезапуска Marzban после обновления сертификата
cat > /opt/marzban/renew-cert.sh << 'EOF'
#!/bin/bash
echo "Certificate renewed, restarting Marzban..."
/usr/local/bin/marzban restart
EOF

# Делаем скрипт исполняемым
chmod +x /opt/marzban/renew-cert.sh

# Настройка автообновления
~/.acme.sh/acme.sh --install-cert -d panel.snaplyze.me \
  --fullchain-file /var/lib/marzban/certs/fullchain.pem \
  --key-file /var/lib/marzban/certs/key.pem \
  --reloadcmd "/opt/marzban/renew-cert.sh"
```

---

## 4. Установка и настройка HAProxy для "все на одном порту"

### 4.1 Понимание архитектуры "все на одном порту" (только REALITY)

Цель упрощенной конфигурации "все на одном порту" - направить весь трафик (панель Marzban и REALITY конфигурации) через один порт (443). HAProxy анализирует SNI (Server Name Indication) входящих SSL/TLS соединений и направляет трафик на соответствующие локальные порты.

**Упрощенная схема работы (только REALITY)**:
```
Клиент → Порт 443 (HAProxy) → Анализ SNI → Локальные порты:
                                         ├─ panel.yourdomain.com → 127.0.0.1:10000 (Marzban Panel)
                                         └─ deb.debian.org → 127.0.0.1:12000 (VLESS TCP REALITY)
```

**Важно**: В этой упрощенной конфигурации мы используем ТОЛЬКО протокол VLESS TCP REALITY, исключив все остальные протоколы (VMess, Trojan, WebSocket).

### 4.2 Установка HAProxy

```bash
# Обновление пакетов
apt update

# Установка HAProxy
apt install -y haproxy

# Проверка версии
haproxy -v

# Проверка статуса
systemctl status haproxy
```

### 4.3 Упрощенная конфигурация HAProxy (только для REALITY)

**Важно**: Перед настройкой HAProxy убедитесь, что:
- Marzban настроен на локальный порт (127.0.0.1:10000)
- Файрвол разрешает порт 443
- Используется ТОЛЬКО протокол VLESS TCP REALITY

```bash
# Создание резервной копии оригинального файла
cp /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.backup

# Редактирование конфигурации HAProxy
nano /etc/haproxy/haproxy.cfg
```

**Замените содержимое файла на следующую упрощенную конфигурацию**:

```haproxy
global
    log stdout local0
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    daemon

defaults
    mode tcp
    log global
    option tcplog
    option dontlognull
    timeout connect 5000
    timeout client 50000
    timeout server 50000

# Frontend для приема всего трафика на порту 443
listen front
    bind *:443
    mode tcp

    # Задержка для анализа SNI
    tcp-request inspect-delay 5s
    tcp-request content accept if { req_ssl_hello_type 1 }

    # Маршрутизация по SNI (замените yourdomain.com на ваш домен)
    use_backend panel if { req.ssl_sni -i end panel.yourdomain.com }
    use_backend reality if { req.ssl_sni -i end deb.debian.org }

    # По умолчанию направляем на панель
    default_backend panel

# Backend для панели Marzban
backend panel
    mode tcp
    server srv1 127.0.0.1:10000

# Backend для REALITY конфигураций
backend reality
    mode tcp
    server srv1 127.0.0.1:12000 send-proxy-v2

# Статистика HAProxy (опционально)
listen stats
    bind 127.0.0.1:8404
    mode http
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
```

### 4.4 Настройка логирования HAProxy

```bash
# Настройка rsyslog для HAProxy
cat >> /etc/rsyslog.conf << 'EOF'

# HAProxy logging
$ModLoad imudp
$UDPServerRun 514
$UDPServerAddress 127.0.0.1
local0.*    /var/log/haproxy.log
& stop
EOF

# Перезапуск rsyslog
systemctl restart rsyslog

# Создание файла логов
touch /var/log/haproxy.log
chown syslog:adm /var/log/haproxy.log
```

### 4.5 Проверка и запуск HAProxy

```bash
# Проверка синтаксиса конфигурации
haproxy -c -f /etc/haproxy/haproxy.cfg

# Если проверка прошла успешно, перезапускаем HAProxy
systemctl restart haproxy

# Включение автозапуска
systemctl enable haproxy

# Проверка статуса
systemctl status haproxy

# Проверка портов
netstat -tlnp | grep :443

# Просмотр логов HAProxy
tail -f /var/log/haproxy.log

# Просмотр статистики (опционально)
curl http://127.0.0.1:8404/stats
```

### 4.6 Тестирование HAProxy

```bash
# Тест подключения к различным SNI
echo | openssl s_client -connect yourdomain.com:443 -servername panel.yourdomain.com 2>/dev/null | grep "subject="
echo | openssl s_client -connect yourdomain.com:443 -servername deb.debian.org 2>/dev/null | grep "subject="

# Проверка маршрутизации
curl -v --resolve panel.yourdomain.com:443:YOUR_SERVER_IP https://panel.yourdomain.com:443

# Мониторинг соединений в реальном времени
watch -n 1 'netstat -an | grep :443 | grep ESTABLISHED | wc -l'
```

### 4.7 Настройка Marzban для работы с HAProxy (упрощенная)

После настройки HAProxy необходимо настроить Marzban для работы на локальном порту:

```bash
# Редактирование конфигурации Marzban
nano /opt/marzban/.env
```

Добавьте или измените следующие параметры:

```env
# Настройки для работы с HAProxy
UVICORN_HOST = "127.0.0.1"
UVICORN_PORT = 10000

# URL для подписок (замените на ваш домен)
XRAY_SUBSCRIPTION_URL_PREFIX = "https://panel.yourdomain.com"

# Включение документации API
DOCS = true

# НЕ ИСПОЛЬЗУЙТЕ fallback настройки для упрощенной конфигурации
# XRAY_FALLBACKS_INBOUND_TAG не нужен для REALITY-only конфигурации
```

```bash
# Перезапуск Marzban для применения настроек
marzban restart

# Проверка статуса
marzban status

# Проверка логов
marzban logs
```

### 4.8 Важные замечания по конфигурации

**Обязательные требования для работы "все на одном порту"**:

1. **Настройка inbound'ов на локальные адреса**:
   - REALITY inbound должен слушать `127.0.0.1:12000`
   - TLS Fallback inbound должен слушать `127.0.0.1:11000`
   - Все WebSocket inbound'ы должны использовать `@socket-name`

2. **Использование acceptProxyProtocol**:
   - Для REALITY inbound'ов добавьте `"acceptProxyProtocol": true`
   - Это необходимо для получения реального IP клиента

3. **Настройка Host Settings**:
   - Все хосты должны использовать порт 443
   - SNI должны соответствовать настройкам HAProxy

4. **Проблемы и решения**:
   ```bash
   # Если HAProxy не запускается, проверьте синтаксис
   haproxy -c -f /etc/haproxy/haproxy.cfg

   # Если соединения не проходят, проверьте логи
   tail -f /var/log/haproxy.log

   # Проверка занятости портов
   netstat -tlnp | grep -E ':(443|10000|11000|12000)'

   # Если нужно остановить HAProxy для отладки
   systemctl stop haproxy

   # Запуск HAProxy в режиме отладки
   haproxy -f /etc/haproxy/haproxy.cfg -d
   ```

---

## 5. Установка и подключение нод (с HAProxy на порту 443)

### 5.1 Установка Marzban-node на серверах нод

Выполните на каждом сервере ноды:

```bash
# Установка Docker (если не установлен)
curl -fsSL https://get.docker.com | sh

# Установка HAProxy на ноде (так же как на основном сервере)
apt update
apt install -y haproxy

# Установка Marzban-node
sudo bash -c "$(curl -sL https://github.com/Gozargah/Marzban-scripts/raw/master/marzban-node.sh)" @ install

# Для установки с кастомным именем (для множественных нод)
# sudo bash -c "$(curl -sL https://github.com/Gozargah/Marzban-scripts/raw/master/marzban-node.sh)" @ install --name marzban-node2
```

### 5.2 Настройка HAProxy на ноде

Настройте HAProxy на каждой ноде точно так же, как на основном сервере:

```bash
# Создание резервной копии
cp /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.backup

# Редактирование конфигурации HAProxy на ноде
nano /etc/haproxy/haproxy.cfg
```

**Конфигурация HAProxy для ноды** (замените `nodeX.yourdomain.com` на домен вашей ноды):

```haproxy
global
    log stdout local0
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    daemon

defaults
    mode tcp
    log global
    option tcplog
    option dontlognull
    timeout connect 5000
    timeout client 50000
    timeout server 50000

# Frontend для приема всего трафика на порту 443
listen front
    bind *:443
    mode tcp

    # Задержка для анализа SNI
    tcp-request inspect-delay 5s
    tcp-request content accept if { req_ssl_hello_type 1 }

    # Маршрутизация по SNI для ноды
    use_backend reality if { req.ssl_sni -i end node1.yourdomain.com }

    # По умолчанию направляем на REALITY
    default_backend reality

# Backend для REALITY конфигураций на ноде
backend reality
    mode tcp
    server srv1 127.0.0.1:12000 send-proxy-v2

# Статистика HAProxy (опционально)
listen stats
    bind 127.0.0.1:8404
    mode http
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
```

```bash
# Проверка конфигурации HAProxy
haproxy -c -f /etc/haproxy/haproxy.cfg

# Запуск HAProxy на ноде
systemctl restart haproxy
systemctl enable haproxy
```

### 5.3 Конфигурация Marzban-node

```bash
# Переход в директорию Marzban-node
cd ~/Marzban-node

# Редактирование docker-compose.yml
nano docker-compose.yml
```

Настройте файл следующим образом:

```yaml
services:
  marzban-node:
    image: gozargah/marzban-node:latest
    restart: always
    network_mode: host

    environment:
      SSL_CLIENT_CERT_FILE: "/var/lib/marzban-node/ssl_client_cert.pem"
      SERVICE_PROTOCOL: "rest"
      SERVICE_PORT: 62050
      XRAY_API_PORT: 62051

    volumes:
      - /var/lib/marzban-node:/var/lib/marzban-node
```

### 5.4 Получение сертификата от панели

1. **В панели Marzban**:
   - Перейдите в `Node Settings`
   - Нажмите `Add New Marzban Node`
   - Нажмите `Show Certificate`
   - Скопируйте содержимое сертификата

2. **На сервере ноды**:
```bash
# Создание директории для сертификатов
mkdir -p /var/lib/marzban-node

# Создание файла сертификата
nano /var/lib/marzban-node/ssl_client_cert.pem

# Вставьте скопированный сертификат и сохраните файл
```

### 5.5 Запуск Marzban-node

```bash
# Запуск ноды
cd ~/Marzban-node
docker compose up -d

# Проверка статуса
docker compose ps

# Просмотр логов
docker compose logs -f

# Проверка HAProxy на ноде
systemctl status haproxy

# Проверка портов на ноде
netstat -tlnp | grep -E ':(443|12000|62050|62051)'
```

### 5.6 Добавление ноды в панель

В панели Marzban заполните следующие поля:

- **Name**: `Node-1-REALITY` (или любое удобное имя)
- **Address**: IP адрес сервера ноды
- **Port**: `62050`
- **API Port**: `62051`
- **Usage Ratio**: `1` (коэффициент использования)
- **Add this node as a new host**: ✅ (отметить)

Нажмите `Add Node` для сохранения.

### 5.7 Настройка REALITY inbound на ноде

После добавления ноды в панель, настройте REALITY inbound на ноде:

1. **В панели Marzban перейдите в `Core Settings`**
2. **Добавьте REALITY inbound для ноды**:

```json
{
  "tag": "VLESS_TCP_REALITY_NODE1",
  "listen": "127.0.0.1",
  "port": 12000,
  "protocol": "vless",
  "settings": {
    "clients": [],
    "decryption": "none"
  },
  "streamSettings": {
    "network": "tcp",
    "tcpSettings": {
      "acceptProxyProtocol": true
    },
    "security": "reality",
    "realitySettings": {
      "show": false,
      "dest": "deb.debian.org:443",
      "xver": 0,
      "serverNames": [
        "deb.debian.org"
      ],
      "privateKey": "ВАШИ_ПРИВАТНЫЙ_КЛЮЧ_REALITY_ДЛЯ_НОДЫ",
      "shortIds": [
        "ВАШИ_SHORT_ID_ДЛЯ_НОДЫ"
      ]
    }
  },
  "sniffing": {
    "enabled": true,
    "destOverride": [
      "http",
      "tls"
    ]
  }
}
```

**Важно**: Каждая нода должна иметь свои уникальные REALITY ключи!

---

## 6. Конфигурация VLESS TCP REALITY (упрощенная)

### 6.1 Настройка единственного REALITY inbound

В панели Marzban перейдите в `Core Settings` и добавьте следующий inbound:

**ВАЖНО**: Этот inbound должен слушать на порту 12000, который настроен в HAProxy как backend для REALITY трафика.

```json
{
  "tag": "VLESS_TCP_REALITY",
  "listen": "127.0.0.1",
  "port": 12000,
  "protocol": "vless",
  "settings": {
    "clients": [],
    "decryption": "none"
  },
  "streamSettings": {
    "network": "tcp",
    "tcpSettings": {
      "acceptProxyProtocol": true
    },
    "security": "reality",
    "realitySettings": {
      "show": false,
      "dest": "deb.debian.org:443",
      "xver": 0,
      "serverNames": [
        "deb.debian.org"
      ],
      "privateKey": "ВАШИ_ПРИВАТНЫЙ_КЛЮЧ_REALITY",
      "shortIds": [
        "ВАШИ_SHORT_ID"
      ]
    }
  },
  "sniffing": {
    "enabled": true,
    "destOverride": [
      "http",
      "tls"
    ]
  }
}
```

**Изменения в конфигурации**:
- Изменен destination с `tradingview.com` на `deb.debian.org`
- Обновлен serverNames соответственно
- Убраны все остальные протоколы и inbound'ы

### 6.2 Генерация ключей REALITY

```bash
# Генерация ключей REALITY
docker run --rm gozargah/marzban-node:latest xray x25519

# Сохраните приватный и публичный ключи
# Приватный ключ используется в конфигурации inbound
# Публичный ключ используется в клиентских конфигурациях
```

### 6.3 Проверка конфигурации REALITY

**Проверка конфигурации**:
```bash
# Проверка порта REALITY inbound
netstat -tlnp | grep :12000

# Проверка логов Xray
marzban logs | grep -E "(REALITY|inbound|listen)"

# Тест подключения к локальному порту REALITY
telnet 127.0.0.1 12000
```

**Типичные ошибки и решения**:

1. **Ошибка "bind: address already in use"**:
   ```bash
   # Найти процесс, использующий порт
   lsof -i :12000

   # Остановить Marzban и перезапустить
   marzban stop
   marzban start
   ```

2. **HAProxy не может подключиться к REALITY backend**:
   ```bash
   # Проверить статус backend'ов в HAProxy
   echo "show stat" | socat stdio /run/haproxy/admin.sock

   # Проверить доступность порта REALITY
   nc -zv 127.0.0.1 12000
   ```

3. **REALITY не работает**:
   - Убедитесь, что `acceptProxyProtocol: true` установлен
   - Проверьте, что HAProxy использует `send-proxy-v2` для REALITY backend
   - Проверьте правильность REALITY ключей и destination (deb.debian.org)

---

## 7. Настройка Host Settings (упрощенная для REALITY)

### 7.1 Добавление хостов только для REALITY

В панели Marzban перейдите в `Host Settings` и добавьте следующие хосты:

**Для REALITY inbound (основной)**:
- **Remark**: `REALITY-Main`
- **Address**: `deb.debian.org`
- **Port**: `443`
- **SNI**: `deb.debian.org`
- **Host**: `deb.debian.org`
- **Inbound Tags**: `VLESS_TCP_REALITY`

**Для нод (с HAProxy на порту 443)**:
- **Remark**: `Node-1-REALITY`
- **Address**: `node1.yourdomain.com`
- **Port**: `443` (через HAProxy)
- **SNI**: `deb.debian.org`
- **Host**: `deb.debian.org`
- **Inbound Tags**: `VLESS_TCP_REALITY_NODE1`

**Для дополнительных нод**:
- **Remark**: `Node-2-REALITY`
- **Address**: `node2.yourdomain.com`
- **Port**: `443` (через HAProxy)
- **SNI**: `deb.debian.org`
- **Host**: `deb.debian.org`
- **Inbound Tags**: `VLESS_TCP_REALITY_NODE2`

### 7.2 Важные замечания по Host Settings (только REALITY)

**Обязательные требования для REALITY**:

1. **Все хосты должны использовать порт 443** - это единственный порт, который принимает HAProxy
2. **SNI должны соответствовать настройкам HAProxy** - именно по ним происходит маршрутизация
3. **Для REALITY хостов**:
   - Address: ваш домен для REALITY (например, `deb.debian.org`)
   - SNI: destination сайт (`deb.debian.org`)
   - Host: тот же destination сайт (`deb.debian.org`)
4. **Inbound Tags**: только `VLESS_TCP_REALITY`

**Проверка Host Settings**:
```bash
# Тест REALITY хоста основного сервера
curl -v --resolve deb.debian.org:443:YOUR_MAIN_SERVER_IP \
  --connect-to deb.debian.org:443:deb.debian.org:443 \
  https://deb.debian.org:443

# Тест REALITY хоста ноды
curl -v --resolve node1.yourdomain.com:443:YOUR_NODE1_IP \
  --connect-to node1.yourdomain.com:443:deb.debian.org:443 \
  https://node1.yourdomain.com:443

# Проверка доступности destination
curl -v https://deb.debian.org:443

# Проверка HAProxy на всех серверах
curl -s http://YOUR_MAIN_SERVER_IP:8404/stats
curl -s http://YOUR_NODE1_IP:8404/stats
```

---

## 8. Финальная настройка и тестирование (упрощенная для REALITY)

### 8.1 Обновление конфигурации Marzban

```bash
# Обновление переменных окружения
nano /opt/marzban/.env
```

Убедитесь, что следующие параметры установлены:
```env
# Настройки для работы с HAProxy (упрощенная конфигурация)
UVICORN_HOST = "127.0.0.1"
UVICORN_PORT = 10000

# URL для подписок (используйте ваш домен)
XRAY_SUBSCRIPTION_URL_PREFIX = "https://panel.yourdomain.com"

# Включение документации API
DOCS = true

# НЕ ИСПОЛЬЗУЙТЕ SSL настройки в Marzban при работе с HAProxy
# UVICORN_SSL_CERTFILE и UVICORN_SSL_KEYFILE должны быть закомментированы

# НЕ ИСПОЛЬЗУЙТЕ fallback настройки для REALITY-only конфигурации
# XRAY_FALLBACKS_INBOUND_TAG не нужен
```

**ВАЖНО**: При использовании HAProxy с REALITY-only конфигурацией, Marzban работает только как HTTP сервер на локальном порту.

### 8.2 Перезапуск всех сервисов

```bash
# Перезапуск Marzban
marzban restart

# Перезапуск HAProxy
systemctl restart haproxy

# Проверка статуса всех сервисов
marzban status
systemctl status haproxy
systemctl status docker
```

### 8.3 Создание тестового пользователя (только REALITY)

1. **Войдите в панель Marzban**: `http://127.0.0.1:10000/dashboard/` (локально) или через HAProxy
2. **Создайте нового пользователя**:
   - Username: `test-user-reality`
   - Data Limit: `10 GB`
   - Expire Date: `+30 days`
   - Protocols: Выберите ТОЛЬКО `VLESS`

3. **Настройте протокол**:
   - **VLESS**: Выберите `VLESS_TCP_REALITY` inbound
   - **НЕ добавляйте** другие протоколы (VMess, Trojan, WebSocket)

### 8.4 Тестирование подключений (упрощенная для REALITY)

**Проверка HAProxy и маршрутизации**:
```bash
# Проверка статуса HAProxy
systemctl status haproxy

# Проверка статистики HAProxy
curl -s http://127.0.0.1:8404/stats

# Проверка backend'ов HAProxy (только panel и reality)
echo "show stat" | socat stdio /run/haproxy/admin.sock

# Мониторинг соединений в реальном времени
watch -n 1 'echo "show stat" | socat stdio /run/haproxy/admin.sock | grep -E "(panel|reality)"'
```

**Проверка локальных портов (упрощенная)**:
```bash
# Проверка доступности локальных портов
nc -zv 127.0.0.1 10000  # Marzban Panel
nc -zv 127.0.0.1 12000  # REALITY

# Проверка процессов на портах
netstat -tlnp | grep -E ':(10000|12000)'
```

**Проверка REALITY конфигурации**:
```bash
# Тест подключения к REALITY через HAProxy
curl -v --connect-timeout 10 --resolve deb.debian.org:443:YOUR_SERVER_IP \
  https://deb.debian.org:443

# Проверка SNI маршрутизации для REALITY
echo | openssl s_client -connect YOUR_SERVER_IP:443 -servername deb.debian.org 2>/dev/null | grep -E "(subject|issuer)"

# Проверка destination сайта
curl -v https://deb.debian.org:443
```

**Проверка панели Marzban**:
```bash
# Тест прямого подключения к Marzban (в обход HAProxy)
curl -v http://127.0.0.1:10000/docs

# Проверка доступности панели через HAProxy (если настроен TLS для панели)
curl -v --resolve panel.yourdomain.com:443:YOUR_SERVER_IP \
  https://panel.yourdomain.com:443/dashboard/
```

**Диагностика проблем (упрощенная)**:
```bash
# Если соединения не проходят, проверьте логи
tail -f /var/log/haproxy.log
marzban logs | tail -20

# Проверка DNS разрешения
nslookup deb.debian.org
nslookup deb.debian.org

# Проверка файрвола
ufw status verbose
iptables -L -n | grep 443

# Проверка REALITY inbound
marzban logs | grep REALITY
```

---

## 9. Мониторинг и обслуживание

### 9.1 Настройка логирования

**Логи Marzban**:
```bash
# Просмотр логов в реальном времени
marzban logs

# Просмотр логов с фильтрацией
marzban logs | grep ERROR

# Сохранение логов в файл
marzban logs > /var/log/marzban.log
```

**Логи HAProxy**:
```bash
# Настройка логирования HAProxy
echo '$ModLoad imudp' >> /etc/rsyslog.conf
echo '$UDPServerRun 514' >> /etc/rsyslog.conf
echo '$UDPServerAddress 127.0.0.1' >> /etc/rsyslog.conf
echo 'local0.*    /var/log/haproxy.log' >> /etc/rsyslog.conf

# Перезапуск rsyslog
systemctl restart rsyslog

# Просмотр логов HAProxy
tail -f /var/log/haproxy.log
```

### 9.2 Автоматическое резервное копирование

Создайте скрипт для автоматического резервного копирования:

```bash
# Создание скрипта резервного копирования
cat > /opt/marzban/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/marzban/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="marzban_backup_$DATE.tar.gz"

# Создание директории для резервных копий
mkdir -p $BACKUP_DIR

# Остановка Marzban
marzban stop

# Создание резервной копии
tar -czf $BACKUP_DIR/$BACKUP_FILE \
  /opt/marzban \
  /var/lib/marzban \
  /etc/haproxy/haproxy.cfg

# Запуск Marzban
marzban start

# Удаление старых резервных копий (старше 7 дней)
find $BACKUP_DIR -name "marzban_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/$BACKUP_FILE"
EOF

# Делаем скрипт исполняемым
chmod +x /opt/marzban/backup.sh

# Добавление в crontab (ежедневно в 2:00)
echo "0 2 * * * /opt/marzban/backup.sh" | crontab -
```

### 9.3 Мониторинг производительности

**Создание скрипта мониторинга**:
```bash
cat > /opt/marzban/monitor.sh << 'EOF'
#!/bin/bash

echo "=== Marzban System Monitor ==="
echo "Date: $(date)"
echo ""

echo "=== System Resources ==="
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free | grep Mem | awk '{printf("%.2f%%", $3/$2 * 100.0)}')"
echo "Disk Usage: $(df -h / | awk 'NR==2{printf "%s", $5}')"
echo ""

echo "=== Service Status ==="
echo "Marzban: $(systemctl is-active docker && echo "Running" || echo "Stopped")"
echo "HAProxy: $(systemctl is-active haproxy)"
echo "UFW: $(ufw status | head -1)"
echo ""

echo "=== Network Connections ==="
echo "Active connections on port 443: $(netstat -an | grep :443 | grep ESTABLISHED | wc -l)"
echo "Active connections on port 8000: $(netstat -an | grep :8000 | grep ESTABLISHED | wc -l)"
echo ""

echo "=== Docker Containers ==="
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
EOF

chmod +x /opt/marzban/monitor.sh

# Запуск мониторинга
/opt/marzban/monitor.sh
```

---

## 10. Интеграция с VPN ботом

### 10.1 Настройка API доступа

В файле `/opt/marzban/.env` добавьте:
```env
# Включение API документации
DOCS = true

# Настройки для внешнего доступа к API
UVICORN_HOST = "0.0.0.0"

# Настройки CORS (если необходимо)
# CORS_ORIGINS = "https://snaplyze.me"
```

### 10.2 Создание API пользователя для бота

```bash
# Создание API пользователя
marzban cli admin create --username api-bot

# Получение токена доступа через API
curl -X POST "https://panel.snaplyze.me:8000/api/admin/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=api-bot&password=YOUR_PASSWORD"
```

### 10.3 Тестирование API интеграции

```bash
# Получение списка пользователей
curl -X GET "https://panel.snaplyze.me:8000/api/users" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Создание нового пользователя
curl -X POST "https://panel.snaplyze.me:8000/api/user" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "bot-user-001",
    "data_limit": 10737418240,
    "expire": null,
    "proxies": {
      "vless": {
        "id": "auto"
      }
    },
    "inbounds": {
      "vless": ["VLESS_TCP_REALITY"]
    }
  }'
```

---

## 11. Устранение неполадок

### 11.1 Общие проблемы и решения

**Проблема**: Marzban не запускается
```bash
# Проверка логов Docker
docker logs marzban-marzban-1

# Проверка конфигурации
marzban status

# Перезапуск с очисткой
marzban down
marzban up -d
```

**Проблема**: HAProxy не работает
```bash
# Проверка конфигурации
haproxy -c -f /etc/haproxy/haproxy.cfg

# Проверка портов
netstat -tlnp | grep :443

# Перезапуск с отладкой
systemctl stop haproxy
haproxy -f /etc/haproxy/haproxy.cfg -d
```

**Проблема**: SSL сертификат не работает
```bash
# Проверка сертификата
openssl x509 -in /var/lib/marzban/certs/fullchain.pem -text -noout

# Проверка срока действия
openssl x509 -in /var/lib/marzban/certs/fullchain.pem -noout -dates

# Принудительное обновление
~/.acme.sh/acme.sh --renew -d panel.snaplyze.me --force
```

### 11.2 Команды для диагностики

```bash
# Проверка всех сервисов
systemctl status marzban haproxy docker ufw

# Проверка сетевых подключений
ss -tlnp | grep -E ':(443|8000|62050)'

# Проверка логов системы
journalctl -xe

# Проверка использования ресурсов
htop
df -h
free -h

# Тест DNS разрешения
nslookup panel.snaplyze.me
dig panel.snaplyze.me

# Проверка файрвола
ufw status verbose
iptables -L -n
```

### 11.3 Восстановление из резервной копии

```bash
# Остановка сервисов
marzban stop
systemctl stop haproxy

# Восстановление из резервной копии
tar -xzf /opt/marzban/backups/marzban_backup_YYYYMMDD_HHMMSS.tar.gz -C /

# Перезапуск сервисов
systemctl start haproxy
marzban start

# Проверка статуса
marzban status
systemctl status haproxy
```

---

## 12. Рекомендации по безопасности

### 12.1 Усиление безопасности сервера

```bash
# Изменение SSH порта
sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config
systemctl restart sshd

# Отключение root логина по SSH
sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# Настройка fail2ban
apt install -y fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

### 12.2 Регулярные обновления

```bash
# Создание скрипта обновления
cat > /opt/marzban/update.sh << 'EOF'
#!/bin/bash

echo "Updating system packages..."
apt update && apt upgrade -y

echo "Updating Marzban..."
marzban update

echo "Updating Marzban nodes..."
# Выполните на каждой ноде:
# marzban-node update

echo "Update completed!"
EOF

chmod +x /opt/marzban/update.sh

# Добавление в crontab (еженедельно)
echo "0 3 * * 0 /opt/marzban/update.sh" | crontab -
```

### 12.3 Мониторинг безопасности

```bash
# Мониторинг подозрительной активности
cat > /opt/marzban/security-monitor.sh << 'EOF'
#!/bin/bash

# Проверка неудачных попыток входа
echo "=== Failed Login Attempts ==="
grep "Failed password" /var/log/auth.log | tail -10

# Проверка активных соединений
echo "=== Active Connections ==="
netstat -an | grep :443 | grep ESTABLISHED | wc -l

# Проверка использования ресурсов
echo "=== Resource Usage ==="
top -bn1 | head -20

# Проверка логов HAProxy на подозрительную активность
echo "=== HAProxy Suspicious Activity ==="
grep -E "(40[0-9]|50[0-9])" /var/log/haproxy.log | tail -10
EOF

chmod +x /opt/marzban/security-monitor.sh
```

---

## Заключение

Данное руководство предоставляет **упрощенную инструкцию** по развертыванию системы Marzban с архитектурой "основная панель + ноды" и конфигурацией "все на одном порту" **только для протокола VLESS TCP REALITY**.

### Ключевые достижения упрощенной конфигурации:
- ✅ Установлена и настроена основная панель Marzban
- ✅ Настроены SSL/TLS сертификаты через Let's Encrypt
- ✅ Реализована упрощенная конфигурация "все на одном порту" с HAProxy
- ✅ Установлены и подключены ноды Marzban
- ✅ Настроен **ТОЛЬКО VLESS TCP REALITY** inbound с destination `deb.debian.org`
- ✅ Убраны все лишние протоколы (VMess, Trojan, WebSocket)
- ✅ Упрощена конфигурация HAProxy (только 2 backend'а)
- ✅ Подготовлена интеграция с VPN ботом
- ✅ Настроен мониторинг и резервное копирование

### Преимущества упрощенной конфигурации:
- **Простота настройки** - меньше компонентов для настройки
- **Надежность** - меньше точек отказа
- **Производительность** - отсутствие fallback механизмов
- **Безопасность** - использование только современного протокола REALITY
- **Совместимость** - работает с любыми клиентами, поддерживающими VLESS

### Следующие шаги:
1. Интеграция с существующим VPN ботом через API (только VLESS)
2. Настройка дополнительных нод по мере необходимости
3. Мониторинг производительности REALITY соединений
4. Регулярное обслуживание и обновления

**Упрощенная система готова к продуктивному использованию!** 🚀

### Важные замечания:
- Используется **ТОЛЬКО** протокол VLESS TCP REALITY
- Destination изменен с `tradingview.com` на `deb.debian.org`
- Убраны все TLS fallback конфигурации
- HAProxy настроен только для маршрутизации REALITY трафика
- **Все ноды также используют HAProxy на порту 443** для единообразия
- Каждая нода имеет свои уникальные REALITY ключи
- Конфигурация максимально упрощена для легкого понимания и обслуживания

### Архитектура с нодами:
```
Клиенты → Порт 443 → HAProxy (на каждом сервере) → REALITY inbound (порт 12000)
├─ Основной сервер: panel.yourdomain.com:443 → 127.0.0.1:12000
├─ Нода 1: node1.yourdomain.com:443 → 127.0.0.1:12000
└─ Нода 2: node2.yourdomain.com:443 → 127.0.0.1:12000
```

### Преимущества единообразной архитектуры:
- **Консистентность**: все серверы работают одинаково
- **Простота управления**: одинаковая конфигурация на всех серверах
- **Масштабируемость**: легко добавлять новые ноды
- **Безопасность**: только порт 443 открыт на всех серверах