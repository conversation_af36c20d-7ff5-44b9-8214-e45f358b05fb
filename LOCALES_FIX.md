# 🔧 Исправление проблемы с правами доступа к файлам локализации

## 🔍 Диагностированная проблема

**Корневая причина**: Контейнер работает под пользователем `appuser`, но не может записывать в примонтированную директорию `locales` из-за несоответствия прав доступа между хост-системой и контейнером.

### Симптомы:
- `PermissionError: [Errno 13] Permission denied: 'locales/ru/LC_MESSAGES/bot.mo'`
- Контейнер перезапускается в цикле
- Компиляция Babel не может создать .mo файлы

## ✅ Решение 1: Компиляция на этапе сборки (Рекомендуемое)

### Что изменено:

1. **Dockerfile**: 
   - Локализация копируется и компилируется на этапе сборки
   - Убрана компиляция из ENTRYPOINT
   - Файлы .mo создаются как root, затем права передаются appuser

2. **docker-compose.yml**:
   - Убрано монтирование `./locales:/app/locales`
   - Локализация теперь встроена в образ

### Преимущества:
- ✅ Нет проблем с правами доступа
- ✅ Быстрый запуск контейнера
- ✅ Безопасность (appuser не может изменять системные файлы)
- ✅ Консистентность между окружениями

### Недостатки:
- ❌ Нужно пересобирать образ для изменения локализации

## 🔄 Решение 2: Runtime компиляция с правильными правами

Если нужна возможность изменения локализации без пересборки:

### 1. Использовать альтернативный Dockerfile:
```bash
# Переименовать текущий Dockerfile
mv Dockerfile Dockerfile.build-time

# Использовать runtime версию
mv Dockerfile.runtime-locales Dockerfile
```

### 2. Исправить права доступа на хосте:
```bash
# Запустить скрипт исправления прав
./fix-permissions.sh
```

### 3. Обновить docker-compose.yml:
```yaml
volumes:
  - "./goods.json:/app/goods.json"
  - "./locales:/app/locales"  # Вернуть монтирование
```

## 🚀 Применение исправления

### Для Решения 1 (Рекомендуемое):

```bash
# 1. Остановить контейнеры
docker compose down

# 2. Пересобрать с исправлениями
docker compose build --no-cache

# 3. Запустить заново
docker compose up -d

# 4. Проверить логи
docker compose logs -f bot
```

### Для Решения 2 (Runtime компиляция):

```bash
# 1. Остановить контейнеры
docker compose down

# 2. Исправить права доступа
./fix-permissions.sh

# 3. Использовать runtime Dockerfile
mv Dockerfile Dockerfile.build-time
mv Dockerfile.runtime-locales Dockerfile

# 4. Вернуть монтирование locales в docker-compose.yml
# (раскомментировать строку с ./locales:/app/locales)

# 5. Пересобрать и запустить
docker compose build --no-cache
docker compose up -d
```

## 📋 Ожидаемые результаты

### ✅ Успешные логи:
```
bot-1  | 2025-06-03 15:55:39,147 - __main__ - INFO - ✅ MySQL database is ready! (attempt 1/30)
bot-1  | 2025-06-03 15:55:39,148 - __main__ - INFO - 🎉 Database is ready! Proceeding with application startup...
bot-1  | INFO  [alembic.runtime.migration] Context impl MySQLImpl.
bot-1  | INFO  [alembic.runtime.migration] Will assume non-transactional DDL.
bot-1  | INFO  [alembic.runtime.migration] Running upgrade  -> abc123, Initial migration
bot-1  | 2025-06-03 15:55:42,123 - __main__ - INFO - Starting VPN bot...
bot-1  | 2025-06-03 15:55:42,456 - __main__ - INFO - VPN bot started successfully
```

### ✅ Отсутствие ошибок:
- Нет `PermissionError` при компиляции локализации
- Нет циклических перезапусков контейнера
- Успешный запуск Alembic миграций
- Запуск основного приложения

## 🔧 Дополнительная диагностика

### Проверка прав доступа:
```bash
# В контейнере
docker compose exec bot ls -la locales/*/LC_MESSAGES/

# На хосте
ls -la locales/*/LC_MESSAGES/
```

### Проверка пользователя в контейнере:
```bash
docker compose exec bot whoami
docker compose exec bot id
```

### Тестирование компиляции:
```bash
# Ручная компиляция в контейнере
docker compose exec bot pybabel compile -d locales -D bot
```

## 🎯 Рекомендации

1. **Используйте Решение 1** для production окружений
2. **Используйте Решение 2** только для разработки, если нужно часто изменять локализацию
3. **Всегда проверяйте права доступа** при монтировании volumes
4. **Используйте фиксированные UID/GID** для предсказуемого поведения

## 🔄 Откат изменений

Если что-то пошло не так:

```bash
# Остановить контейнеры
docker compose down

# Восстановить оригинальные файлы
git checkout HEAD -- Dockerfile docker-compose.yml

# Пересобрать
docker compose build --no-cache
docker compose up -d
```

## 📞 Troubleshooting

### Если проблема сохраняется:

1. **Проверьте SELinux/AppArmor**:
```bash
# Отключить SELinux временно
sudo setenforce 0
```

2. **Проверьте Docker версию**:
```bash
docker --version
docker compose version
```

3. **Очистите Docker кэш**:
```bash
docker system prune -a
```

4. **Проверьте файловую систему**:
```bash
df -h
mount | grep locales
```
