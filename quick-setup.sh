#!/bin/bash

# =============================================================================
# 🚀 QUICK SETUP SCRIPT FOR MARZBAN VPN BOT
# =============================================================================
# Автоматическая настройка и тестирование VPN бота для Marzban инфраструктуры
# =============================================================================

set -e  # Выход при любой ошибке

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Функции для цветного вывода
print_header() {
    echo -e "\n${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}\n"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

print_step() {
    echo -e "${CYAN}🔄 $1${NC}"
}

# Проверка зависимостей
check_dependencies() {
    print_header "ПРОВЕРКА ЗАВИСИМОСТЕЙ"
    
    # Проверка Docker
    if command -v docker &> /dev/null; then
        print_success "Docker установлен: $(docker --version)"
    else
        print_error "Docker не установлен. Установите Docker и повторите попытку."
        exit 1
    fi
    
    # Проверка Docker Compose
    if command -v docker compose &> /dev/null; then
        print_success "Docker Compose установлен: $(docker compose --version)"
    else
        print_error "Docker Compose не установлен. Установите Docker Compose и повторите попытку."
        exit 1
    fi
    
    # Проверка Python
    if command -v python3 &> /dev/null; then
        print_success "Python3 установлен: $(python3 --version)"
    else
        print_error "Python3 не установлен. Установите Python3 и повторите попытку."
        exit 1
    fi
    
    # Проверка curl
    if command -v curl &> /dev/null; then
        print_success "curl установлен"
    else
        print_error "curl не установлен. Установите curl и повторите попытку."
        exit 1
    fi
}

# Проверка .env файла
check_env_file() {
    print_header "ПРОВЕРКА КОНФИГУРАЦИИ"
    
    if [ ! -f ".env" ]; then
        print_error ".env файл не найден!"
        print_info "Создайте .env файл на основе примера в MARZBAN_BOT_SETUP_GUIDE.md"
        exit 1
    fi
    
    print_success ".env файл найден"
    
    # Проверка критических переменных
    critical_vars=("BOT_TOKEN" "PANEL_HOST" "PANEL_USER" "PANEL_PASS" "DB_NAME" "DB_USER" "DB_PASS")
    
    for var in "${critical_vars[@]}"; do
        if grep -q "^${var}=" .env && [ -n "$(grep "^${var}=" .env | cut -d'=' -f2)" ]; then
            print_success "$var настроен"
        else
            print_error "$var не настроен в .env файле"
            exit 1
        fi
    done
    
    # Проверка ключей шифрования
    if grep -q "your_32_character_encryption_key_here_change_this" .env; then
        print_warning "ENCRYPTION_KEY использует значение по умолчанию. Рекомендуется изменить!"
    fi
    
    if grep -q "your_unique_salt_here_change_this" .env; then
        print_warning "ENCRYPTION_SALT использует значение по умолчанию. Рекомендуется изменить!"
    fi
}

# Генерация ключей шифрования
generate_encryption_keys() {
    print_header "ГЕНЕРАЦИЯ КЛЮЧЕЙ ШИФРОВАНИЯ"
    
    if grep -q "your_32_character_encryption_key_here_change_this" .env; then
        print_step "Генерация нового ключа шифрования..."
        new_key=$(python3 -c "import secrets; print(secrets.token_urlsafe(32)[:32])")
        sed -i "s/your_32_character_encryption_key_here_change_this/$new_key/" .env
        print_success "Ключ шифрования обновлен"
    fi
    
    if grep -q "your_unique_salt_here_change_this" .env; then
        print_step "Генерация новой соли..."
        new_salt=$(python3 -c "import secrets; print(secrets.token_urlsafe(16)[:16])")
        sed -i "s/your_unique_salt_here_change_this/$new_salt/" .env
        print_success "Соль обновлена"
    fi
}

# Сборка и запуск контейнеров
build_and_start() {
    print_header "СБОРКА И ЗАПУСК КОНТЕЙНЕРОВ"
    
    print_step "Остановка существующих контейнеров..."
    docker compose down || true
    
    print_step "Сборка образов..."
    docker compose build
    
    print_step "Запуск контейнеров..."
    docker compose up -d
    
    print_step "Ожидание готовности базы данных..."
    sleep 30
    
    # Проверка статуса контейнеров
    if docker compose ps | grep -q "Up"; then
        print_success "Контейнеры запущены успешно"
    else
        print_error "Ошибка запуска контейнеров"
        docker compose logs
        exit 1
    fi
}

# Настройка базы данных
setup_database() {
    print_header "НАСТРОЙКА БАЗЫ ДАННЫХ"
    
    print_step "Проверка подключения к базе данных..."
    if docker compose exec -T bot python3 test-db-connection.py; then
        print_success "Подключение к базе данных успешно"
    else
        print_error "Ошибка подключения к базе данных"
        exit 1
    fi
    
    print_step "Применение миграций..."
    docker compose exec -T bot python3 -c "
import asyncio
from db.migrations import MigrationManager
from db.database import get_session

async def apply_migrations():
    async with get_session() as session:
        manager = MigrationManager(session)
        await manager.apply_all_migrations()
        print('Migrations applied successfully')

asyncio.run(apply_migrations())
" || print_warning "Миграции уже применены или произошла ошибка"
}

# Настройка стран и нод
setup_nodes() {
    print_header "НАСТРОЙКА СТРАН И НОД"
    
    print_step "Создание стран и нод..."
    if docker compose exec -T bot python3 scripts/setup_marzban_nodes.py; then
        print_success "Страны и ноды настроены успешно"
    else
        print_warning "Ошибка настройки стран и нод (возможно, уже настроены)"
    fi
}

# Тестирование Marzban API
test_marzban_api() {
    print_header "ТЕСТИРОВАНИЕ MARZBAN API"
    
    print_step "Проверка подключения к Marzban API..."
    docker compose exec -T bot python3 -c "
import asyncio
from utils.marzban_api import panel

async def test_api():
    try:
        token = await panel.get_token()
        print(f'✅ Marzban API connection successful')
        print(f'Token: {token[:20]}...')
        
        users = await panel.get_users(limit=5)
        print(f'✅ Users retrieved: {len(users.get(\"users\", []))}')
        
        nodes = await panel.get_nodes()
        print(f'✅ Nodes retrieved: {len(nodes)}')
        
    except Exception as e:
        print(f'❌ Marzban API error: {e}')
        exit(1)

asyncio.run(test_api())
"
}

# Комплексное тестирование
run_integration_tests() {
    print_header "КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ"
    
    print_step "Запуск интеграционных тестов..."
    if docker compose exec -T bot python3 scripts/test_marzban_integration.py; then
        print_success "Все тесты пройдены успешно!"
    else
        print_error "Некоторые тесты не прошли. Проверьте логи выше."
        return 1
    fi
}

# Проверка health checks
check_health() {
    print_header "ПРОВЕРКА HEALTH CHECKS"
    
    print_step "Ожидание готовности сервисов..."
    sleep 10
    
    # Проверка основного health check
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        print_success "Health check: OK"
    else
        print_error "Health check: FAIL"
    fi
    
    # Проверка готовности
    if curl -s http://localhost:8000/health/ready | grep -q "ready"; then
        print_success "Ready check: OK"
    else
        print_error "Ready check: FAIL"
    fi
    
    # Проверка жизнеспособности
    if curl -s http://localhost:8000/health/live | grep -q "alive"; then
        print_success "Live check: OK"
    else
        print_error "Live check: FAIL"
    fi
}

# Вывод информации о запуске
show_startup_info() {
    print_header "ИНФОРМАЦИЯ О ЗАПУСКЕ"
    
    echo -e "${GREEN}🎉 VPN бот успешно настроен и запущен!${NC}\n"
    
    echo -e "${BLUE}📊 Полезные ссылки:${NC}"
    echo -e "  • Health Check: ${CYAN}http://localhost:8000/health${NC}"
    echo -e "  • Metrics: ${CYAN}http://localhost:8000/metrics${NC}"
    echo -e "  • Alerts Dashboard: ${CYAN}http://localhost:8000/alerts${NC}"
    echo -e "  • Database Status: ${CYAN}http://localhost:8000/api/db/status${NC}"
    
    echo -e "\n${BLUE}🔧 Полезные команды:${NC}"
    echo -e "  • Просмотр логов: ${CYAN}docker compose logs -f bot${NC}"
    echo -e "  • Перезапуск: ${CYAN}docker compose restart bot${NC}"
    echo -e "  • Остановка: ${CYAN}docker compose down${NC}"
    echo -e "  • Обновление: ${CYAN}docker compose up --build -d${NC}"
    
    echo -e "\n${BLUE}📱 Telegram бот:${NC}"
    BOT_TOKEN=$(grep "^BOT_TOKEN=" .env | cut -d'=' -f2)
    if [ -n "$BOT_TOKEN" ]; then
        echo -e "  • Найдите вашего бота в Telegram и отправьте ${CYAN}/start${NC}"
        echo -e "  • Токен бота: ${CYAN}${BOT_TOKEN:0:20}...${NC}"
    fi
    
    echo -e "\n${YELLOW}⚠️ Следующие шаги:${NC}"
    echo -e "  1. Обновите REALITY ключи в базе данных"
    echo -e "  2. Настройте платежные системы (YooKassa, Cryptomus)"
    echo -e "  3. Добавьте дополнительные ноды"
    echo -e "  4. Настройте мониторинг и алерты"
    echo -e "  5. Протестируйте создание пользователей"
}

# Главная функция
main() {
    print_header "🚀 MARZBAN VPN BOT QUICK SETUP"
    
    # Проверяем аргументы командной строки
    case "${1:-}" in
        "test-only")
            print_info "Режим: только тестирование"
            run_integration_tests
            exit $?
            ;;
        "setup-nodes")
            print_info "Режим: только настройка нод"
            setup_nodes
            exit $?
            ;;
        "health-check")
            print_info "Режим: только проверка здоровья"
            check_health
            exit $?
            ;;
    esac
    
    # Полная настройка
    check_dependencies
    check_env_file
    generate_encryption_keys
    build_and_start
    setup_database
    setup_nodes
    test_marzban_api
    
    # Тестирование (опционально)
    if [ "${SKIP_TESTS:-}" != "true" ]; then
        if ! run_integration_tests; then
            print_warning "Тесты не прошли, но бот может работать. Проверьте конфигурацию."
        fi
    fi
    
    check_health
    show_startup_info
    
    print_success "Настройка завершена успешно! 🎉"
}

# Обработка сигналов
trap 'print_error "Настройка прервана пользователем"; exit 1' INT TERM

# Запуск
main "$@"
